{"permissions": {"allow": ["mcp__serena__create_text_file", "mcp__serena__activate_project", "<PERSON><PERSON>(python:*)", "Bash(pip cache:*)", "<PERSON><PERSON>(pip uninstall:*)", "Bash(pip install:*)", "Bash(find:*)", "<PERSON><PERSON>(source:*)", "Read(//home/<USER>/miniconda3/envs/**)", "Read(//system/conda/miniconda3/**)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(cat:*)", "Bash(./scripts/copilot-api.sh:*)", "<PERSON><PERSON>(poetry run:*)", "<PERSON><PERSON>(poetry install:*)", "<PERSON><PERSON>(poetry lock:*)", "Bash(npm run lint)", "<PERSON><PERSON>(poetry check:*)", "mcp__serena__list_dir", "mcp__serena__read_file", "mcp__serena__replace_symbol_body", "mcp__serena__find_file", "mcp__serena__replace_regex", "Bash(npm install:*)"], "deny": [], "ask": []}}