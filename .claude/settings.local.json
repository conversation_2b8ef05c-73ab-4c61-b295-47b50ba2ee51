{"permissions": {"allow": ["mcp__serena__create_text_file", "mcp__serena__activate_project", "<PERSON><PERSON>(python:*)", "Bash(pip cache:*)", "<PERSON><PERSON>(pip uninstall:*)", "Bash(pip install:*)", "Bash(find:*)", "<PERSON><PERSON>(source:*)", "Read(//home/<USER>/miniconda3/envs/**)", "Read(//system/conda/miniconda3/**)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(cat:*)", "Bash(./scripts/copilot-api.sh:*)", "<PERSON><PERSON>(poetry run:*)", "<PERSON><PERSON>(poetry install:*)", "<PERSON><PERSON>(poetry lock:*)", "Bash(npm run lint)", "<PERSON><PERSON>(poetry check:*)"], "deny": [], "ask": []}}