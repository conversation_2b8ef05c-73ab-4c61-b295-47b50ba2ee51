# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Open Multi-Agent Canvas is a Next.js application with a Python backend that provides a multi-agent chat interface for restaurant intelligence, research, and general-purpose tasks through MCP (Model Context Protocol) servers. Built with CopilotKit, LangGraph, and LangChain.

## Architecture

### Frontend (Next.js)
- **Structure**: Next.js 15 app router with TypeScript
- **Framework**: React 19 with CopilotKit for agent integration
- **UI**: TailwindCSS with Radix UI components and Lucide icons
- **State**: React Query for data fetching, local storage for MCP configurations
- **Key Components**:
  - `Canvas`: Main layout with agent status and MCP configuration
  - `ChatWindow`: Shared chat interface for all agents
  - Agent components: Restaurant Intelligence, Research, and MCP agents with specialized UI
  - `MCPConfigModal`: Configuration interface for MCP server connections

### Backend (Python)
- **Framework**: LangGraph for agent workflows with Poetry dependency management
- **Agent**: MCP Agent using langchain-mcp-adapters for tool integration
- **MCP Integration**: Supports both stdio (local commands) and SSE (remote servers) connections
- **Default Tools**: Built-in math server and restaurant intelligence server for demonstration

### Agent System
Three specialized agents with distinct capabilities:
1. **Restaurant Intelligence Agent**: Business intelligence for restaurant operations and analytics
2. **Research Agent**: AI-powered research with source tracking and report generation
3. **MCP Agent**: General-purpose agent with configurable MCP server tools

## Commands

### Development Workflow
```bash
# Setup and install dependencies
make install                    # Install all dependencies (frontend + agent)
npm run install                 # Alternative using package.json root scripts

# Development
npm run dev                     # Run all services in development mode with parallel logging
npm run dev:frontend            # Frontend development mode (port 3000)
npm run dev:agent               # Start agent backend (port 8123)
npm run dev:copilot-api         # Start Copilot API server (port 4141)
make run-frontend               # Run frontend only (production)
make run-agent                  # Start agent backend only

# Production
npm run build                   # Build frontend for production
npm run start                   # Run frontend in production mode
make run                        # Run both frontend and agent

# Maintenance
npm run lint                    # Run ESLint on frontend code
```

### MCP Server Configuration
The application supports two types of MCP server connections:
- **stdio**: Local command execution (e.g., Python scripts)
- **sse**: Remote MCP-compatible servers via Server-Sent Events

Configure via the "MCP Servers" button in the UI or by modifying local storage.

### Backend Configuration
```bash
# Environment setup
./setup.sh                      # Interactive setup script
./config/setup-env.sh           # Setup environment files
./config/validate.sh            # Validate configuration

# Choose your backend (updates agent/.env)
./config/load-config.sh copilot # Configure for GitHub Copilot API (default)
./config/load-config.sh openai  # Configure for traditional OpenAI API
```

### Copilot API Integration
```bash
# Prerequisites: Set GITHUB_TOKEN environment variable
export GITHUB_TOKEN=your_github_token_here

# Copilot API management
./scripts/copilot-api.sh start  # Start Copilot API on port 4141
./scripts/copilot-api.sh stop   # Stop Copilot API server
./scripts/copilot-api.sh status # Check Copilot API status
npm run dev:copilot-api         # Alternative way to start Copilot API
```

## Key Files and Patterns

### Frontend State Management
- **Agent State**: Each agent maintains separate state through `useCoAgent` hook
- **MCP Configuration**: Stored in localStorage under `MCP_STORAGE_KEY`
- **Available Agents**: Enumerated in `src/lib/available-agents.ts`

### Backend Agent Structure
- **Agent Definition**: `agent/mcp-agent/agent.py` contains the main workflow graph
- **State**: `AgentState` inherits from `CopilotKitState` with optional `mcp_config`
- **Tools**: Dynamic tool loading based on MCP server configuration
- **MCP Client**: Uses `MultiServerMCPClient` for connecting to stdio and SSE servers
- **Available Tools**: Built-in math server (`agent/math_server.py`) and restaurant intelligence server (`agent/restaurant_intelligence_server.py`)

### Environment Configuration
- **Frontend**: Requires `NEXT_PUBLIC_CPK_PUBLIC_API_KEY` from Copilot Cloud
- **Agent**: Requires `OPENAI_API_KEY` or `GITHUB_TOKEN` and optional `LANGSMITH_API_KEY`
- **Copilot API**: Requires `GITHUB_TOKEN` for authentication

## Ports and Services

- **Frontend**: http://localhost:3000
- **Agent API**: http://localhost:8123 (LangGraph dev server)
- **Copilot API**: http://localhost:4141 (when using GitHub Copilot backend)

## Development Notes

- Frontend uses npm for package management (not pnpm)
- Backend uses Poetry for Python dependency management
- Agent backend runs on LangGraph dev server (port 8123)
- All agents share the same chat interface but maintain separate state
- MCP configurations are persisted in browser localStorage
- The system supports running with either OpenAI API or GitHub Copilot API as the backend
- Default backend is GitHub Copilot API (claude-sonnet-4 model)
- Root package.json provides unified scripts for concurrent development with `concurrently`

## Testing and Code Quality

### Frontend Testing
```bash
cd frontend && npm run lint       # ESLint for code quality
cd frontend && npm run build      # Production build validation
```

### Backend Testing and Quality
```bash
cd agent && poetry run pytest    # Run test suite
cd agent && poetry run black .   # Code formatting
cd agent && poetry run isort .   # Import sorting
cd agent && poetry run flake8    # Code linting
cd agent && poetry run mypy .    # Type checking
```

## Common Development Tasks

### Adding a New MCP Server
1. Configure via UI: Click "MCP Servers" button in the canvas interface
2. Add stdio server: Provide command and arguments for local execution
3. Add SSE server: Provide URL for remote MCP-compatible server
4. Configuration persists in browser localStorage and syncs with agent state

### Switching Between Backends
```bash
# Switch to GitHub Copilot (default)
./config/load-config.sh copilot
npm run dev

# Switch to OpenAI
./config/load-config.sh openai
# Edit agent/.env to add your OPENAI_API_KEY
npm run dev
```

### Debugging Agent Issues
1. Check agent logs: Agent output appears in terminal where `npm run dev:agent` was executed
2. Check MCP server configurations: Use the MCP config modal in the UI
3. Verify environment: Use `./config/validate.sh` to check configuration
4. Check all service logs: `npm run dev` shows parallel output from all services