# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Open Multi-Agent Canvas is a Next.js application with a Python backend that provides a multi-agent chat interface for travel planning, research, and general-purpose tasks through MCP (Model Context Protocol) servers. Built with CopilotKit, LangGraph, and LangChain.

## Architecture

### Frontend (Next.js)
- **Structure**: Next.js 15 app router with TypeScript
- **Framework**: React 19 with CopilotKit for agent integration
- **UI**: TailwindCSS with Radix UI components and Lucide icons
- **State**: React Query for data fetching, local storage for MCP configurations
- **Key Components**:
  - `Canvas`: Main layout with agent status and MCP configuration
  - `ChatWindow`: Shared chat interface for all agents
  - Agent components: Travel, Research, and MCP agents with specialized UI
  - `MCPConfigModal`: Configuration interface for MCP server connections

### Backend (Python)
- **Framework**: LangGraph for agent workflows with FastAPI/Poetry
- **Agent**: MCP Agent using langchain-mcp-adapters for tool integration
- **MCP Integration**: Supports both stdio (local commands) and SSE (remote servers) connections
- **Default Tools**: Built-in math server for demonstration

### Agent System
Three specialized agents with distinct capabilities:
1. **Travel Agent**: Trip planning with mapping and place recommendations
2. **Research Agent**: AI-powered research with source tracking and report generation
3. **MCP Agent**: General-purpose agent with configurable MCP server tools

## Commands

### Development Workflow
```bash
# Setup and install dependencies
make install                    # Install all dependencies (frontend + agent)
make setup-env                  # Setup environment files

# Development
make run-dev                    # Frontend development mode (port 3000)
make run-agent                  # Start agent backend (port 8123)

# Production
make build                      # Build frontend for production
make run-frontend               # Run frontend in production mode
make run                        # Run both frontend and agent with Copilot API

# Maintenance
make clean                      # Remove dependencies and build artifacts
make lint                       # Run ESLint on frontend code
```

### MCP Server Configuration
The application supports two types of MCP server connections:
- **stdio**: Local command execution (e.g., Python scripts)
- **sse**: Remote MCP-compatible servers via Server-Sent Events

Configure via the "MCP Servers" button in the UI or by modifying local storage.

### Copilot API Integration
```bash
# Prerequisites: Set GITHUB_TOKEN environment variable
export GITHUB_TOKEN=your_github_token_here

# Using Copilot API as OpenAI backend
make start-copilot-api          # Start Copilot API on port 4141
make run-with-copilot           # Run agent with Copilot API backend
make stop-copilot-api           # Stop Copilot API server
```

### Testing and Validation
```bash
make validate                   # Full configuration validation
make validate-quick             # Quick validation check
make validate-env               # Validate environment files only
```

## Key Files and Patterns

### Frontend State Management
- **Agent State**: Each agent maintains separate state through `useCoAgent` hook
- **MCP Configuration**: Stored in localStorage under `MCP_STORAGE_KEY`
- **Available Agents**: Enumerated in `src/lib/available-agents.ts`

### Backend Agent Structure
- **Agent Definition**: `agent/mcp-agent/agent.py` contains the main workflow graph
- **State**: `AgentState` inherits from `CopilotKitState` with optional `mcp_config`
- **Tools**: Dynamic tool loading based on MCP server configuration

### Environment Configuration
- **Frontend**: Requires `NEXT_PUBLIC_CPK_PUBLIC_API_KEY` from Copilot Cloud
- **Agent**: Requires `OPENAI_API_KEY` and optional `LANGSMITH_API_KEY`
- **Copilot API**: Requires `GITHUB_TOKEN` for authentication

## Development Notes

- Frontend uses pnpm for package management
- Backend uses Poetry for Python dependency management
- Agent backend runs on LangGraph dev server (port 8123)
- All agents share the same chat interface but maintain separate state
- MCP configurations are persisted in browser localStorage
- The system supports running with either OpenAI API or GitHub Copilot API as the backend