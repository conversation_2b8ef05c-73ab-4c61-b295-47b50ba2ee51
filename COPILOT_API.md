# Copilot API Integration - Default Backend

🤖 **Copilot API is now the default backend for Open Multi-Agent Canvas!**

This project is configured to use GitHub Copilot API as the primary LLM backend, providing access to Claude Sonnet 4 and other models through an OpenAI-compatible interface.

## 🚀 Quick Start

### 1. Set GitHub Token
```bash
export GITHUB_TOKEN=your_github_token_here
```
Get your token at: https://github.com/settings/tokens

### 2. Start Everything
```bash
# Option 1: One command to rule them all
make quick-start

# Option 2: Manual step-by-step
make setup-env
make install
make start-all

# Option 3: Use the startup script directly
./start.sh
```

### 3. Access Services
- **Frontend**: http://localhost:3000
- **Agent Backend**: http://localhost:8123
- **Copilot API**: http://localhost:4141

## 🔧 Configuration

### Default Configuration
```bash
# OpenAI-compatible settings for Copilot API
OPENAI_API_KEY=dummy
OPENAI_MODEL=claude-sonnet-4
OPENAI_BASE_URL=http://localhost:4141

# GitHub token for Copilot API
GITHUB_TOKEN=your_github_token_here
```

### Traditional OpenAI Alternative
To use traditional OpenAI instead, edit `agent/.env`:
```bash
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_BASE_URL=https://api.openai.com/v1
```

## 📝 Available Commands

### All-in-One Commands (Recommended)
```bash
make quick-start      # Setup + install + start everything
make start-all        # Start all services with Copilot API
make stop-all         # Stop all services
make status-all       # Check status of all services
```

### Individual Service Commands
```bash
# Copilot API management
make start-copilot-api    # Start Copilot API server
make stop-copilot-api     # Stop Copilot API server
make status-copilot-api   # Check Copilot API status
make ensure-copilot-api   # Ensure Copilot API is running

# Run services
make run                  # Run with Copilot API (default)
make run-agent-copilot    # Run agent with Copilot API
make run-agent            # Run agent with traditional OpenAI
make run-frontend         # Run frontend only
```

### Direct Script Usage
```bash
# Startup script
./start.sh start      # Start all services
./start.sh stop       # Stop all services
./start.sh status     # Show service status
./start.sh logs       # Show service logs

# Copilot API script
./scripts/copilot-api.sh start    # Start Copilot API
./scripts/copilot-api.sh status   # Check status
./scripts/copilot-api.sh stop     # Stop Copilot API
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Frontend     │    │  Agent Backend  │    │   Copilot API   │
│   (Next.js)     │────│   (LangGraph)   │────│   (Port 4141)   │
│  Port 3000      │    │   Port 8123     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   OpenAI API    │    │  GitHub Copilot │
                       │   Compatible    │    │     Models      │
                       │   Interface     │    │  (Claude, etc.) │
                       └─────────────────┘    └─────────────────┘
```

## 🔍 Monitoring & Debugging

### Service Status
```bash
# Check all services
make status-all

# Check individual services
curl http://localhost:3000  # Frontend
curl http://localhost:8123  # Agent
curl http://localhost:4141  # Copilot API
```

### Logs
```bash
# View all logs
./start.sh logs

# View specific logs
tail -f logs/frontend.log
tail -f logs/agent.log
./scripts/copilot-api.sh logs
```

### Health Checks
```bash
# Full validation
make validate

# Quick check
make validate-quick

# Environment validation
make validate-env
```

## 🛠️ Troubleshooting

### Common Issues

#### Copilot API Won't Start
```bash
# Check if GitHub token is set
echo $GITHUB_TOKEN

# Check if port is available
lsof -i :4141

# Restart Copilot API
make stop-copilot-api
make start-copilot-api
```

#### Agent Can't Connect
```bash
# Check Copilot API status
make status-copilot-api

# Verify configuration
cat agent/.env | grep OPENAI

# Check connectivity
curl http://localhost:4141
```

#### Frontend Issues
```bash
# Check if dependencies are installed
cd frontend && ls node_modules

# Reinstall dependencies
make clean
make install-frontend
```

### Environment Variables Issues
```bash
# Recreate environment files
rm frontend/.env agent/.env
make setup-env

# Validate environment
make validate-env
```

## 📊 Performance & Features

### Benefits of Copilot API Backend
- 🚀 **Access to Claude Sonnet 4**: High-quality responses
- 🔄 **OpenAI Compatibility**: Drop-in replacement
- 🆓 **Cost Effective**: Uses your GitHub Copilot subscription
- 🔒 **Secure**: Local API endpoint
- ⚡ **Fast**: Direct connection without external routing

### Supported Models
- `claude-sonnet-4` (default)
- `gpt-4` (if available)
- Other models supported by Copilot API

## 🔗 Integration Details

### Environment File Structure
```
agent/.env:
  OPENAI_API_KEY=dummy
  OPENAI_MODEL=claude-sonnet-4
  OPENAI_BASE_URL=http://localhost:4141
  GITHUB_TOKEN=your_token

frontend/.env:
  NEXT_PUBLIC_CPK_PUBLIC_API_KEY=your_copilot_key
  NEXT_PUBLIC_AGENT_ENDPOINT=http://localhost:8123
```

### Automatic Detection
The agent automatically detects whether to use Copilot API or traditional OpenAI based on the `OPENAI_BASE_URL` setting.

### Failover Support
If Copilot API is unavailable, you can quickly switch to traditional OpenAI by updating the environment variables.

## 📚 Additional Resources

- [Copilot API Documentation](https://docs.github.com/en/copilot)
- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [CopilotKit Documentation](https://docs.copilotkit.ai/)
- [Project Repository](https://github.com/CopilotKit/CopilotKit)

## 🆘 Support

If you encounter issues:

1. Check the [troubleshooting section](#troubleshooting)
2. Run `make validate` for comprehensive checks
3. Check service logs with `./start.sh logs`
4. Ensure your GitHub token has proper permissions
5. Verify all dependencies are installed

---

**Happy coding with Copilot API! 🚀**
