# Open Multi-Agent Canvas Makefile
# This Makefile provides conven# Run both frontend and agent with Copilot API (default configuration)
run: ensure-copilot-api run-fronte	@echo "🤖 Starting agent with Copilot API backend..."
	cd agent && OPENAI_API_KEY=dummy OPENAI_MODEL=claude-sonnet-4 OPENAI_BASE_URL=http://localhost:4141 ./run.sh

# Configuration Management
setup-copilot-config:
	@echo "🔧 Configuring agent to use Copilot API..."
	@sed -i.bak 's/^OPENAI_API_KEY=your_openai_api_key_here/#OPENAI_API_KEY=your_openai_api_key_here/' agent/.env
	@sed -i.bak 's/^OPENAI_MODEL=gpt-4/#OPENAI_MODEL=gpt-4/' agent/.env
	@sed -i.bak 's/^OPENAI_BASE_URL=https:\/\/api.openai.com\/v1/#OPENAI_BASE_URL=https:\/\/api.openai.com\/v1/' agent/.env
	@sed -i.bak 's/^#OPENAI_API_KEY=dummy/OPENAI_API_KEY=dummy/' agent/.env
	@sed -i.bak 's/^#OPENAI_MODEL=claude-sonnet-4/OPENAI_MODEL=claude-sonnet-4/' agent/.env
	@sed -i.bak 's/^#OPENAI_BASE_URL=http:\/\/localhost:4141/OPENAI_BASE_URL=http:\/\/localhost:4141/' agent/.env
	@echo "✅ Agent configured for Copilot API. Run 'make run' to start."

setup-openai-config:
	@echo "🔧 Configuring agent to use traditional OpenAI..."
	@sed -i.bak 's/^#OPENAI_API_KEY=your_openai_api_key_here/OPENAI_API_KEY=your_openai_api_key_here/' agent/.env
	@sed -i.bak 's/^#OPENAI_MODEL=gpt-4/OPENAI_MODEL=gpt-4/' agent/.env
	@sed -i.bak 's/^#OPENAI_BASE_URL=https:\/\/api.openai.com\/v1/OPENAI_BASE_URL=https:\/\/api.openai.com\/v1/' agent/.env
	@sed -i.bak 's/^OPENAI_API_KEY=dummy/#OPENAI_API_KEY=dummy/' agent/.env
	@sed -i.bak 's/^OPENAI_MODEL=claude-sonnet-4/#OPENAI_MODEL=claude-sonnet-4/' agent/.env
	@sed -i.bak 's/^OPENAI_BASE_URL=http:\/\/localhost:4141/#OPENAI_BASE_URL=http:\/\/localhost:4141/' agent/.env
	@echo "✅ Agent configured for traditional OpenAI."
	@echo "⚠️  Don't forget to set your OPENAI_API_KEY in agent/.env"

.PHONY: install run start-all stop-all run-frontend run-agent run-dev
.PHONY: run-agent-copilot start-copilot-api stop-copilot-api status-copilot-api ensure-copilot-api
.PHONY: setup-copilot-config setup-openai-config-bg run-agent
	@echo "🚀 Both frontend and agent are running with Copilot API backend!"t commands to manage the entire project

.PHONY: help install install-frontend install-agent run run-frontend run-agent run-agent-copilot run-dev clean build test lint format start-copilot-api stop-copilot-api status-copilot-api ensure-copilot-api config validate start-all stop-all status-all quick-start

# Default target
help:
	@echo "Open Multi-Agent Canvas - Available commands:"
	@echo ""
	@echo "  help           - Show this help message"
	@echo "  install        - Install all dependencies (frontend + agent)"
	@echo "  install-frontend - Install frontend dependencies"
	@echo "  install-agent  - Install agent dependencies"
	@echo "  run            - Run all services in parallel (Copilot API + frontend + agent)"
	@echo "  run-frontend   - Run frontend in production mode"
	@echo "  run-agent      - Run agent backend (uses .env configuration)"
	@echo "  run-agent-copilot - Run agent backend with Copilot API (explicit)"
	@echo "  run-agent-openai - Run agent backend with traditional OpenAI"
	@echo "  run-dev        - Run frontend in development mode"
	@echo "  dev-all        - Run all services in development mode (parallel)"
	@echo "  build          - Build the frontend application"
	@echo "  clean          - Clean all dependencies and build artifacts"
	@echo "  lint           - Run linting on frontend"
	@echo "  format         - Format code"
	@echo "  test           - Run tests (if available)"
	@echo ""
	@echo "All-in-One Commands:"
	@echo "  start-all      - Start all services in parallel (production mode)"
	@echo "  stop-all       - Stop all services"
	@echo "  status-all     - Show status of all services"
	@echo "  quick-start    - Setup + install + start all (recommended)"
	@echo ""
	@echo "Copilot API (Default Backend):"
	@echo "  start-copilot-api - Start Copilot API server on port 4141"
	@echo "  stop-copilot-api  - Stop Copilot API server"
	@echo "  status-copilot-api - Check Copilot API status"
	@echo ""
	@echo "Configuration:"
	@echo "  setup-copilot-config - Configure agent to use Copilot API"
	@echo "  setup-openai-config  - Configure agent to use traditional OpenAI"
	@echo "  ensure-copilot-api - Ensure Copilot API is running"
	@echo "  run-with-copilot  - Legacy: Run agent with Copilot API (same as run-agent-copilot)"
	@echo ""
	@echo "Configuration:"
	@echo "  config            - Show current configuration"
	@echo "  config-dev        - Load development configuration"
	@echo "  config-prod       - Load production configuration"
	@echo "  setup-env         - Setup environment files"
	@echo "  validate-env      - Validate environment files"
	@echo "  validate          - Full configuration validation"
	@echo "  validate-quick    - Quick validation check"
	@echo ""
	@echo "Prerequisites:"
	@echo "  - pnpm (for frontend)"
	@echo "  - poetry (for agent backend)"
	@echo "  - Python 3.8+ (for agent backend)"
	@echo "  - GITHUB_TOKEN (for Copilot API)"

# Install all dependencies
install: install-frontend install-agent
	@echo "✅ All dependencies installed successfully!"

# Install frontend dependencies
install-frontend:
	@echo "📦 Installing frontend dependencies..."
	cd frontend && chmod +x install.sh && ./install.sh

# Install agent dependencies
install-agent:
	@echo "🤖 Installing agent dependencies..."
	cd agent && chmod +x install.sh && ./install.sh

# Run both frontend and agent with Copilot API as default (parallel execution)
run: ensure-copilot-api
	@echo "🚀 Starting all services in parallel..."
	@echo "  - Frontend: http://localhost:3000"
	@echo "  - Agent API: http://localhost:8000" 
	@echo "  - Copilot API: http://localhost:4141"
	@echo ""
	@echo "Press Ctrl+C to stop all services"
	@(cd frontend && chmod +x run.sh && ./run.sh) & \
	(cd agent && chmod +x run.sh && ./run.sh) & \
	wait

# Start all services in parallel with status monitoring
start-all: ensure-copilot-api
	@echo "🚀 Starting Open Multi-Agent Canvas..."
	@echo "======================================="
	@echo ""
	@echo "Services:"
	@echo "  🌐 Frontend:    http://localhost:3000"
	@echo "  🤖 Agent API:   http://localhost:8000"
	@echo "  💬 Copilot API: http://localhost:4141"
	@echo ""
	@echo "Starting services in parallel..."
	@echo "Press Ctrl+C to stop all services"
	@echo ""
	@(echo "🌐 Starting frontend..." && cd frontend && chmod +x run.sh && ./run.sh) & \
	(echo "🤖 Starting agent..." && cd agent && chmod +x run.sh && ./run.sh) & \
	wait

# Stop all services
stop-all:
	@echo "🛑 Stopping all services..."
	@pkill -f "pnpm.*start" 2>/dev/null || true
	@pkill -f "next.*start" 2>/dev/null || true
	@pkill -f "poetry.*run" 2>/dev/null || true
	@pkill -f "python.*agent" 2>/dev/null || true
	@./scripts/copilot-api.sh stop
	@echo "✅ All services stopped"

# Show status of all services
status-all:
	@echo "📊 Service Status:"
	@echo "=================="
	@echo ""
	@echo "🌐 Frontend (port 3000):"
	@if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then \
		echo "  ✅ Running"; \
	else \
		echo "  ❌ Not running"; \
	fi
	@echo ""
	@echo "🤖 Agent API (port 8000):"
	@if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then \
		echo "  ✅ Running"; \
	else \
		echo "  ❌ Not running"; \
	fi
	@echo ""
	@echo "💬 Copilot API (port 4141):"
	@./scripts/copilot-api.sh status

# Run frontend in production mode
run-frontend:
	@echo "🌐 Starting frontend in production mode..."
	cd frontend && chmod +x run.sh && ./run.sh

# Run agent backend (uses .env configuration)
run-agent:
	@echo "🤖 Starting agent backend (using configuration from .env)..."
	cd agent && chmod +x run.sh && ./run.sh

# Run agent backend with traditional OpenAI (override .env)
run-agent-openai:
	@echo "🤖 Starting agent backend with traditional OpenAI..."
	cd agent && OPENAI_API_KEY=$$OPENAI_API_KEY OPENAI_MODEL=gpt-4 OPENAI_BASE_URL=https://api.openai.com/v1 ./run.sh

# Run agent backend with Copilot API (explicit override)
run-agent-copilot: ensure-copilot-api
	@echo "🤖 Starting agent backend with Copilot API..."
	cd agent && OPENAI_API_KEY=dummy OPENAI_MODEL=claude-sonnet-4 OPENAI_BASE_URL=http://localhost:4141 ./run.sh

# Run frontend in development mode
run-dev:
	@echo "🛠️ Starting frontend in development mode..."
	cd frontend && pnpm run dev

# Run all services in development mode with parallel logging
dev-all: ensure-copilot-api
	@echo "🛠️ Starting all services in development mode..."
	@echo "================================================"
	@echo ""
	@echo "Services:"
	@echo "  🌐 Frontend (dev): http://localhost:3000"
	@echo "  🤖 Agent API:      http://localhost:8000"
	@echo "  💬 Copilot API:    http://localhost:4141"
	@echo ""
	@echo "Press Ctrl+C to stop all services"
	@echo ""
	@if command -v npx >/dev/null 2>&1; then \
		npx concurrently \
			--names "frontend,agent" \
			--prefix-colors "blue,green" \
			--kill-others \
			"cd frontend && pnpm run dev" \
			"cd agent && chmod +x run.sh && ./run.sh"; \
	else \
		echo "⚠️  concurrently not found, falling back to basic parallel execution..."; \
		(echo "🌐 Starting frontend..." && cd frontend && pnpm run dev) & \
		(echo "🤖 Starting agent..." && cd agent && chmod +x run.sh && ./run.sh) & \
		wait; \
	fi

# Quick start - setup, install, and run everything
quick-start: setup-env install start-all
	@echo "🎉 Quick start complete! All services are running."

# Build frontend
build:
	@echo "🔨 Building frontend..."
	cd frontend && pnpm run build

# Clean all dependencies and build artifacts
clean:
	@echo "🧹 Cleaning dependencies and build artifacts..."
	cd frontend && rm -rf node_modules .next pnpm-lock.yaml
	cd agent && rm -rf .venv __pycache__ .pytest_cache *.egg-info
	@echo "✅ Cleanup complete!"

# Setup environment files
setup-env:
	@echo "⚙️ Setting up environment files..."
	./config/setup-env.sh setup development

# Configuration commands
config:
	@echo "📊 Current Configuration:"
	./config/setup-env.sh show development

config-dev:
	@echo "🛠️ Loading development configuration..."
	./config/load-config.sh --env development

config-prod:
	@echo "🏭 Loading production configuration..."
	./config/load-config.sh --env production

validate-env:
	@echo "🔍 Validating environment files..."
	./config/setup-env.sh validate

# Full configuration validation
validate:
	@echo "🔍 Running full configuration validation..."
	./config/validate.sh

# Quick validation
validate-quick:
	@echo "🔍 Running quick validation..."
	./config/validate.sh quick

# Copilot API commands
start-copilot-api:
	@echo "🤖 Starting Copilot API server on port 4141..."
	./scripts/copilot-api.sh start

stop-copilot-api:
	@echo "🛑 Stopping Copilot API server..."
	./scripts/copilot-api.sh stop

# Check Copilot API status
status-copilot-api:
	@echo "� Checking Copilot API status..."
	./scripts/copilot-api.sh status

# Ensure Copilot API is running
ensure-copilot-api:
	@if [ -f agent/.env ]; then \
		export $$(grep -v '^#' agent/.env | xargs) && ./scripts/copilot-api.sh ensure; \
	else \
		./scripts/copilot-api.sh ensure; \
	fi

# Run agent with Copilot API as OpenAI backend (legacy command)
run-with-copilot: ensure-copilot-api
	@echo "🚀 Starting agent with Copilot API backend..."
	cd agent && OPENAI_API_KEY=dummy OPENAI_MODEL=claude-sonnet-4 OPENAI_BASE_URL=http://localhost:4141 ./run.sh

# Lint frontend code
lint:
	@echo "🔍 Linting frontend code..."
	cd frontend && pnpm run lint

# Format code (if formatter is available)
format:
	@echo "💅 Formatting code..."
	cd frontend && pnpm run lint --fix 2>/dev/null || echo "No formatter configured"

# Run tests
test:
	@echo "🧪 Running tests..."
	@echo "No tests configured yet"

# Quick start command with Copilot API
quick-start: setup-env install
	@echo "🚀 Quick start with Copilot API..."
	./start.sh start

# Production start command
prod-start: setup-env install build run
	@echo "🚀 Production deployment complete!"

# Start all services with one command
start-all:
	@echo "🚀 Starting all services with Copilot API..."
	./start.sh start

# Stop all services
stop-all:
	@echo "🛑 Stopping all services..."
	./start.sh stop

# Show service status
status-all:
	@echo "📊 Showing service status..."
	./start.sh status
