

<div align="center">

# Open Multi-Agent Canvas
   
![CopilotKit-Banner](https://github.com/user-attachments/assets/8167c845-0381-45d9-ad1c-83f995d48290)
</div>


![multi-agent-canvas](https://github.com/user-attachments/assets/5953a5a6-5686-4722-9477-5279b67b3dba)


Open Multi-Agent Canvas, created by [CopilotK<PERSON>](https://github.com/CopilotKit/CopilotKit) is an open-source multi-agent chat interface that lets you manage multiple agents in one dynamic conversation. It's built with Next.js, LangGraph, and CopilotKit to help with travel planning, research, and general-purpose tasks through MCP servers.

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
./setup.sh    # Interactive setup script
make install  # Install dependencies
make run      # Start the system
```

### Option 2: Manual Setup
```bash
# 1. Install dependencies
make install

# 2. Configure your backend (choose one):
make setup-copilot-config  # For GitHub Copilot API
make setup-openai-config   # For traditional OpenAI

# 3. Edit agent/.env with your API keys
nano agent/.env

# 4. Start the system
make run
```

## 🔧 Backend Options

### GitHub Copilot API (Recommended)
- **Model**: claude-sonnet-4 via GitHub Copilot
- **Setup**: Requires GitHub account with Copilot access
- **Cost**: Included with GitHub Copilot subscription

**Configuration:**
```bash
make setup-copilot-config
# Then edit agent/.env to add your GITHUB_TOKEN
```

### Traditional OpenAI API
- **Models**: gpt-4, gpt-3.5-turbo, etc.
- **Setup**: Requires OpenAI account and API key
- **Cost**: Pay-per-use OpenAI pricing

**Configuration:**
```bash
make setup-openai-config
# Then edit agent/.env to add your OPENAI_API_KEY
```

## 🎯 Available Commands

### Main Commands
```bash
make help           # Show all available commands
make install        # Install all dependencies
make run            # Start both frontend and agent
make start-all      # Alternative to run (with status info)
make stop-all       # Stop all services
```

### Development
```bash
make run-dev        # Run frontend in development mode
make run-frontend   # Run frontend only (production)
make run-agent      # Run agent backend only
make build          # Build frontend for production
```

### Backend Configuration
```bash
make setup-copilot-config  # Configure for Copilot API
make setup-openai-config   # Configure for OpenAI API
```

### Copilot API Management
```bash
make start-copilot-api    # Start Copilot API server
make stop-copilot-api     # Stop Copilot API server
make status-copilot-api   # Check Copilot API status
```

## 🌐 Access Points

Once started, the system will be available at:
- **Frontend**: http://localhost:3000
- **Agent API**: http://localhost:8000
- **Copilot API**: http://localhost:4141 (if using Copilot backend)

## 🔑 API Key Setup

### GitHub Token (for Copilot API)
1. Go to [GitHub Settings > Personal Access Tokens](https://github.com/settings/tokens)
2. Click "Generate new token (classic)"
3. Select scopes: `repo`, `read:user`, `user:email`
4. Copy the token and add to `agent/.env`:
   ```
   GITHUB_TOKEN=ghp_your_token_here
   ```

### OpenAI API Key (for OpenAI backend)
1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add to `agent/.env`:
   ```
   OPENAI_API_KEY=sk-your_key_here
   ```

## 🛠 Troubleshooting

### Copilot API Issues

**Error: "Failed to get Copilot token"**
- Verify your GitHub token has correct permissions
- Ensure you have GitHub Copilot access
- Try regenerating your GitHub token

**Fix:**
```bash
make stop-copilot-api
make setup-openai-config  # Switch to OpenAI temporarily
```

### Port Conflicts

**Error: "Port already in use"**
```bash
# Check what's using the ports
lsof -i :3000  # Frontend
lsof -i :8000  # Agent
lsof -i :4141  # Copilot API

# Stop conflicting processes
make stop-all
```

## 🔄 Switching Backends

You can easily switch between Copilot API and OpenAI:

```bash
# Switch to Copilot API
make setup-copilot-config

# Switch to OpenAI
make setup-openai-config

# Restart the system
make stop-all
make run
```

## Existing Agents

Check out these awesome agents (they live in separate repositories). You can run them separately or deploy them on LangSmith:
- [CoAgents Travel Agent](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-travel/agent)
- [CoAgents AI Researcher](https://github.com/CopilotKit/CopilotKit/tree/main/examples/coagents-ai-researcher/agent)

Additionally, this project now includes a built-in MCP (Multi-Channel Protocol) Agent:
- **MCP Agent**: A general-purpose agent capable of handling various tasks through configurable MCP servers.

## Copilot Cloud is required to run this project: 




## Quick Start 🚀

### 1. Prerequisites
Make sure you have:
- [pnpm](https://pnpm.io/installation)

### 2. API Keys
- [Copilot Cloud](https://cloud.copilotkit.ai)

## Running the Frontend

Rename the `example.env` file in the `frontend` folder to `.env`:

```sh
NEXT_PUBLIC_CPK_PUBLIC_API_KEY=...
```

Install dependencies:

```sh
cd frontend
pnpm i
```



Need a CopilotKit API key? Get one [here](https://cloud.copilotkit.ai/).

Then, fire up the Next.js project:

```sh
pnpm run build && pnpm run start
```

## MCP Agent Setup

![mcp-demo](./agent/demo/mcp-demo.gif)

The MCP Agent allows you to connect to various MCP-compatible servers:

1. **Configuring Custom MCP Servers**:
   - Click the "MCP Servers" button in the top right of the interface
   - Add servers via the configuration panel:
     - **Standard IO**: Run commands locally (e.g., Python scripts)
     - **SSE**: Connect to external MCP-compatible servers (via Server-Sent Events)

2. **Public MCP Servers**:
   - You can connect to public MCP servers like [mcp.composio.dev](https://mcp.composio.dev/) and [mcp.run](https://www.mcp.run/)

## Running the MCP Agent Backend (Optional)

Rename the `example.env` file in the `agent` folder to `.env`:

```sh
OPENAI_API_KEY=...
LANGSMITH_API_KEY=...
```

If you want to use the included MCP Agent with the built-in math server:

```sh
cd agent
poetry install
poetry run langgraph dev --host localhost --port 8123 --no-browser
```

## Running a tunnel

Add another terminal and select Remote Endpoint.
Then select Local Development.
Once this is done, copy the command into your terminal and change the port to match the LangGraph server `8123`
![image](https://github.com/user-attachments/assets/6bf41042-9529-4470-8baf-dd076aad31a1)


## Documentation 
- [CopilotKit Docs](https://docs.copilotkit.ai/coagents)
- [LangGraph Platform Docs](https://langchain-ai.github.io/langgraph/cloud/deployment/cloud/)
- [Model Context Protocol (MCP) Docs](https://github.com/langchain-ai/langgraph/tree/main/examples/mcp)

## License
Distributed under the MIT License. See LICENSE for more info.
