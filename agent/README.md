# Open Multi-Agent Canvas - Agent Backend

Python backend for the Open Multi-Agent Canvas with MCP server integration.

## MCP Servers

- **restaurant-intelligence-server**: BiteBase Intelligence restaurant analytics
- **math-server**: Basic math operations for demonstration

## Usage

```bash
# Install dependencies
poetry install

# Run MCP servers directly
poetry run restaurant-intelligence-server
poetry run math-server

# Or run the full agent backend
./run.sh
```