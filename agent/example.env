# Auto-generated environment file for agent
# Generated on: Sat Sep 13 12:57:01 UTC 2025
# Environment: development

# Agent Configuration - Using Copilot API as Default Backend
OPENAI_API_KEY=dummy
OPENAI_MODEL=claude-sonnet-4
OPENAI_BASE_URL=http://localhost:4141

LANGSMITH_API_KEY=***************************************************

# OpenAI Configuration - Backup/Alternative LLM Provider
# Use these settings when running with traditional OpenAI API instead
#OPENAI_API_KEY=your_openai_api_key_here
#OPENAI_MODEL=gpt-4
#OPENAI_BASE_URL=https://api.openai.com/v1

# GitHub Token for Copilot API (required for run-with-copilot)
GITHUB_TOKEN=****************************************

# Environment
LOG_LEVEL=debug
DEBUG=true

# Service Configuration
AGENT_HOST=
AGENT_PORT=8123
