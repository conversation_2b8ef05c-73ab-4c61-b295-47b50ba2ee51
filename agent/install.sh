#!/bin/bash

# Agent Installation Script for Open Multi-Agent Canvas
# This script installs all agent backend dependencies

set -e  # Exit on any error

echo "🤖 Installing Open Multi-Agent Canvas Agent Backend..."
echo "======================================================"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
echo "✅ Python version: $(python3 --version)"

# Check if Node.js and npx are available (needed for Copilot API)
if command -v node &> /dev/null && command -v npx &> /dev/null; then
    echo "✅ Node.js and npx available: $(node --version)"
else
    echo "⚠️  Node.js/npx not found. Install Node.js to use Copilot API features."
fi

# Check Python version (should be 3.8+)
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
    echo "⚠️  Python 3.8+ is recommended. Current version: $PYTHON_VERSION"
fi

# Check if we're in the agent directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ pyproject.toml not found. Please run this script from the agent directory."
    exit 1
fi

# Check if poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "📦 Poetry not found. Installing poetry..."
    curl -sSL https://install.python-poetry.org | python3 -
    
    # Add poetry to PATH for current session
    export PATH="$HOME/.local/bin:$PATH"
    
    # Check if poetry is now available
    if ! command -v poetry &> /dev/null; then
        echo "❌ Poetry installation failed. Please install poetry manually:"
        echo "   curl -sSL https://install.python-poetry.org | python3 -"
        echo "   Then restart your terminal or run: export PATH=\"\$HOME/.local/bin:\$PATH\""
        exit 1
    fi
fi

echo "✅ Poetry found: $(poetry --version)"

# Configure poetry to create virtual environment in project directory
poetry config virtualenvs.in-project true

# Install dependencies
echo "📦 Installing Python dependencies with poetry..."
poetry install

