[tool.poetry]
name = "bitebase-intelligence-canvas"
version = "0.1.0"
description = "Multi-agent canvas with restaurant intelligence, research, and MCP agent capabilities"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT"
readme = "README.md"
packages = [{include = "mcp-agent"}]

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
# Core LangChain and AI dependencies
langchain = "^0.3.1"
langchain-core = "^0.3.25"
langchain-openai = "^0.2.1"
langchain-anthropic = "^0.2.1"
langchain-community = "^0.3.1"
openai = "^1.51.0"
# LangGraph for agent workflows
langgraph = "^0.3.5"
langgraph-cli = {extras = ["inmem"], version = "^0.1.64"}
# CopilotKit integration
copilotkit = "0.1.39"
# MCP (Model Context Protocol) support
fastmcp = "^0.4.1"
langchain-mcp-adapters = "^0.0.3"
mcp = "^1.3.0"
# Web server and utilities
uvicorn = "^0.31.0"
python-dotenv = "^1.0.1"
# OpenTelemetry for observability
opentelemetry-api = "^1.15.0"
opentelemetry-sdk = "^1.15.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"

[tool.poetry.scripts]
# MCP Servers
restaurant-intelligence-server = "restaurant_intelligence_server:main"
math-server = "math_server:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# Code formatting with Black
[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# Import sorting with isort
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["mcp-agent"]

# Type checking with mypy
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

# Testing with pytest
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
addopts = "-v --tb=short"
asyncio_mode = "auto"

# Coverage configuration
[tool.coverage.run]
source = ["mcp-agent", "restaurant_intelligence_server", "math_server"]
omit = ["tests/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

