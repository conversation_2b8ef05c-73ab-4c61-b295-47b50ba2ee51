"""
Restaurant Intelligence Agent

This agent specializes in restaurant analytics and business intelligence,
providing comprehensive reports for location analysis, competitor benchmarking,
menu optimization, sales forecasting, customer segmentation, and franchise expansion.
"""

import os

from copilotkit import CopilotKitState
from langchain_core.prompts import Chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import create_react_agent
from langgraph.types import Command
from typing_extensions import Any, Dict, List, Literal, Optional, TypedDict, Union


# Define the connection type structures
class StdioConnection(TypedDict):
    command: str
    args: List[str]
    transport: Literal["stdio"]


class SSEConnection(TypedDict):
    url: str
    transport: Literal["sse"]


# Type for MCP configuration
MCPConfig = Dict[str, Union[StdioConnection, SSEConnection]]


class RestaurantIntelligenceState(CopilotKitState):
    """
    State for the Restaurant Intelligence Agent

    Inherits from CopilotKitState and adds restaurant-specific configuration
    for the MCP server that provides restaurant analytics tools.
    """

    # Define mcp_config as an optional field for restaurant intelligence tools
    mcp_config: Optional[MCPConfig]


# Default MCP configuration for Restaurant Intelligence
DEFAULT_RESTAURANT_MCP_CONFIG: MCPConfig = {
    "restaurant_intelligence": {
        "command": "python",
        # Use the restaurant intelligence server we just created
        "args": [
            os.path.join(os.path.dirname(__file__), "restaurant_intelligence_server.py")
        ],
        "transport": "stdio",
    },
}

# Define a specialized prompt for restaurant intelligence analysis
RESTAURANT_INTELLIGENCE_PROMPT = ChatPromptTemplate.from_messages(
    [
        (
            "system",
            """You are a Restaurant Intelligence Analyst specializing in data-driven business insights for restaurants.

You have access to powerful analytics tools that can generate comprehensive reports for:
- Location Analysis: Site selection and trade area analysis
- Competitor Benchmarking: Market positioning and competitive intelligence
- Menu Optimization: Profit and popularity analysis with strategic recommendations
- Sales Forecasting: Predictive revenue modeling with seasonal adjustments
- Customer Segmentation: Behavioral analysis and targeting strategies
- Franchise Expansion: Market feasibility and financial projections

Your approach should be:

1. **Consultative**: Ask clarifying questions to understand the user's specific business needs
2. **Strategic**: Focus on actionable insights that drive business decisions
3. **Comprehensive**: Use multiple analysis types when they provide complementary insights
4. **Professional**: Present findings in a clear, business-focused manner

When generating reports:
- Always explain the business context and implications
- Highlight the most critical findings and recommendations
- Suggest follow-up analyses that could provide additional value
- Present data in a way that supports strategic decision-making

You have access to the following restaurant intelligence tools:

{tools}

To use a tool, follow this format:
```
Thought: I need to analyze [specific business question] for the user.
Action: [tool_name]
Action Input: [appropriate parameters based on user's request]
```

The observation will show the results in JSON format, which you should interpret and present in a business-friendly way.

When you have completed the analysis, provide:
```
Thought: I can now provide comprehensive business insights.
Final Answer: [Clear summary of findings, key recommendations, and suggested next steps]
```

Always focus on delivering actionable business intelligence that helps restaurant operators make informed decisions.
""",
        ),
        MessagesPlaceholder(variable_name="messages"),
    ]
)


async def restaurant_intelligence_node(
    state: RestaurantIntelligenceState, config: RunnableConfig
) -> Command[Literal["__end__"]]:
    """
    Restaurant Intelligence Agent Node

    This node provides specialized restaurant analytics using the Restaurant Intelligence MCP server.
    It focuses on generating business intelligence reports and strategic recommendations.
    """
    # Get MCP configuration from state, or use the default restaurant intelligence config
    mcp_config = state.get("mcp_config", DEFAULT_RESTAURANT_MCP_CONFIG)

    # Set up the MCP client with restaurant intelligence tools
    async with MultiServerMCPClient(mcp_config) as mcp_client:
        # Get the restaurant intelligence tools
        mcp_tools = mcp_client.get_tools()
        print(
            f"Restaurant Intelligence tools available: {[tool.name for tool in mcp_tools]}"
        )

        # Create a model instance optimized for business analysis
        model = ChatOpenAI(
            model="gpt-4o", temperature=0.1
        )  # Lower temperature for more consistent business analysis

        # Create the restaurant intelligence react agent with specialized prompt
        restaurant_agent = create_react_agent(
            model, mcp_tools, prompt=RESTAURANT_INTELLIGENCE_PROMPT
        )

        # Prepare messages for the react agent
        agent_input = {"messages": state["messages"]}

        # Run the restaurant intelligence agent
        agent_response = await restaurant_agent.ainvoke(agent_input)

        print(
            f"Restaurant Intelligence Agent response: {len(agent_response.get('messages', []))} messages generated"
        )

        # Update the state with the new messages
        updated_messages = state["messages"] + agent_response.get("messages", [])

        # End the graph with the updated messages
        return Command(
            goto=END,
            update={"messages": updated_messages},
        )


# Define the workflow graph for Restaurant Intelligence
restaurant_workflow = StateGraph(RestaurantIntelligenceState)
restaurant_workflow.add_node(
    "restaurant_intelligence_node", restaurant_intelligence_node
)
restaurant_workflow.set_entry_point("restaurant_intelligence_node")

# Compile the workflow graph with memory
restaurant_intelligence_graph = restaurant_workflow.compile(MemorySaver())

# Export the graph for use in the main application
__all__ = [
    "restaurant_intelligence_graph",
    "RestaurantIntelligenceState",
    "DEFAULT_RESTAURANT_MCP_CONFIG",
]
