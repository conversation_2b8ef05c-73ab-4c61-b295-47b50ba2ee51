"""
Restaurant Intelligence MCP Server

Provides comprehensive restaurant analytics and reporting tools following the BiteBase Intelligence
specification. Generates six types of reports: location analysis, competitor benchmarking,
menu optimization, sales forecasting, customer segmentation, and franchise expansion.
"""

import json
import random
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from mcp.server.fastmcp import FastMCP

mcp = FastMCP("RestaurantIntelligence")


# Mock data generators for realistic report content
def generate_location_data(location: str) -> Dict[str, Any]:
    """Generate mock location analysis data"""
    base_lat = 40.7128 + random.uniform(-0.1, 0.1)
    base_lng = -74.0060 + random.uniform(-0.1, 0.1)

    restaurants = []
    restaurant_types = [
        ("Mario's Italian Bistro", "Italian", "$$", "direct"),
        ("Sakura Sushi", "Japanese", "$$$", "indirect"),
        ("The Local Burger", "American", "$", "direct"),
        ("Cafe Lumière", "French", "$$$$", "indirect"),
        ("Taco Junction", "Mexican", "$", "potential"),
        ("Golden Dragon", "Chinese", "$$", "indirect"),
        ("Pasta Paradise", "Italian", "$$", "direct"),
        ("Green Garden Vegan", "Vegetarian", "$$$", "potential"),
    ]

    for i, (name, cuisine, price, comp_level) in enumerate(restaurant_types[:6]):
        restaurants.append(
            {
                "id": f"rest_{i+1}",
                "name": name,
                "address": f"{100 + i*15} Main St, {location}",
                "latitude": base_lat + random.uniform(-0.01, 0.01),
                "longitude": base_lng + random.uniform(-0.01, 0.01),
                "rating": round(random.uniform(3.5, 4.8), 1),
                "description": f"Popular {cuisine.lower()} restaurant known for authentic flavors and quality service.",
                "cuisine_type": cuisine,
                "price_range": price,
                "competitor_level": comp_level,
            }
        )

    return {
        "center_latitude": base_lat,
        "center_longitude": base_lng,
        "zoom": 14,
        "restaurants": restaurants,
    }


def generate_menu_items() -> List[Dict[str, Any]]:
    """Generate mock menu optimization data"""
    menu_items = [
        {
            "name": "Margherita Pizza",
            "category": "Pizza",
            "price": 18.99,
            "popularity": 85,
            "profit_margin": 0.65,
        },
        {
            "name": "Caesar Salad",
            "category": "Salads",
            "price": 14.99,
            "popularity": 70,
            "profit_margin": 0.75,
        },
        {
            "name": "Grilled Salmon",
            "category": "Seafood",
            "price": 28.99,
            "popularity": 60,
            "profit_margin": 0.55,
        },
        {
            "name": "Chicken Parmesan",
            "category": "Entrees",
            "price": 22.99,
            "popularity": 78,
            "profit_margin": 0.62,
        },
        {
            "name": "Mushroom Risotto",
            "category": "Vegetarian",
            "price": 19.99,
            "popularity": 45,
            "profit_margin": 0.68,
        },
        {
            "name": "Chocolate Lava Cake",
            "category": "Desserts",
            "price": 9.99,
            "popularity": 65,
            "profit_margin": 0.80,
        },
    ]
    return menu_items


def generate_customer_segments() -> List[Dict[str, Any]]:
    """Generate mock customer segmentation data"""
    segments = [
        {
            "id": "seg_1",
            "name": "Frequent Diners",
            "description": "Regular customers who visit 3+ times per month",
            "size": 1250,
            "avg_spend": 45.80,
            "frequency": "Weekly",
            "characteristics": ["High loyalty", "Price insensitive", "Values quality"],
        },
        {
            "id": "seg_2",
            "name": "Occasional Visitors",
            "description": "Customers who visit 1-2 times per month",
            "size": 3400,
            "avg_spend": 32.50,
            "frequency": "Monthly",
            "characteristics": ["Price conscious", "Family oriented", "Weekend dining"],
        },
        {
            "id": "seg_3",
            "name": "Special Occasion",
            "description": "Customers who visit for celebrations and events",
            "size": 890,
            "avg_spend": 78.90,
            "frequency": "Quarterly",
            "characteristics": ["High spenders", "Group dining", "Quality focused"],
        },
    ]
    return segments


@mcp.tool()
def generate_location_analysis(
    location: str,
    concept_type: str = "Italian Restaurant",
    analysis_radius: str = "1 mile",
) -> str:
    """
    Generate a location analysis report for restaurant site selection.

    Args:
        location: Target location for analysis (e.g., "Downtown Seattle", "Brooklyn Heights")
        concept_type: Type of restaurant concept (e.g., "Italian Restaurant", "Fast Casual")
        analysis_radius: Radius for competitive analysis (e.g., "0.5 mile", "1 mile")

    Returns:
        JSON string containing location analysis data
    """

    location_data = generate_location_data(location)

    report_data = {
        "id": str(uuid.uuid4()),
        "title": f"Location Analysis: {location}",
        "type": "location_analysis",
        "status": "completed",
        "createdAt": datetime.now().isoformat(),
        "completedAt": datetime.now().isoformat(),
        "summary": f"Comprehensive location analysis for {concept_type} in {location} within {analysis_radius} radius.",
        "content": {
            "executiveSummary": f"Analysis of {location} reveals a vibrant dining market with {len(location_data['restaurants'])} competing establishments. The area shows strong demographic indicators for {concept_type.lower()} concepts with moderate competition density.",
            "keyFindings": [
                f"Trade area contains {len(location_data['restaurants'])} direct and indirect competitors",
                f"Average competitor rating is {round(sum(r['rating'] for r in location_data['restaurants']) / len(location_data['restaurants']), 1)} stars",
                "Demographic profile shows strong alignment with target customer base",
                "Foot traffic patterns indicate peak dining hours align with concept strategy",
                "Commercial rent rates are within acceptable range for concept profitability",
            ],
            "recommendations": [
                {
                    "id": "rec_1",
                    "title": "Proceed with Site Acquisition",
                    "description": "Location demonstrates strong market fundamentals and manageable competition",
                    "priority": "high",
                    "category": "Site Selection",
                    "impact": "High revenue potential with proper execution",
                    "effort": "3-6 months development timeline",
                    "timeline": "Q2 2024",
                },
                {
                    "id": "rec_2",
                    "title": "Differentiation Strategy Required",
                    "description": "Develop unique value proposition to stand out from existing competitors",
                    "priority": "medium",
                    "category": "Positioning",
                    "impact": "Essential for market penetration",
                    "effort": "Menu and concept refinement",
                    "timeline": "Pre-opening",
                },
            ],
            "analysis_data": {
                "location": location,
                "concept_type": concept_type,
                "analysis_radius": analysis_radius,
                **location_data,
            },
        },
        "metadata": {
            "requestId": str(uuid.uuid4()),
            "query": f"Location analysis for {concept_type} in {location}",
            "analysisTime": random.randint(180, 300),
            "confidence": round(random.uniform(0.75, 0.92), 2),
            "dataSourcesUsed": ["Placer.ai", "SafeGraph", "Census Bureau", "RentCast"],
            "limitations": [
                "Analysis based on current market conditions",
                "Seasonal variations not fully accounted for",
                "Future development plans may impact projections",
            ],
        },
    }

    return json.dumps(report_data, indent=2)


@mcp.tool()
def generate_competitor_benchmarking(
    market_area: str,
    competitor_focus: str = "Direct Competitors",
    analysis_period: str = "Last 6 months",
) -> str:
    """
    Generate a competitor benchmarking report comparing performance against market competitors.

    Args:
        market_area: Geographic market for analysis (e.g., "Manhattan", "Silicon Valley")
        competitor_focus: Type of competitors to analyze ("Direct Competitors", "All Competitors", "Category Leaders")
        analysis_period: Time period for analysis ("Last 3 months", "Last 6 months", "Last year")

    Returns:
        JSON string containing competitor benchmarking data
    """

    competitors = [
        {
            "name": "Tony's Italian Kitchen",
            "market_share": 15.2,
            "rating": 4.3,
            "price_point": "$$",
        },
        {
            "name": "Bella Vista",
            "market_share": 12.8,
            "rating": 4.1,
            "price_point": "$$$",
        },
        {
            "name": "Romano's Bistro",
            "market_share": 10.5,
            "rating": 4.4,
            "price_point": "$$",
        },
        {
            "name": "Casa Italiana",
            "market_share": 8.9,
            "rating": 3.9,
            "price_point": "$",
        },
        {
            "name": "Your Restaurant",
            "market_share": 7.3,
            "rating": 4.2,
            "price_point": "$$",
        },
    ]

    report_data = {
        "id": str(uuid.uuid4()),
        "title": f"Competitor Benchmarking: {market_area}",
        "type": "competitor_benchmarking",
        "status": "completed",
        "createdAt": datetime.now().isoformat(),
        "completedAt": datetime.now().isoformat(),
        "summary": f"Competitive analysis of {competitor_focus.lower()} in {market_area} over {analysis_period.lower()}.",
        "content": {
            "executiveSummary": f"Competitive analysis of {market_area} reveals a fragmented market with opportunities for growth. Your restaurant ranks #5 in market share but shows strong rating performance relative to competitors.",
            "keyFindings": [
                "Market leader holds 15.2% market share, indicating fragmented competitive landscape",
                "Your restaurant's 4.2-star rating exceeds market average of 4.1 stars",
                "Pricing strategy aligns with majority of successful competitors",
                "Significant opportunity exists to capture market share from underperforming competitors",
                "Customer satisfaction scores suggest quality positioning is effective",
            ],
            "recommendations": [
                {
                    "id": "rec_1",
                    "title": "Aggressive Market Share Growth",
                    "description": "Target 12% market share through enhanced marketing and service delivery",
                    "priority": "high",
                    "category": "Growth Strategy",
                    "impact": "65% revenue increase potential",
                    "effort": "6-month focused marketing campaign",
                    "timeline": "Q3-Q4 2024",
                },
                {
                    "id": "rec_2",
                    "title": "Premium Positioning Strategy",
                    "description": "Leverage superior rating to justify slight premium pricing",
                    "priority": "medium",
                    "category": "Pricing",
                    "impact": "8-12% margin improvement",
                    "effort": "Menu repricing and value communication",
                    "timeline": "Q3 2024",
                },
            ],
            "competitor_data": {
                "market_area": market_area,
                "competitors": competitors,
                "market_trends": {
                    "growth_rate": "3.2% YoY",
                    "avg_check_size": "$32.50",
                    "customer_retention": "68%",
                },
            },
        },
        "metadata": {
            "requestId": str(uuid.uuid4()),
            "query": f"Competitor benchmarking for {market_area}",
            "analysisTime": random.randint(240, 420),
            "confidence": round(random.uniform(0.80, 0.95), 2),
            "dataSourcesUsed": ["Placer.ai", "Twingly", "Google Reviews", "MealMe"],
            "limitations": [
                "Data based on publicly available information",
                "Seasonal fluctuations may affect current rankings",
                "Private financial data not available for competitors",
            ],
        },
    }

    return json.dumps(report_data, indent=2)


@mcp.tool()
def generate_menu_optimization(
    menu_focus: str = "Profitability",
    analysis_type: str = "Full Menu",
    optimization_goal: str = "Increase Profit Margins",
) -> str:
    """
    Generate a menu optimization report analyzing menu performance and recommendations.

    Args:
        menu_focus: Primary optimization focus ("Profitability", "Popularity", "Both")
        analysis_type: Scope of analysis ("Full Menu", "Entrees Only", "Top Performers")
        optimization_goal: Primary goal ("Increase Profit Margins", "Boost Sales", "Reduce Costs")

    Returns:
        JSON string containing menu optimization data
    """

    menu_items = generate_menu_items()

    # Categorize items using menu engineering matrix
    stars = [
        item
        for item in menu_items
        if item["popularity"] >= 70 and item["profit_margin"] >= 0.60
    ]
    plowhorses = [
        item
        for item in menu_items
        if item["popularity"] >= 70 and item["profit_margin"] < 0.60
    ]
    puzzles = [
        item
        for item in menu_items
        if item["popularity"] < 70 and item["profit_margin"] >= 0.60
    ]
    dogs = [
        item
        for item in menu_items
        if item["popularity"] < 70 and item["profit_margin"] < 0.60
    ]

    report_data = {
        "id": str(uuid.uuid4()),
        "title": f"Menu Optimization: {menu_focus} Analysis",
        "type": "menu_optimization",
        "status": "completed",
        "createdAt": datetime.now().isoformat(),
        "completedAt": datetime.now().isoformat(),
        "summary": f"Comprehensive menu analysis focused on {menu_focus.lower()} with goal to {optimization_goal.lower()}.",
        "content": {
            "executiveSummary": f"Menu analysis reveals {len(stars)} star items and {len(dogs)} underperforming items. Strategic menu engineering can improve overall profitability by 15-20% through targeted pricing and positioning adjustments.",
            "keyFindings": [
                f"Menu contains {len(stars)} star items (high profit, high popularity)",
                f"{len(plowhorses)} items are plowhorses (popular but low margin)",
                f"{len(puzzles)} items are puzzles (profitable but unpopular)",
                f"{len(dogs)} items are dogs (low profit and popularity)",
                f"Average menu profit margin is {round(sum(item['profit_margin'] for item in menu_items) / len(menu_items) * 100, 1)}%",
            ],
            "recommendations": [
                {
                    "id": "rec_1",
                    "title": "Promote Star Items",
                    "description": "Feature high-performing items prominently and consider premium positioning",
                    "priority": "high",
                    "category": "Menu Design",
                    "impact": "10-15% increase in average check",
                    "effort": "Menu redesign and staff training",
                    "timeline": "2-4 weeks",
                },
                {
                    "id": "rec_2",
                    "title": "Reengineer Plow Horse Items",
                    "description": "Reduce costs or increase prices on popular low-margin items",
                    "priority": "high",
                    "category": "Cost Management",
                    "impact": "5-8% margin improvement",
                    "effort": "Recipe optimization and supplier negotiation",
                    "timeline": "4-6 weeks",
                },
                {
                    "id": "rec_3",
                    "title": "Remove or Redesign Dog Items",
                    "description": "Eliminate or completely redesign underperforming menu items",
                    "priority": "medium",
                    "category": "Menu Simplification",
                    "impact": "Reduced complexity and food waste",
                    "effort": "Menu revision and kitchen workflow optimization",
                    "timeline": "6-8 weeks",
                },
            ],
            "menu_analysis": {
                "stars": stars,
                "plowhorses": plowhorses,
                "puzzles": puzzles,
                "dogs": dogs,
                "overall_metrics": {
                    "total_items": len(menu_items),
                    "avg_profit_margin": round(
                        sum(item["profit_margin"] for item in menu_items)
                        / len(menu_items),
                        3,
                    ),
                    "avg_popularity": round(
                        sum(item["popularity"] for item in menu_items)
                        / len(menu_items),
                        1,
                    ),
                },
            },
        },
        "metadata": {
            "requestId": str(uuid.uuid4()),
            "query": f"Menu optimization focused on {menu_focus}",
            "analysisTime": random.randint(120, 240),
            "confidence": round(random.uniform(0.85, 0.98), 2),
            "dataSourcesUsed": [
                "POS Data",
                "Recipe Costs",
                "MealMe",
                "Supplier Pricing",
            ],
            "limitations": [
                "Analysis based on historical performance data",
                "Seasonal menu variations not included",
                "Customer preference changes may affect future performance",
            ],
        },
    }

    return json.dumps(report_data, indent=2)


@mcp.tool()
def generate_sales_forecasting(
    forecast_period: str = "Next 3 months",
    forecast_type: str = "Revenue",
    seasonal_adjustment: str = "Include Seasonality",
) -> str:
    """
    Generate a sales forecasting report with predictive analytics.

    Args:
        forecast_period: Time period to forecast ("Next 3 months", "Next 6 months", "Next year")
        forecast_type: Type of forecast ("Revenue", "Customer Count", "Both")
        seasonal_adjustment: Whether to include seasonal factors ("Include Seasonality", "Exclude Seasonality")

    Returns:
        JSON string containing sales forecasting data
    """

    # Generate forecast data points
    base_revenue = 85000
    forecast_data = []

    periods = {"Next 3 months": 3, "Next 6 months": 6, "Next year": 12}
    num_periods = periods.get(forecast_period, 3)

    for i in range(num_periods):
        month_name = (datetime.now() + timedelta(days=30 * (i + 1))).strftime("%B %Y")

        # Apply seasonal adjustments and growth trends
        seasonal_factor = 1.0
        if seasonal_adjustment == "Include Seasonality":
            seasonal_factors = [
                0.95,
                1.0,
                1.05,
                1.1,
                1.15,
                1.2,
                1.15,
                1.1,
                1.05,
                1.0,
                0.95,
                0.9,
            ]
            current_month = (datetime.now().month + i) % 12
            seasonal_factor = seasonal_factors[current_month]

        growth_factor = 1 + (0.02 * (i + 1))  # 2% monthly growth
        forecast_revenue = base_revenue * seasonal_factor * growth_factor

        forecast_data.append(
            {
                "period": month_name,
                "forecasted_revenue": round(forecast_revenue, 2),
                "confidence_lower": round(forecast_revenue * 0.85, 2),
                "confidence_upper": round(forecast_revenue * 1.15, 2),
                "factors": {
                    "seasonal": round(seasonal_factor, 2),
                    "growth": round(growth_factor, 2),
                },
            }
        )

    report_data = {
        "id": str(uuid.uuid4()),
        "title": f"Sales Forecasting: {forecast_period}",
        "type": "sales_forecasting",
        "status": "completed",
        "createdAt": datetime.now().isoformat(),
        "completedAt": datetime.now().isoformat(),
        "summary": f"Predictive {forecast_type.lower()} analysis for {forecast_period.lower()} with {seasonal_adjustment.lower()}.",
        "content": {
            "executiveSummary": f"Sales forecasting model predicts steady growth over {forecast_period.lower()} with total projected revenue of ${sum(f['forecasted_revenue'] for f in forecast_data):,.0f}. Model incorporates seasonal patterns and market growth trends.",
            "keyFindings": [
                f"Projected {num_periods}-period revenue growth of {round((forecast_data[-1]['forecasted_revenue'] / base_revenue - 1) * 100, 1)}%",
                f"Peak revenue month expected to be {max(forecast_data, key=lambda x: x['forecasted_revenue'])['period']}",
                "Seasonal patterns indicate strong Q4 performance with holiday dining surge",
                "Market growth trends support continued expansion at current rate",
                f"Confidence intervals range from ±15% providing reliable planning framework",
            ],
            "recommendations": [
                {
                    "id": "rec_1",
                    "title": "Inventory Planning Optimization",
                    "description": "Adjust inventory levels based on forecasted demand patterns",
                    "priority": "high",
                    "category": "Operations",
                    "impact": "15-20% reduction in food waste",
                    "effort": "Implement demand-based ordering system",
                    "timeline": "Next 30 days",
                },
                {
                    "id": "rec_2",
                    "title": "Staffing Level Adjustments",
                    "description": "Align staffing schedules with predicted busy periods",
                    "priority": "medium",
                    "category": "Labor Management",
                    "impact": "Improved service quality and cost control",
                    "effort": "Workforce planning and scheduling optimization",
                    "timeline": "Ongoing monthly",
                },
            ],
            "forecast_data": forecast_data,
            "model_parameters": {
                "forecast_period": forecast_period,
                "forecast_type": forecast_type,
                "seasonal_adjustment": seasonal_adjustment,
                "base_revenue": base_revenue,
                "growth_assumption": "2% monthly",
            },
        },
        "metadata": {
            "requestId": str(uuid.uuid4()),
            "query": f"Sales forecasting for {forecast_period}",
            "analysisTime": random.randint(90, 180),
            "confidence": round(random.uniform(0.75, 0.90), 2),
            "dataSourcesUsed": [
                "Historical POS Data",
                "Market Trends",
                "Economic Indicators",
                "Seasonal Patterns",
            ],
            "limitations": [
                "Forecast accuracy decreases with longer time horizons",
                "External economic factors may impact projections",
                "Model assumes current operational capacity",
            ],
        },
    }

    return json.dumps(report_data, indent=2)


@mcp.tool()
def generate_customer_segmentation(
    segmentation_criteria: str = "RFM Analysis",
    customer_period: str = "Last 12 months",
    segment_focus: str = "All Customers",
) -> str:
    """
    Generate a customer segmentation report analyzing customer behavior patterns.

    Args:
        segmentation_criteria: Method for segmentation ("RFM Analysis", "Demographic", "Behavioral")
        customer_period: Time period for analysis ("Last 6 months", "Last 12 months", "All time")
        segment_focus: Customer subset to analyze ("All Customers", "Active Only", "High Value")

    Returns:
        JSON string containing customer segmentation data
    """

    segments = generate_customer_segments()

    # Add more detailed metrics to segments
    for segment in segments:
        segment.update(
            {
                "retention_rate": round(random.uniform(60, 85), 1),
                "growth_rate": round(random.uniform(-5, 15), 1),
                "lifetime_value": round(
                    segment["avg_spend"]
                    * (
                        12
                        / (
                            4
                            if segment["frequency"] == "Weekly"
                            else 1
                            if segment["frequency"] == "Monthly"
                            else 0.25
                        )
                    ),
                    2,
                ),
            }
        )

    report_data = {
        "id": str(uuid.uuid4()),
        "title": f"Customer Segmentation: {segmentation_criteria}",
        "type": "customer_segmentation",
        "status": "completed",
        "createdAt": datetime.now().isoformat(),
        "completedAt": datetime.now().isoformat(),
        "summary": f"Customer behavior analysis using {segmentation_criteria.lower()} over {customer_period.lower()}.",
        "content": {
            "executiveSummary": f"Customer segmentation reveals {len(segments)} distinct customer groups with significant variations in value and behavior. High-value segments represent 22% of customers but generate 45% of revenue.",
            "keyFindings": [
                f"Customer base segmented into {len(segments)} distinct behavioral groups",
                f"Top segment ({segments[0]['name']}) shows ${segments[0]['lifetime_value']:.0f} lifetime value",
                f"Retention rates vary from {min(s['retention_rate'] for s in segments)}% to {max(s['retention_rate'] for s in segments)}%",
                "Frequent diners demonstrate highest loyalty and spending patterns",
                "Significant opportunity exists to migrate occasional visitors to higher frequency",
            ],
            "recommendations": [
                {
                    "id": "rec_1",
                    "title": "VIP Program for Frequent Diners",
                    "description": "Implement loyalty rewards to maintain and grow high-value segment",
                    "priority": "high",
                    "category": "Customer Retention",
                    "impact": "15-25% increase in top-tier customer spending",
                    "effort": "Develop and launch loyalty program",
                    "timeline": "6-8 weeks",
                },
                {
                    "id": "rec_2",
                    "title": "Frequency Incentive Campaign",
                    "description": "Target occasional visitors with frequency-building promotions",
                    "priority": "medium",
                    "category": "Customer Development",
                    "impact": "10-15% improvement in visit frequency",
                    "effort": "Design and execute targeted marketing campaign",
                    "timeline": "4-6 weeks",
                },
                {
                    "id": "rec_3",
                    "title": "Special Occasion Marketing",
                    "description": "Develop premium experiences for celebration-focused customers",
                    "priority": "medium",
                    "category": "Revenue Optimization",
                    "impact": "Higher average check for special events",
                    "effort": "Create premium service packages",
                    "timeline": "8-10 weeks",
                },
            ],
            "segments": segments,
            "segment_analysis": {
                "total_customers": sum(s["size"] for s in segments),
                "total_revenue_represented": sum(
                    s["size"] * s["avg_spend"] for s in segments
                ),
                "high_value_percentage": round(
                    (segments[0]["size"] / sum(s["size"] for s in segments)) * 100, 1
                ),
            },
        },
        "metadata": {
            "requestId": str(uuid.uuid4()),
            "query": f"Customer segmentation using {segmentation_criteria}",
            "analysisTime": random.randint(150, 300),
            "confidence": round(random.uniform(0.80, 0.92), 2),
            "dataSourcesUsed": [
                "Customer Database",
                "POS Transaction Data",
                "Loyalty Program Data",
            ],
            "limitations": [
                "Analysis based on available customer data only",
                "Anonymous customers not included in segmentation",
                "Seasonal behavior patterns may affect segment stability",
            ],
        },
    }

    return json.dumps(report_data, indent=2)


@mcp.tool()
def generate_franchise_expansion(
    target_market: str,
    expansion_timeline: str = "18 months",
    investment_budget: str = "500000",
) -> str:
    """
    Generate a franchise expansion feasibility report for new market entry.

    Args:
        target_market: Target market for expansion (e.g., "Austin, TX", "Portland, OR")
        expansion_timeline: Timeline for expansion ("12 months", "18 months", "24 months")
        investment_budget: Available investment budget ("250000", "500000", "1000000")

    Returns:
        JSON string containing franchise expansion analysis
    """

    budget = float(investment_budget)

    # Generate market analysis data
    market_data = {
        "population": random.randint(500000, 2000000),
        "median_income": random.randint(45000, 85000),
        "restaurant_density": round(random.uniform(2.5, 8.5), 1),
        "growth_rate": round(random.uniform(1.5, 4.2), 1),
    }

    # Financial projections
    initial_investment = budget * 0.75
    working_capital = budget * 0.25
    projected_revenue_y1 = random.randint(800000, 1200000)
    projected_revenue_y2 = projected_revenue_y1 * random.uniform(1.15, 1.35)
    projected_revenue_y3 = projected_revenue_y2 * random.uniform(1.10, 1.25)

    break_even_month = random.randint(8, 16)
    roi_year_3 = ((projected_revenue_y3 * 0.15) / budget) * 100

    report_data = {
        "id": str(uuid.uuid4()),
        "title": f"Franchise Expansion: {target_market}",
        "type": "franchise_expansion",
        "status": "completed",
        "createdAt": datetime.now().isoformat(),
        "completedAt": datetime.now().isoformat(),
        "summary": f"Comprehensive feasibility analysis for franchise expansion into {target_market} with ${budget:,.0f} investment over {expansion_timeline}.",
        "content": {
            "executiveSummary": f"Market analysis of {target_market} indicates strong potential for franchise expansion. Projected break-even at month {break_even_month} with {roi_year_3:.1f}% ROI by year 3. Market fundamentals support sustainable growth with manageable competitive pressure.",
            "keyFindings": [
                f"Target market population of {market_data['population']:,} with median income ${market_data['median_income']:,}",
                f"Restaurant density of {market_data['restaurant_density']} per 1,000 residents indicates healthy market",
                f"Market growth rate of {market_data['growth_rate']}% annually supports expansion timing",
                f"Projected break-even achievement in month {break_even_month}",
                f"3-year ROI projection of {roi_year_3:.1f}% exceeds investment hurdle rate",
            ],
            "recommendations": [
                {
                    "id": "rec_1",
                    "title": "Proceed with Market Entry",
                    "description": "Market conditions and financial projections support expansion decision",
                    "priority": "high",
                    "category": "Strategic Decision",
                    "impact": f"${projected_revenue_y3:,.0f} annual revenue potential by year 3",
                    "effort": f"{expansion_timeline} development and launch timeline",
                    "timeline": "Begin site selection immediately",
                },
                {
                    "id": "rec_2",
                    "title": "Secure Prime Location",
                    "description": "Focus on high-traffic areas with demographic alignment",
                    "priority": "high",
                    "category": "Site Selection",
                    "impact": "15-25% premium on revenue potential",
                    "effort": "Comprehensive site evaluation and negotiation",
                    "timeline": "Next 90 days",
                },
                {
                    "id": "rec_3",
                    "title": "Local Partnership Strategy",
                    "description": "Consider local partnerships for market knowledge and community integration",
                    "priority": "medium",
                    "category": "Market Entry",
                    "impact": "Accelerated market penetration and local credibility",
                    "effort": "Partnership development and legal structuring",
                    "timeline": "Months 3-6",
                },
            ],
            "financial_projections": {
                "initial_investment": initial_investment,
                "working_capital": working_capital,
                "total_investment": budget,
                "revenue_projections": {
                    "year_1": round(projected_revenue_y1, 0),
                    "year_2": round(projected_revenue_y2, 0),
                    "year_3": round(projected_revenue_y3, 0),
                },
                "break_even_month": break_even_month,
                "roi_year_3": round(roi_year_3, 1),
            },
            "market_analysis": {
                "target_market": target_market,
                "market_data": market_data,
                "competitive_landscape": "Moderate competition with differentiation opportunities",
                "risk_factors": [
                    "Local competition response",
                    "Economic downturn impact",
                    "Regulatory changes",
                ],
            },
        },
        "metadata": {
            "requestId": str(uuid.uuid4()),
            "query": f"Franchise expansion feasibility for {target_market}",
            "analysisTime": random.randint(300, 480),
            "confidence": round(random.uniform(0.75, 0.88), 2),
            "dataSourcesUsed": [
                "Market Research",
                "Demographics",
                "Commercial Real Estate",
                "Financial Modeling",
            ],
            "limitations": [
                "Projections based on current market conditions",
                "Local regulatory requirements may affect timeline",
                "Economic factors may impact actual performance",
            ],
        },
    }

    return json.dumps(report_data, indent=2)


def main():
    """Main entry point for the restaurant intelligence server"""
    mcp.run(transport="stdio")


if __name__ == "__main__":
    main()
