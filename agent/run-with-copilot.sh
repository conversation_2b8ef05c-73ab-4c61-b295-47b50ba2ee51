#!/bin/bash

# Agent Run Script with Copilot API for Open Multi-Agent Canvas
# This script runs the agent backend using Copilot API as OpenAI backend

set -e  # Exit on any error

echo "🤖 Starting Open Multi-Agent Canvas Agent with Copilot API..."
echo "============================================================="

# Check if we're in the agent directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ pyproject.toml not found. Please run this script from the agent directory."
    exit 1
fi

# Check if GITHUB_TOKEN is set
if [ -z "$GITHUB_TOKEN" ]; then
    echo "❌ GITHUB_TOKEN environment variable is required"
    echo "   Please set your GitHub token:"
    echo "   export GITHUB_TOKEN=your_github_token_here"
    echo ""
    echo "   You can create a token at: https://github.com/settings/tokens"
    exit 1
fi

# Check if poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please run './install.sh' first."
    exit 1
fi

# Check if npx is available
if ! command -v npx &> /dev/null; then
    echo "❌ npx is not installed. Please install Node.js and npm first."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "📦 Virtual environment not found. Installing dependencies..."
    poetry install
fi

# Start Copilot API in background
echo "🚀 Starting Copilot API server on port 4141..."
npx copilot-api@latest start --github-token "$GITHUB_TOKEN" --port 4141 &
COPILOT_PID=$!

# Wait for Copilot API to start
echo "⏳ Waiting for Copilot API to initialize..."
sleep 5

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping Copilot API server..."
    kill $COPILOT_PID 2>/dev/null || true
    pkill -f "copilot-api" 2>/dev/null || true
    echo "✅ Cleanup complete"
}

# Set trap to cleanup on script exit
trap cleanup EXIT INT TERM

# Check if Copilot API is running
if ! curl -s http://localhost:4141 > /dev/null; then
    echo "❌ Copilot API failed to start on port 4141"
    exit 1
fi

echo "✅ Copilot API is running at http://localhost:4141"

# Set environment variables for OpenAI compatibility
export OPENAI_API_KEY="dummy"
export OPENAI_MODEL="claude-sonnet-4"
export OPENAI_BASE_URL="http://localhost:4141"

# Set default host and port for LangGraph
HOST=${HOST:-"localhost"}
PORT=${PORT:-"8123"}

echo ""
echo "🔧 Configuration:"
echo "   OPENAI_API_KEY: $OPENAI_API_KEY"
echo "   OPENAI_MODEL: $OPENAI_MODEL"
echo "   OPENAI_BASE_URL: $OPENAI_BASE_URL"
echo ""
echo "🚀 Starting LangGraph development server..."
echo "🌐 Agent backend will be available at: http://$HOST:$PORT"
echo "🤖 Using Copilot API as OpenAI backend"
echo ""
echo "Press Ctrl+C to stop both servers"
echo ""

# Start the LangGraph development server
poetry run langgraph dev --host "$HOST" --port "$PORT" --no-browser
