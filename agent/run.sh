#!/bin/bash

# Agent Run Script for Open Multi-Agent Canvas
# This script runs the agent backend using LangGraph

set -e  # Exit on any error

echo "🤖 Starting Open Multi-Agent Canvas Agent Backend..."
echo "===================================================="

# Check if we're in the agent directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ pyproject.toml not found. Please run this script from the agent directory."
    exit 1
fi

# Check if poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please run './install.sh' first."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "📦 Virtual environment not found. Installing dependencies..."
    poetry install
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found!"
    if [ -f "example.env" ]; then
        echo "📄 Creating .env from example.env..."
        cp example.env .env
        echo "⚠️  Please edit .env file with your API keys and restart."
        echo "   Required: OPENAI_API_KEY"
        echo "   Optional: LANGSMITH_API_KEY"
        exit 1
    else
        echo "❌ No .env or example.env found. Please create .env file with required variables."
        exit 1
    fi
fi

# Check for required environment variables
source .env

# Auto-detect backend based on configuration
if [ "$OPENAI_BASE_URL" = "http://localhost:4141" ] || [ "$OPENAI_API_KEY" = "dummy" ]; then
    echo "🔧 Detected Copilot API configuration"
    if [ -z "$GITHUB_TOKEN" ] || [ "$GITHUB_TOKEN" = "your_github_token_here" ]; then
        echo "⚠️  Copilot API backend requires GITHUB_TOKEN in .env file"
        echo "   Please set your GitHub token in .env file"
        echo "   Get token from: https://github.com/settings/tokens"
        exit 1
    fi
    
    # Check if Copilot API is running
    if ! curl -s http://localhost:4141 > /dev/null 2>&1; then
        echo "⚠️  Copilot API not running. Starting automatically..."
        if command -v ../scripts/copilot-api.sh &> /dev/null; then
            ../scripts/copilot-api.sh ensure
        else
            echo "❌ Cannot start Copilot API automatically"
            echo "   Please run: make start-copilot-api"
            exit 1
        fi
    fi
else
    echo "🔧 Detected traditional OpenAI configuration"
    if [ -z "$OPENAI_API_KEY" ] || [ "$OPENAI_API_KEY" = "your_openai_api_key_here" ]; then
        echo "⚠️  Please set OPENAI_API_KEY in .env file"
        echo "   Get your API key from: https://platform.openai.com/api-keys"
        exit 1
    fi
fi

# Check if langgraph.json exists
if [ ! -f "langgraph.json" ]; then
    echo "⚠️  langgraph.json not found. This may affect agent functionality."
fi

# Set default host and port
HOST=${HOST:-"localhost"}
PORT=${PORT:-"8123"}

echo ""
echo "🚀 Starting LangGraph development server..."
echo "🌐 Agent backend will be available at: http://$HOST:$PORT"
echo "📊 LangGraph Studio will be available for debugging"
echo ""

# Show which backend is being used
if [ "$OPENAI_BASE_URL" = "http://localhost:4141" ] || [ "$OPENAI_API_KEY" = "dummy" ]; then
    echo "🤖 Using Copilot API as OpenAI backend (http://localhost:4141)"
    echo "   Model: ${OPENAI_MODEL:-claude-sonnet-4}"
else
    echo "🔑 Using traditional OpenAI API"
    echo "   Model: ${OPENAI_MODEL:-gpt-4}"
fi

echo ""
echo "Environment variables loaded from .env"
echo "Press Ctrl+C to stop the server"
echo ""

# Start the LangGraph development server
poetry run langgraph dev --host "$HOST" --port "$PORT" --no-browser
