/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?6a4d\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_app_render_strip_flight_headers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/app-render/strip-flight-headers */ \"(rsc)/./node_modules/next/dist/server/app-render/strip-flight-headers.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_27___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_27__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig, interceptionRoutePatterns } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_14__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = routeModule.match(pathname, prerenderManifest);\n    const isPrerendered = !!prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.NEXT_ROUTER_PREFETCH_HEADER] === '1' // exclude runtime prefetches, which use '2'\n    ;\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_15__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_9__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_12__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    // If this is a request for an app path that should be statically generated\n    // and we aren't in the edge runtime, strip the flight headers so it will\n    // generate the static response.\n    if (!routeModule.isDev && !isDraftMode && isSSG && isRSCRequest && !isDynamicRSCRequest) {\n        (0,next_dist_server_app_render_strip_flight_headers__WEBPACK_IMPORTED_MODULE_7__.stripFlightHeaders)(req.headers);\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_26__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_25___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_11__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_13__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const varyHeader = routeModule.getVaryHeader(resolvedPathname, interceptionRoutePatterns);\n        res.setHeader('Vary', varyHeader);\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_8__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.cacheComponents && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir:  true ? (__webpack_require__(/*! path */ \"path\").join)(/* turbopackIgnore: true */ process.cwd(), routeModule.relativeProjectDir) : 0,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        clientParamParsing: Boolean(nextConfig.experimental.clientParamParsing),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a HTML bot request, we want to serve a blocking render and\n            // not the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_17__.isBot)(userAgent)) {\n                if (!isRoutePPREnabled || isHtmlBot) {\n                    fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.BLOCKING_STATIC_RENDER;\n                }\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_19__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    const cacheKey = typeof (prerenderInfo == null ? void 0 : prerenderInfo.fallback) === 'string' ? prerenderInfo.fallback : isProduction ? normalizedSrcPage : null;\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_10__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__[\"default\"].EMPTY,\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_10__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                        req,\n                        res,\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__[\"default\"].fromStatic(matchedSegment, next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.RSC_CONTENT_TYPE_HEADER),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__[\"default\"].EMPTY,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_21__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_27__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                        req,\n                        res,\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_20__[\"default\"].fromStatic(cachedData.rscData, next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_16__.RSC_CONTENT_TYPE_HEADER),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode || isRSCRequest) {\n                // If we're in test mode, we should add a sentinel chunk to the response\n                // that's between the static and dynamic parts so we can compare the\n                // chunks and add assertions.\n                if (false) {}\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.push(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_22__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // If we're in test mode, we should add a sentinel chunk to the response\n            // that's between the static and dynamic parts so we can compare the\n            // chunks and add assertions.\n            if (false) {}\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.push(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_18__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_23__.sendRenderResult)({\n                req,\n                res,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_24__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n// TODO: omit this from production builds, only test builds should include it\n/**\n * Creates a readable stream that emits a PPR boundary sentinel.\n *\n * @returns A readable stream that emits a PPR boundary sentinel.\n */ function createPPRBoundarySentinel() {\n    return new ReadableStream({\n        start (controller) {\n            controller.enqueue(new TextEncoder().encode('<!-- PPR_BOUNDARY_SENTINEL -->'));\n            controller.close();\n        }\n    });\n}\n\n//# sourceMappingURL=app-page.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/framework/boundary-components.js */ \"(rsc)/./node_modules/next/dist/lib/framework/boundary-components.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fproviders%2FProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fproviders%2FProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/Providers.tsx */ \"(rsc)/./src/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fproviders%2FProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRnRlYW1zcGFjZSUyRnN0dWRpb3MlMkZ0aGlzX3N0dWRpbyUyRm9wZW4tbXVsdGktYWdlbnQtY2FudmFzJTJGYml0ZWJhc2VfYWdlbnQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTJIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi90ZWFtc3BhY2Uvc3R1ZGlvcy90aGlzX3N0dWRpby9vcGVuLW11bHRpLWFnZW50LWNhbnZhcy9iaXRlYmFzZV9hZ2VudC9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b25650b01208\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImIyNTY1MGIwMTIwOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/Providers */ \"(rsc)/./src/providers/Providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"BiteBase Agent - Restaurant Intelligence\",\n    description: \"Restaurant Intelligence Agent powered by CopilotKit\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_Providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js\");\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);\n// This file is generated by the Webpack next-flight-loader.\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBIiwic291cmNlcyI6WyJfTl9FLy4vc3JjL2FwcC9wYWdlLnRzeC9fX25leHRqcy1pbnRlcm5hbC1wcm94eS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhpcyBmaWxlIGlzIGdlbmVyYXRlZCBieSB0aGUgV2VicGFjayBuZXh0LWZsaWdodC1sb2FkZXIuXG5pbXBvcnQgeyByZWdpc3RlckNsaWVudFJlZmVyZW5jZSB9IGZyb20gXCJyZWFjdC1zZXJ2ZXItZG9tLXdlYnBhY2svc2VydmVyXCI7XG5leHBvcnQgZGVmYXVsdCByZWdpc3RlckNsaWVudFJlZmVyZW5jZShcbmZ1bmN0aW9uKCkgeyB0aHJvdyBuZXcgRXJyb3IoXCJBdHRlbXB0ZWQgdG8gY2FsbCB0aGUgZGVmYXVsdCBleHBvcnQgb2YgXFxcIi90ZWFtc3BhY2Uvc3R1ZGlvcy90aGlzX3N0dWRpby9vcGVuLW11bHRpLWFnZW50LWNhbnZhcy9iaXRlYmFzZV9hZ2VudC9zcmMvYXBwL3BhZ2UudHN4XFxcIiBmcm9tIHRoZSBzZXJ2ZXIsIGJ1dCBpdCdzIG9uIHRoZSBjbGllbnQuIEl0J3Mgbm90IHBvc3NpYmxlIHRvIGludm9rZSBhIGNsaWVudCBmdW5jdGlvbiBmcm9tIHRoZSBzZXJ2ZXIsIGl0IGNhbiBvbmx5IGJlIHJlbmRlcmVkIGFzIGEgQ29tcG9uZW50IG9yIHBhc3NlZCB0byBwcm9wcyBvZiBhIENsaWVudCBDb21wb25lbnQuXCIpOyB9LFxuXCIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL2FwcC9wYWdlLnRzeFwiLFxuXCJkZWZhdWx0XCIsXG4pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/providers/Providers.tsx":
/*!*************************************!*\
  !*** ./src/providers/Providers.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js\");\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);\n// This file is generated by the Webpack next-flight-loader.\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/providers/Providers.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/providers/Providers.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvcHJvdmlkZXJzL1Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEiLCJzb3VyY2VzIjpbIl9OX0UvLi9zcmMvcHJvdmlkZXJzL1Byb3ZpZGVycy50c3gvX19uZXh0anMtaW50ZXJuYWwtcHJveHkubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgZmlsZSBpcyBnZW5lcmF0ZWQgYnkgdGhlIFdlYnBhY2sgbmV4dC1mbGlnaHQtbG9hZGVyLlxuaW1wb3J0IHsgcmVnaXN0ZXJDbGllbnRSZWZlcmVuY2UgfSBmcm9tIFwicmVhY3Qtc2VydmVyLWRvbS13ZWJwYWNrL3NlcnZlclwiO1xuZXhwb3J0IGRlZmF1bHQgcmVnaXN0ZXJDbGllbnRSZWZlcmVuY2UoXG5mdW5jdGlvbigpIHsgdGhyb3cgbmV3IEVycm9yKFwiQXR0ZW1wdGVkIHRvIGNhbGwgdGhlIGRlZmF1bHQgZXhwb3J0IG9mIFxcXCIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL3Byb3ZpZGVycy9Qcm92aWRlcnMudHN4XFxcIiBmcm9tIHRoZSBzZXJ2ZXIsIGJ1dCBpdCdzIG9uIHRoZSBjbGllbnQuIEl0J3Mgbm90IHBvc3NpYmxlIHRvIGludm9rZSBhIGNsaWVudCBmdW5jdGlvbiBmcm9tIHRoZSBzZXJ2ZXIsIGl0IGNhbiBvbmx5IGJlIHJlbmRlcmVkIGFzIGEgQ29tcG9uZW50IG9yIHBhc3NlZCB0byBwcm9wcyBvZiBhIENsaWVudCBDb21wb25lbnQuXCIpOyB9LFxuXCIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL3Byb3ZpZGVycy9Qcm92aWRlcnMudHN4XCIsXG5cImRlZmF1bHRcIixcbik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/providers/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/framework/boundary-components.js */ \"(ssr)/./node_modules/next/dist/lib/framework/boundary-components.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fbuiltin%2Fglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fframework%2Fboundary-components.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fproviders%2FProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fproviders%2FProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/Providers.tsx */ \"(ssr)/./src/providers/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-jetbrains%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fproviders%2FProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRnRlYW1zcGFjZSUyRnN0dWRpb3MlMkZ0aGlzX3N0dWRpbyUyRm9wZW4tbXVsdGktYWdlbnQtY2FudmFzJTJGYml0ZWJhc2VfYWdlbnQlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTJIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-QNJNOZH3.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-JEMBA7PE.mjs\");\n/* harmony import */ var _components_canvas__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/canvas */ \"(ssr)/./src/components/canvas.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen gap-16 font-[family-name:var(--font-geist-sans)]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_canvas__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\nfunction YourMainContent({ themeColor }) {\n    // 🪁 Shared State: https://docs.copilotkit.ai/coagents/shared-state\n    const { state, setState } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_2__.useCoAgent)({\n        name: \"starterAgent\",\n        initialState: {\n            proverbs: [\n                \"CopilotKit may be new, but its the best thing since sliced bread.\"\n            ]\n        }\n    });\n    // 🪁 Frontend Actions: https://docs.copilotkit.ai/coagents/frontend-actions\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__.useCopilotAction)({\n        name: \"addProverb\",\n        description: \"Add a proverb to the list.\",\n        parameters: [\n            {\n                name: \"proverb\",\n                description: \"The proverb to add. Make it witty, short and concise.\",\n                required: true\n            }\n        ],\n        handler: {\n            \"YourMainContent.useCopilotAction\": ({ proverb })=>{\n                setState({\n                    ...state,\n                    proverbs: [\n                        ...state.proverbs,\n                        proverb\n                    ]\n                });\n            }\n        }[\"YourMainContent.useCopilotAction\"]\n    });\n    //🪁 Generative UI: https://docs.copilotkit.ai/coagents/generative-ui\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__.useCopilotAction)({\n        name: \"getWeather\",\n        description: \"Get the weather for a given location.\",\n        available: \"disabled\",\n        parameters: [\n            {\n                name: \"location\",\n                type: \"string\",\n                required: true\n            }\n        ],\n        render: {\n            \"YourMainContent.useCopilotAction\": ({ args })=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WeatherCard, {\n                    location: args.location,\n                    themeColor: themeColor\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 14\n                }, this);\n            }\n        }[\"YourMainContent.useCopilotAction\"]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            backgroundColor: themeColor\n        },\n        className: \"h-screen w-screen flex justify-center items-center flex-col transition-colors duration-300\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white/20 backdrop-blur-md p-8 rounded-2xl shadow-xl max-w-2xl w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-white mb-2 text-center\",\n                    children: \"Proverbs\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-200 text-center italic mb-6\",\n                    children: \"This is a demonstrative page, but it could be anything you want! \\uD83E\\uDE81\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                    className: \"border-white/20 my-6\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-3\",\n                    children: state.proverbs?.map((proverb, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/15 p-4 rounded-xl text-white relative group hover:bg-white/20 transition-all\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"pr-8\",\n                                    children: proverb\n                                }, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setState({\n                                            ...state,\n                                            proverbs: state.proverbs?.filter((_, i)=>i !== index)\n                                        }),\n                                    className: \"absolute right-3 top-3 opacity-0 group-hover:opacity-100 transition-opacity  bg-red-500 hover:bg-red-600 text-white rounded-full h-6 w-6 flex items-center justify-center\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                state.proverbs?.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-white/80 italic my-8\",\n                    children: \"No proverbs yet. Ask the assistant to add some!\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 42\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n// Simple sun icon for the weather card\nfunction SunIcon() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        className: \"w-14 h-14 text-yellow-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"5\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\",\n                strokeWidth: \"2\",\n                stroke: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n// Weather card component where the location and themeColor are based on what the agent\n// sets via tool calls.\nfunction WeatherCard({ location, themeColor }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            backgroundColor: themeColor\n        },\n        className: \"rounded-xl shadow-xl mt-6 mb-4 max-w-md w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white/20 p-4 w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white capitalize\",\n                                    children: location\n                                }, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white\",\n                                    children: \"Current Weather\"\n                                }, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SunIcon, {}, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex items-end justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold text-white\",\n                            children: \"70\\xb0\"\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-white\",\n                            children: \"Clear skies\"\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 pt-4 border-t border-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-2 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-xs\",\n                                        children: \"Humidity\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"45%\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-xs\",\n                                        children: \"Wind\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"5 mph\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white text-xs\",\n                                        children: \"Feels Like\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"72\\xb0\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n            lineNumber: 118,\n            columnNumber: 5\n        }, this)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/app/page.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/agents/index.tsx":
/*!*****************************************!*\
  !*** ./src/components/agents/index.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIResearchAgent: () => (/* reexport safe */ _researcher__WEBPACK_IMPORTED_MODULE_1__.AIResearchAgent),\n/* harmony export */   MCPAgent: () => (/* reexport safe */ _mcp_agent__WEBPACK_IMPORTED_MODULE_2__.MCPAgent),\n/* harmony export */   RestaurantIntelligenceAgent: () => (/* reexport safe */ _restaurant_intelligence__WEBPACK_IMPORTED_MODULE_0__.RestaurantIntelligenceAgent)\n/* harmony export */ });\n/* harmony import */ var _restaurant_intelligence__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./restaurant-intelligence */ \"(ssr)/./src/components/agents/restaurant-intelligence.tsx\");\n/* harmony import */ var _researcher__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./researcher */ \"(ssr)/./src/components/agents/researcher.tsx\");\n/* harmony import */ var _mcp_agent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mcp-agent */ \"(ssr)/./src/components/agents/mcp-agent.tsx\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hZ2VudHMvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUNiO0FBQ0QiLCJzb3VyY2VzIjpbIi90ZWFtc3BhY2Uvc3R1ZGlvcy90aGlzX3N0dWRpby9vcGVuLW11bHRpLWFnZW50LWNhbnZhcy9iaXRlYmFzZV9hZ2VudC9zcmMvY29tcG9uZW50cy9hZ2VudHMvaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL3Jlc3RhdXJhbnQtaW50ZWxsaWdlbmNlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9yZXNlYXJjaGVyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9tY3AtYWdlbnRcIjsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/agents/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/agents/mcp-agent.tsx":
/*!*********************************************!*\
  !*** ./src/components/agents/mcp-agent.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MCPAgent: () => (/* binding */ MCPAgent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/available-agents */ \"(ssr)/./src/lib/available-agents.ts\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-QNJNOZH3.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-2QZSAQTX.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _lib_mcp_config_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mcp-config-types */ \"(ssr)/./src/lib/mcp-config-types.ts\");\n/* harmony import */ var _hooks_use_local_storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-local-storage */ \"(ssr)/./src/hooks/use-local-storage.tsx\");\n\n\n\n\n\n\n\n\nconst MCPAgent = ()=>{\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const isProcessing = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Use ref to avoid re-rendering issues\n    const configsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Get saved MCP configurations from localStorage\n    const [savedConfigs] = (0,_hooks_use_local_storage__WEBPACK_IMPORTED_MODULE_4__.useLocalStorage)(_lib_mcp_config_types__WEBPACK_IMPORTED_MODULE_3__.MCP_STORAGE_KEY, {});\n    // Set the ref value once we have the saved configs\n    if (Object.keys(savedConfigs).length > 0 && Object.keys(configsRef.current).length === 0) {\n        configsRef.current = savedConfigs;\n    }\n    const { state: mcpAgentState, stop: stopMcpAgent } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCoAgent)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.MCP_AGENT,\n        initialState: {\n            response: \"\",\n            logs: [],\n            mcp_config: configsRef.current\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MCPAgent.useEffect\": ()=>{\n            if (mcpAgentState.logs) {\n                setLogs({\n                    \"MCPAgent.useEffect\": (prevLogs)=>{\n                        const newLogs = [\n                            ...prevLogs\n                        ];\n                        mcpAgentState.logs.forEach({\n                            \"MCPAgent.useEffect\": (log)=>{\n                                const existingLogIndex = newLogs.findIndex({\n                                    \"MCPAgent.useEffect.existingLogIndex\": (l)=>l.message === log.message\n                                }[\"MCPAgent.useEffect.existingLogIndex\"]);\n                                if (existingLogIndex >= 0) {\n                                    if (log.done && !newLogs[existingLogIndex].done) {\n                                        newLogs[existingLogIndex].done = true;\n                                    }\n                                } else {\n                                    newLogs.push(log);\n                                }\n                            }\n                        }[\"MCPAgent.useEffect\"]);\n                        return newLogs;\n                    }\n                }[\"MCPAgent.useEffect\"]);\n            }\n        }\n    }[\"MCPAgent.useEffect\"], [\n        mcpAgentState.logs\n    ]);\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_6__.useCoAgentStateRender)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.MCP_AGENT,\n        handler: {\n            \"MCPAgent.useCoAgentStateRender\": ({ nodeName })=>{\n                if (nodeName === \"__end__\") {\n                    setTimeout({\n                        \"MCPAgent.useCoAgentStateRender\": ()=>{\n                            stopMcpAgent();\n                        }\n                    }[\"MCPAgent.useCoAgentStateRender\"], 1000);\n                }\n            }\n        }[\"MCPAgent.useCoAgentStateRender\"],\n        render: {\n            \"MCPAgent.useCoAgentStateRender\": ({ status })=>{\n                if (status === \"inProgress\") {\n                    isProcessing.current = true;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"Processing your request...\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: logs.map({\n                                    \"MCPAgent.useCoAgentStateRender\": (log, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `mr-2 ${log.done ? \"text-green-500\" : \"text-gray-400\"}`,\n                                                    children: log.done ? \"✓\" : \"⟳\"\n                                                }, void 0, false, {\n                                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: log.message\n                                                }, void 0, false, {\n                                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, idx, true, {\n                                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, undefined)\n                                }[\"MCPAgent.useCoAgentStateRender\"])\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined);\n                }\n                if (status === \"complete\") {\n                    isProcessing.current = false;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-green-600 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Processing complete\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, undefined);\n                }\n            }\n        }[\"MCPAgent.useCoAgentStateRender\"]\n    });\n    if (isProcessing.current) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-4 h-full z-[999]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse p-6 bg-white rounded-lg shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-gray-200 rounded w-1/3 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-5/6 mb-2\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-full mb-2\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded w-4/6 mb-2\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!mcpAgentState.response) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4 h-full z-[999]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-2 p-6 bg-white rounded-lg shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                className: \"prose prose-sm md:prose-base lg:prose-lg prose-slate max-w-none bg-gray-50 p-6 rounded-lg border border-gray-200\",\n                components: {\n                    h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold mb-6 pb-2 border-b\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 15\n                        }, void 0),\n                    h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4 mt-8\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 15\n                        }, void 0),\n                    h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold mb-3 mt-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 15\n                        }, void 0),\n                    p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 leading-relaxed\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, void 0),\n                    ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc pl-6 mb-4 space-y-2\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 15\n                        }, void 0),\n                    ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal pl-6 mb-4 space-y-2\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 15\n                        }, void 0),\n                    blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                            className: \"border-l-4 border-gray-300 pl-4 py-2 my-6 bg-gray-50 rounded-r\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 15\n                        }, void 0)\n                },\n                children: mcpAgentState.response\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/mcp-agent.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/agents/mcp-agent.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/agents/researcher.tsx":
/*!**********************************************!*\
  !*** ./src/components/agents/researcher.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIResearchAgent: () => (/* binding */ AIResearchAgent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_research_logs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/research-logs */ \"(ssr)/./src/components/research-logs.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/skeletons */ \"(ssr)/./src/components/skeletons/index.tsx\");\n/* harmony import */ var _lib_available_agents__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/available-agents */ \"(ssr)/./src/lib/available-agents.ts\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-QNJNOZH3.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-2QZSAQTX.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n\n\n\n\n\n\n\n\nconst AIResearchAgent = ()=>{\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const isResearchInProgress = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(false);\n    const { state: researchAgentState, stop: stopResearchAgent } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCoAgent)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_3__.AvailableAgents.RESEARCH_AGENT,\n        initialState: {\n            model: \"openai\",\n            research_question: \"\",\n            resources: [],\n            report: \"\",\n            logs: []\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"AIResearchAgent.useEffect\": ()=>{\n            if (researchAgentState.logs) {\n                setLogs({\n                    \"AIResearchAgent.useEffect\": (prevLogs)=>{\n                        const newLogs = [\n                            ...prevLogs\n                        ];\n                        researchAgentState.logs.forEach({\n                            \"AIResearchAgent.useEffect\": (log)=>{\n                                const existingLogIndex = newLogs.findIndex({\n                                    \"AIResearchAgent.useEffect.existingLogIndex\": (l)=>l.message === log.message\n                                }[\"AIResearchAgent.useEffect.existingLogIndex\"]);\n                                if (existingLogIndex >= 0) {\n                                    // Only update done status if changing from false to true\n                                    if (log.done && !newLogs[existingLogIndex].done) {\n                                        newLogs[existingLogIndex].done = true;\n                                    }\n                                } else {\n                                    newLogs.push(log);\n                                }\n                            }\n                        }[\"AIResearchAgent.useEffect\"]);\n                        return newLogs;\n                    }\n                }[\"AIResearchAgent.useEffect\"]);\n            }\n        }\n    }[\"AIResearchAgent.useEffect\"], [\n        researchAgentState.logs\n    ]);\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_6__.useCoAgentStateRender)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_3__.AvailableAgents.RESEARCH_AGENT,\n        handler: {\n            \"AIResearchAgent.useCoAgentStateRender\": ({ nodeName })=>{\n                // HACK nodeName __end__ stop the research agent\n                if (nodeName === \"__end__\") {\n                    setTimeout({\n                        \"AIResearchAgent.useCoAgentStateRender\": ()=>{\n                            stopResearchAgent();\n                        }\n                    }[\"AIResearchAgent.useCoAgentStateRender\"], 1000);\n                }\n            }\n        }[\"AIResearchAgent.useCoAgentStateRender\"],\n        render: {\n            \"AIResearchAgent.useCoAgentStateRender\": ({ status })=>{\n                if (status === \"inProgress\") {\n                    isResearchInProgress.current = true;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_research_logs__WEBPACK_IMPORTED_MODULE_1__.ResearchLogs, {\n                        logs: logs ?? []\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 16\n                    }, undefined);\n                }\n                if (status === \"complete\") {\n                    isResearchInProgress.current = false;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose max-w-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-green-600 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Research complete\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined);\n                }\n            }\n        }[\"AIResearchAgent.useCoAgentStateRender\"]\n    });\n    if (isResearchInProgress.current) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-4 h-full z-[999]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_2__.ResearchPaperSkeleton, {}, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!researchAgentState.report) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4 h-full z-[999]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-2 p-6 bg-white rounded-lg shadow-sm\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                    className: \"prose prose-sm md:prose-base lg:prose-lg prose-slate max-w-none bg-gray-50 p-6 rounded-lg border border-gray-200\",\n                    components: {\n                        h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-6 pb-2 border-b\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, void 0),\n                        h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-4 mt-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, void 0),\n                        h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold mb-3 mt-6\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, void 0),\n                        p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-4 leading-relaxed\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, void 0),\n                        ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc pl-6 mb-4 space-y-2\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, void 0),\n                        ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal pl-6 mb-4 space-y-2\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, void 0),\n                        blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-gray-300 pl-4 py-2 my-6 bg-gray-50 rounded-r\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, void 0)\n                    },\n                    children: researchAgentState.report\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, undefined),\n                researchAgentState.resources && researchAgentState.resources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"prose max-w-none z-[999] bg-gray-50 p-6 rounded-lg border border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4 mt-8\",\n                            children: \"Resources\"\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc pl-6 mb-4 space-y-2\",\n                            children: researchAgentState.resources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"text-gray-700\",\n                                    children: [\n                                        resource.url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: resource.url,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-blue-600 hover:underline\",\n                                            children: resource.title || resource.url\n                                        }, void 0, false, {\n                                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 23\n                                        }, undefined) : resource.title,\n                                        resource.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mt-1\",\n                                            children: resource.description\n                                        }, void 0, false, {\n                                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/researcher.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/agents/researcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/agents/restaurant-intelligence.tsx":
/*!***********************************************************!*\
  !*** ./src/components/agents/restaurant-intelligence.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RestaurantIntelligenceAgent: () => (/* binding */ RestaurantIntelligenceAgent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_restaurant_dashboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/restaurant-dashboard */ \"(ssr)/./src/components/restaurant-dashboard.tsx\");\n\n\nconst RestaurantIntelligenceAgent = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_restaurant_dashboard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/agents/restaurant-intelligence.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hZ2VudHMvcmVzdGF1cmFudC1pbnRlbGxpZ2VuY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW9FO0FBRTdELE1BQU1DLDhCQUE4QjtJQUN6QyxxQkFBTyw4REFBQ0Qsd0VBQW1CQTs7Ozs7QUFDN0IsRUFBRSIsInNvdXJjZXMiOlsiL3RlYW1zcGFjZS9zdHVkaW9zL3RoaXNfc3R1ZGlvL29wZW4tbXVsdGktYWdlbnQtY2FudmFzL2JpdGViYXNlX2FnZW50L3NyYy9jb21wb25lbnRzL2FnZW50cy9yZXN0YXVyYW50LWludGVsbGlnZW5jZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlc3RhdXJhbnREYXNoYm9hcmQgZnJvbSBcIkAvY29tcG9uZW50cy9yZXN0YXVyYW50LWRhc2hib2FyZFwiO1xuXG5leHBvcnQgY29uc3QgUmVzdGF1cmFudEludGVsbGlnZW5jZUFnZW50ID0gKCkgPT4ge1xuICByZXR1cm4gPFJlc3RhdXJhbnREYXNoYm9hcmQgLz47XG59OyJdLCJuYW1lcyI6WyJSZXN0YXVyYW50RGFzaGJvYXJkIiwiUmVzdGF1cmFudEludGVsbGlnZW5jZUFnZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/agents/restaurant-intelligence.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/canvas.tsx":
/*!***********************************!*\
  !*** ./src/components/canvas.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Canvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_agents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/agents */ \"(ssr)/./src/components/agents/index.tsx\");\n/* harmony import */ var _components_skeletons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/skeletons */ \"(ssr)/./src/components/skeletons/index.tsx\");\n/* harmony import */ var _lib_available_agents__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/available-agents */ \"(ssr)/./src/lib/available-agents.ts\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-QNJNOZH3.mjs\");\n/* harmony import */ var _barrel_optimize_names_CircleOff_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CircleOff,Loader2,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_CircleOff_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CircleOff,Loader2,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_CircleOff_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CircleOff,Loader2,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-off.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _chat_window__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chat-window */ \"(ssr)/./src/components/chat-window.tsx\");\n/* harmony import */ var _mcp_config_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./mcp-config-modal */ \"(ssr)/./src/components/mcp-config-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst getCurrentlyRunningAgent = (state)=>{\n    return state.find((agent)=>agent.status);\n};\nconst DefaultView = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center h-full text-gray-600\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-2xl text-center font-serif italic max-w-3xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                    children: \"Powered by CopilotKit \\uD83E\\uDE81\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 7\n                }, undefined),\n                \"Start a conversation in the chat to analyze restaurant performance, research market trends, or use the MCP agent for other tasks!\"\n            ]\n        }, void 0, true, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n            lineNumber: 24,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined);\nfunction Canvas() {\n    const [showMCPConfigModal, setShowMCPConfigModal] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const { running: restaurantIntelligenceRunning, name: restaurantIntelligenceName, nodeName: restaurantIntelligenceNodeName } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_7__.useCoAgent)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_3__.AvailableAgents.RESTAURANT_INTELLIGENCE\n    });\n    const { running: aiResearchAgentRunning, name: aiResearchAgentName, nodeName: aiResearchAgentNodeName } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_7__.useCoAgent)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_3__.AvailableAgents.RESEARCH_AGENT\n    });\n    const { running: mcpAgentRunning, name: mcpAgentName, nodeName: mcpAgentNodeName } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_7__.useCoAgent)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_3__.AvailableAgents.MCP_AGENT\n    });\n    const currentlyRunningAgent = getCurrentlyRunningAgent([\n        {\n            status: restaurantIntelligenceRunning,\n            name: restaurantIntelligenceName,\n            nodeName: restaurantIntelligenceNodeName ?? \"\"\n        },\n        {\n            status: aiResearchAgentRunning,\n            name: aiResearchAgentName,\n            nodeName: aiResearchAgentNodeName ?? \"\"\n        },\n        {\n            status: mcpAgentRunning,\n            name: mcpAgentName,\n            nodeName: mcpAgentNodeName ?? \"\"\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative h-full w-full grid grid-cols-1 md:grid-cols-12\",\n        children: [\n            currentlyRunningAgent?.status ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-full shadow-lg animate-pulse z-[9999]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-bold\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleOff_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"inline-block w-4 h-4 mr-2 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            currentlyRunningAgent.name,\n                            \" agent executing\",\n                            \" \",\n                            currentlyRunningAgent.nodeName,\n                            \" node\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this),\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 flex gap-2 z-[9999]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowMCPConfigModal(true),\n                        className: \"bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-full shadow-lg flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleOff_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"MCP Servers\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-600 text-white px-4 py-2 rounded-full shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleOff_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"inline-block w-4 h-4 mr-2 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-bold\",\n                                children: \"Multi-Agent\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"order-last md:order-first md:col-span-4 p-4 border-r h-screen overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chat_window__WEBPACK_IMPORTED_MODULE_5__.ChatWindow, {}, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"order-first md:order-last md:col-span-8 bg-white p-8 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8 h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_4__.Suspense, {\n                        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_skeletons__WEBPACK_IMPORTED_MODULE_2__.EmailListSkeleton, {}, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 31\n                        }, void 0),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_agents__WEBPACK_IMPORTED_MODULE_1__.RestaurantIntelligenceAgent, {}, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_agents__WEBPACK_IMPORTED_MODULE_1__.AIResearchAgent, {}, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_agents__WEBPACK_IMPORTED_MODULE_1__.MCPAgent, {}, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                !currentlyRunningAgent?.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DefaultView, {}, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 50\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mcp_config_modal__WEBPACK_IMPORTED_MODULE_6__.MCPConfigModal, {\n                isOpen: showMCPConfigModal,\n                onClose: ()=>setShowMCPConfigModal(false)\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/canvas.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/canvas.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat-window.tsx":
/*!****************************************!*\
  !*** ./src/components/chat-window.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatWindow: () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/react-ui */ \"(ssr)/./node_modules/@copilotkit/react-ui/dist/chunk-NJY6RHHQ.mjs\");\n/* harmony import */ var _copilotkit_react_ui_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/react-ui/styles.css */ \"(ssr)/./node_modules/@copilotkit/react-ui/dist/index.css\");\n/* harmony import */ var _barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ActivityIcon,Loader2,RotateCw,SendIcon,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ActivityIcon,Loader2,RotateCw,SendIcon,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ActivityIcon,Loader2,RotateCw,SendIcon,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ActivityIcon,Loader2,RotateCw,SendIcon,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ActivityIcon,Loader2,RotateCw,SendIcon,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-cw.js\");\n/* __next_internal_client_entry_do_not_use__ ChatWindow auto */ \n\n\n\nconst ChatWindow = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_2__.CopilotChat, {\n        className: \"h-full flex flex-col\",\n        instructions: \"Always use the MCP Agent if you need to use the MCP Servers. You are a multi-agent chat system with specialized agents: - MCP Agent: For general or multipurpose tasks use the mcp-agent - Restaurant Intelligence Agent: Expert in restaurant analytics, performance metrics, and business intelligence - Research Agent: You are a helpful research assistant, set to help the user with conduction and writing a research paper on any topic.\",\n        labels: {\n            placeholder: \"Type your message here...\",\n            regenerateResponse: \"Try another response\"\n        },\n        icons: {\n            sendIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-4 h-4 hover:scale-110 transition-transform\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/chat-window.tsx\",\n                lineNumber: 28,\n                columnNumber: 11\n            }, void 0),\n            activityIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-4 h-4 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/chat-window.tsx\",\n                lineNumber: 30,\n                columnNumber: 23\n            }, void 0),\n            spinnerIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-4 h-4 animate-spin\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/chat-window.tsx\",\n                lineNumber: 31,\n                columnNumber: 22\n            }, void 0),\n            stopIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-4 h-4 hover:text-red-500 transition-colors\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/chat-window.tsx\",\n                lineNumber: 33,\n                columnNumber: 11\n            }, void 0),\n            regenerateIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ActivityIcon_Loader2_RotateCw_SendIcon_Square_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-4 h-4 hover:rotate-180 transition-transform duration-300\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/chat-window.tsx\",\n                lineNumber: 36,\n                columnNumber: 11\n            }, void 0)\n        }\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/chat-window.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat-window.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/coagents-provider.tsx":
/*!**********************************************!*\
  !*** ./src/components/coagents-provider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgentsContext: () => (/* binding */ AgentsContext),\n/* harmony export */   CoAgentsProvider: () => (/* binding */ CoAgentsProvider),\n/* harmony export */   useCoAgents: () => (/* binding */ useCoAgents)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-QNJNOZH3.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/available-agents */ \"(ssr)/./src/lib/available-agents.ts\");\n/* harmony import */ var _lib_mcp_config_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/mcp-config-types */ \"(ssr)/./src/lib/mcp-config-types.ts\");\n/* harmony import */ var _hooks_use_local_storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/use-local-storage */ \"(ssr)/./src/hooks/use-local-storage.tsx\");\n/* __next_internal_client_entry_do_not_use__ AgentsContext,CoAgentsProvider,useCoAgents auto */ \n\n\n\n\n\nconst AgentsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)([]);\n/**\n * This provider wraps state from all agents\n */ const CoAgentsProvider = ({ children })=>{\n    // Use ref to avoid re-rendering issues\n    const configsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Get saved MCP configurations from localStorage\n    const [savedConfigs] = (0,_hooks_use_local_storage__WEBPACK_IMPORTED_MODULE_4__.useLocalStorage)(_lib_mcp_config_types__WEBPACK_IMPORTED_MODULE_3__.MCP_STORAGE_KEY, {});\n    // Set the ref value once we have the saved configs\n    if (Object.keys(savedConfigs).length > 0 && Object.keys(configsRef.current).length === 0) {\n        configsRef.current = savedConfigs;\n    }\n    const { state: restaurantIntelligenceState } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCoAgent)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.RESTAURANT_INTELLIGENCE,\n        initialState: {\n            restaurants: [],\n            selected_restaurant_id: null,\n            location_analyses: [],\n            menu_items: [],\n            analysis_progress: [],\n            current_report: \"\"\n        }\n    });\n    const { state: aiResearchAgentState } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCoAgent)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.RESEARCH_AGENT,\n        initialState: {\n            model: \"openai\",\n            research_question: \"\",\n            resources: [],\n            report: \"\",\n            logs: []\n        }\n    });\n    const { state: mcpAgentState } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCoAgent)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.MCP_AGENT,\n        initialState: {\n            response: \"\",\n            logs: [],\n            mcp_config: configsRef.current\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AgentsContext.Provider, {\n        value: [\n            {\n                ...restaurantIntelligenceState,\n                __name__: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.RESTAURANT_INTELLIGENCE\n            },\n            {\n                ...aiResearchAgentState,\n                __name__: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.RESEARCH_AGENT\n            },\n            {\n                ...mcpAgentState,\n                __name__: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.MCP_AGENT\n            }\n        ],\n        children: children\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/coagents-provider.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, undefined);\n};\nconst useCoAgents = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AgentsContext);\n    if (!context) {\n        throw new Error(\"useAgents must be used within an AgentsProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/coagents-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mcp-config-modal.tsx":
/*!*********************************************!*\
  !*** ./src/components/mcp-config-modal.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MCPConfigModal: () => (/* binding */ MCPConfigModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MCPConfigModal = ({ isOpen, onClose })=>{\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg p-6 max-w-2xl w-full mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold\",\n                            children: \"MCP Server Configuration\"\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/mcp-config-modal.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/mcp-config-modal.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/mcp-config-modal.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"MCP server configuration will be available in a future update.\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/mcp-config-modal.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/mcp-config-modal.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/mcp-config-modal.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mcp-config-modal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/report-view.tsx":
/*!****************************************!*\
  !*** ./src/components/report-view.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReportView: () => (/* binding */ ReportView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ReportView = ({ reportData })=>{\n    if (!reportData) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Restaurant Intelligence Report\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/report-view.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"bg-gray-50 p-4 rounded text-sm overflow-auto\",\n                children: JSON.stringify(reportData, null, 2)\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/report-view.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/report-view.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9yZXBvcnQtdmlldy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQU1PLE1BQU1BLGFBQWtDLENBQUMsRUFBRUMsVUFBVSxFQUFFO0lBQzVELElBQUksQ0FBQ0EsWUFBWTtRQUNmLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUdELFdBQVU7MEJBQTZCOzs7Ozs7MEJBQzNDLDhEQUFDRTtnQkFBSUYsV0FBVTswQkFDWkcsS0FBS0MsU0FBUyxDQUFDTixZQUFZLE1BQU07Ozs7Ozs7Ozs7OztBQUkxQyxFQUFFIiwic291cmNlcyI6WyIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL2NvbXBvbmVudHMvcmVwb3J0LXZpZXcudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZDIH0gZnJvbSBcInJlYWN0XCI7XG5cbmludGVyZmFjZSBSZXBvcnRWaWV3UHJvcHMge1xuICByZXBvcnREYXRhOiBhbnk7XG59XG5cbmV4cG9ydCBjb25zdCBSZXBvcnRWaWV3OiBGQzxSZXBvcnRWaWV3UHJvcHM+ID0gKHsgcmVwb3J0RGF0YSB9KSA9PiB7XG4gIGlmICghcmVwb3J0RGF0YSkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LXNtIHAtNlwiPlxuICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+UmVzdGF1cmFudCBJbnRlbGxpZ2VuY2UgUmVwb3J0PC9oMz5cbiAgICAgIDxwcmUgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTQgcm91bmRlZCB0ZXh0LXNtIG92ZXJmbG93LWF1dG9cIj5cbiAgICAgICAge0pTT04uc3RyaW5naWZ5KHJlcG9ydERhdGEsIG51bGwsIDIpfVxuICAgICAgPC9wcmU+XG4gICAgPC9kaXY+XG4gICk7XG59OyJdLCJuYW1lcyI6WyJSZXBvcnRWaWV3IiwicmVwb3J0RGF0YSIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwicHJlIiwiSlNPTiIsInN0cmluZ2lmeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/report-view.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/research-logs.tsx":
/*!******************************************!*\
  !*** ./src/components/research-logs.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResearchLogs: () => (/* binding */ ResearchLogs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ResearchLogs = ({ logs })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Research Progress\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/research-logs.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-sm ${log.done ? \"text-green-600\" : \"text-gray-600\"}`,\n                        children: [\n                            log.done && \"✓ \",\n                            log.message\n                        ]\n                    }, index, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/research-logs.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/research-logs.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/research-logs.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9yZXNlYXJjaC1sb2dzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBU08sTUFBTUEsZUFBc0MsQ0FBQyxFQUFFQyxJQUFJLEVBQUU7SUFDMUQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBNkI7Ozs7OzswQkFDM0MsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNaRixLQUFLSSxHQUFHLENBQUMsQ0FBQ0MsS0FBS0Msc0JBQ2QsOERBQUNMO3dCQUVDQyxXQUFXLENBQUMsUUFBUSxFQUNsQkcsSUFBSUUsSUFBSSxHQUFHLG1CQUFtQixpQkFDOUI7OzRCQUVERixJQUFJRSxJQUFJLElBQUk7NEJBQ1pGLElBQUlHLE9BQU87O3VCQU5QRjs7Ozs7Ozs7Ozs7Ozs7OztBQVlqQixFQUFFIiwic291cmNlcyI6WyIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL2NvbXBvbmVudHMvcmVzZWFyY2gtbG9ncy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRkMgfSBmcm9tIFwicmVhY3RcIjtcblxuaW50ZXJmYWNlIFJlc2VhcmNoTG9nc1Byb3BzIHtcbiAgbG9nczogQXJyYXk8e1xuICAgIG1lc3NhZ2U6IHN0cmluZztcbiAgICBkb25lOiBib29sZWFuO1xuICB9Pjtcbn1cblxuZXhwb3J0IGNvbnN0IFJlc2VhcmNoTG9nczogRkM8UmVzZWFyY2hMb2dzUHJvcHM+ID0gKHsgbG9ncyB9KSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbSBwLTZcIj5cbiAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPlJlc2VhcmNoIFByb2dyZXNzPC9oMz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgIHtsb2dzLm1hcCgobG9nLCBpbmRleCkgPT4gKFxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2B0ZXh0LXNtICR7XG4gICAgICAgICAgICAgIGxvZy5kb25lID8gXCJ0ZXh0LWdyZWVuLTYwMFwiIDogXCJ0ZXh0LWdyYXktNjAwXCJcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtsb2cuZG9uZSAmJiBcIuKckyBcIn1cbiAgICAgICAgICAgIHtsb2cubWVzc2FnZX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07Il0sIm5hbWVzIjpbIlJlc2VhcmNoTG9ncyIsImxvZ3MiLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsIm1hcCIsImxvZyIsImluZGV4IiwiZG9uZSIsIm1lc3NhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/research-logs.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/restaurant-dashboard.tsx":
/*!*************************************************!*\
  !*** ./src/components/restaurant-dashboard.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RestaurantDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_report_view__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/report-view */ \"(ssr)/./src/components/report-view.tsx\");\n/* harmony import */ var _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/available-agents */ \"(ssr)/./src/lib/available-agents.ts\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-QNJNOZH3.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-2QZSAQTX.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChefHat_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChefHat!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chef-hat.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChefHat_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChefHat!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\nfunction RestaurantDashboard() {\n    const [logs, setLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const isAnalysisInProgress = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(false);\n    const { state: restaurantState, stop: stopRestaurantAgent } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__.useCoAgent)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.RESTAURANT_INTELLIGENCE,\n        initialState: {\n            current_report_type: \"\",\n            location: \"\",\n            concept_type: \"\",\n            report_data: null,\n            logs: []\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"RestaurantDashboard.useEffect\": ()=>{\n            if (restaurantState.logs) {\n                setLogs({\n                    \"RestaurantDashboard.useEffect\": (prevLogs)=>{\n                        const newLogs = [\n                            ...prevLogs\n                        ];\n                        restaurantState.logs.forEach({\n                            \"RestaurantDashboard.useEffect\": (log)=>{\n                                const existingLogIndex = newLogs.findIndex({\n                                    \"RestaurantDashboard.useEffect.existingLogIndex\": (l)=>l.message === log.message\n                                }[\"RestaurantDashboard.useEffect.existingLogIndex\"]);\n                                if (existingLogIndex >= 0) {\n                                    if (log.done && !newLogs[existingLogIndex].done) {\n                                        newLogs[existingLogIndex].done = true;\n                                    }\n                                } else {\n                                    newLogs.push(log);\n                                }\n                            }\n                        }[\"RestaurantDashboard.useEffect\"]);\n                        return newLogs;\n                    }\n                }[\"RestaurantDashboard.useEffect\"]);\n            }\n        }\n    }[\"RestaurantDashboard.useEffect\"], [\n        restaurantState.logs\n    ]);\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.useCoAgentStateRender)({\n        name: _lib_available_agents__WEBPACK_IMPORTED_MODULE_2__.AvailableAgents.RESTAURANT_INTELLIGENCE,\n        handler: {\n            \"RestaurantDashboard.useCoAgentStateRender\": ({ nodeName })=>{\n                if (nodeName === \"__end__\") {\n                    setTimeout({\n                        \"RestaurantDashboard.useCoAgentStateRender\": ()=>{\n                            stopRestaurantAgent();\n                        }\n                    }[\"RestaurantDashboard.useCoAgentStateRender\"], 1000);\n                }\n            }\n        }[\"RestaurantDashboard.useCoAgentStateRender\"],\n        render: {\n            \"RestaurantDashboard.useCoAgentStateRender\": ({ status })=>{\n                if (status === \"inProgress\") {\n                    isAnalysisInProgress.current = true;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-blue-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChefHat_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Analyzing restaurant data...\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: logs.map({\n                                    \"RestaurantDashboard.useCoAgentStateRender\": (log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `text-sm ${log.done ? \"text-green-600\" : \"text-gray-600\"}`,\n                                            children: [\n                                                log.done && \"✓ \",\n                                                log.message\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this)\n                                }[\"RestaurantDashboard.useCoAgentStateRender\"])\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this);\n                }\n                if (status === \"complete\") {\n                    isAnalysisInProgress.current = false;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-green-600 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChefHat_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Analysis complete\"\n                                }, void 0, false, {\n                                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this);\n                }\n            }\n        }[\"RestaurantDashboard.useCoAgentStateRender\"]\n    });\n    if (isAnalysisInProgress.current) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-4 h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-blue-600 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChefHat_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-6 w-6 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"Restaurant Intelligence Analysis\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-sm ${log.done ? \"text-green-600\" : \"text-gray-600\"}`,\n                                children: [\n                                    log.done && \"✓ \",\n                                    log.message\n                                ]\n                            }, index, true, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this);\n    }\n    if (!restaurantState.report_data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChefHat_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-16 w-16 text-gray-400 mb-4\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-semibold text-gray-600 mb-2\",\n                    children: \"BiteBase Intelligence\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-center max-w-md\",\n                    children: \"Get comprehensive restaurant analytics and intelligence reports. Ask me to analyze locations, competitors, menus, sales forecasts, and more.\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4 h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_report_view__WEBPACK_IMPORTED_MODULE_1__.ReportView, {\n            reportData: restaurantState.report_data\n        }, void 0, false, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/restaurant-dashboard.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/restaurant-dashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/skeletons/index.tsx":
/*!********************************************!*\
  !*** ./src/components/skeletons/index.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatSkeleton: () => (/* binding */ ChatSkeleton),\n/* harmony export */   EmailListSkeleton: () => (/* binding */ EmailListSkeleton),\n/* harmony export */   EmailSkeleton: () => (/* binding */ EmailSkeleton),\n/* harmony export */   GenericSkeleton: () => (/* binding */ GenericSkeleton),\n/* harmony export */   MapSkeleton: () => (/* binding */ MapSkeleton),\n/* harmony export */   ResearchPaperSkeleton: () => (/* binding */ ResearchPaperSkeleton),\n/* harmony export */   XKCDSkeleton: () => (/* binding */ XKCDSkeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ui_skeleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/skeleton */ \"(ssr)/./src/components/ui/skeleton.tsx\");\n\n\nconst EmailSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                className: \"h-8 w-full\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 6,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-4 w-[90%]\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-4 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-4 w-[85%]\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 7,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n        lineNumber: 5,\n        columnNumber: 3\n    }, undefined);\nconst EmailListSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: Array.from({\n            length: 5\n        }).map((_, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 p-4 hover:bg-gray-50 cursor-pointer\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-12 w-12 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                                className: \"h-4 w-32\"\n                                            }, void 0, false, {\n                                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                                className: \"h-3 w-24\"\n                                            }, void 0, false, {\n                                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-3 w-3 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"h-4 w-3/4\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"h-3 w-4/5\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"h-3 w-8\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, idx, true, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined);\nconst ResearchPaperSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 mt-14\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prose max-w-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                    className: \"h-10 w-3/4\"\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 47,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: Array.from({\n                    length: 3\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose max-w-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"h-8 w-1/3 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: Array.from({\n                                    length: 4\n                                }).map((_, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-full\"\n                                    }, idx, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, i, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 52,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"prose max-w-none mt-8 pt-6 border-t\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-6 w-32 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 7\n                    }, undefined),\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: Array.from({\n                            length: 3\n                        }).map((_, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-2/3\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \"\n                                ]\n                            }, idx, true, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 66,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\nconst XKCDSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 flex flex-col items-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-[500px] h-[500px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-full w-full\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-4 right-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"h-8 w-32\"\n                            }, void 0, false, {\n                                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 9\n                            }, undefined),\n                            \" \"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 82,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-10 w-24\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 7\n                    }, undefined),\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-10 w-24\"\n                    }, void 0, false, {\n                        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 7\n                    }, undefined),\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 88,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n        lineNumber: 81,\n        columnNumber: 3\n    }, undefined);\nconst ChatSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                className: \"h-8 w-full\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 97,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                className: \"h-24 w-full\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 98,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                className: \"h-24 w-full\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 99,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                className: \"h-24 w-full\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 100,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                className: \"h-12 w-full\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 101,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                className: \"h-12 w-full\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 102,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n        lineNumber: 96,\n        columnNumber: 3\n    }, undefined);\nconst GenericSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen animate-pulse p-4 flex flex-col items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                className: \"h-48 w-48 bg-[url('/icon.png')] bg-center bg-no-repeat bg-contain opacity-20 rounded-lg shadow-lg animate-[fly-away_2s_ease-in-out_infinite]\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n            lineNumber: 109,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n        lineNumber: 107,\n        columnNumber: 3\n    }, undefined);\nconst MapSkeleton = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute inset-0\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-[url('/map-overlay.png')] bg-cover bg-center bg-no-repeat\"\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n            lineNumber: 117,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/skeletons/index.tsx\",\n        lineNumber: 116,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/skeletons/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-primary/10\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/components/ui/skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9za2VsZXRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFFaEMsU0FBU0MsU0FBUyxFQUNoQkMsU0FBUyxFQUNULEdBQUdDLE9BQ2tDO0lBQ3JDLHFCQUNFLDhEQUFDQztRQUNDRixXQUFXRiw4Q0FBRUEsQ0FBQywwQ0FBMENFO1FBQ3ZELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRW1CIiwic291cmNlcyI6WyIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL2NvbXBvbmVudHMvdWkvc2tlbGV0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZnVuY3Rpb24gU2tlbGV0b24oe1xuICBjbGFzc05hbWUsXG4gIC4uLnByb3BzXG59OiBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBjbGFzc05hbWU9e2NuKFwiYW5pbWF0ZS1wdWxzZSByb3VuZGVkLW1kIGJnLXByaW1hcnkvMTBcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IFNrZWxldG9uIH0iXSwibmFtZXMiOlsiY24iLCJTa2VsZXRvbiIsImNsYXNzTmFtZSIsInByb3BzIiwiZGl2Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-local-storage.tsx":
/*!*****************************************!*\
  !*** ./src/hooks/use-local-storage.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useLocalStorage(key, initialValue) {\n    const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLocalStorage.useEffect\": ()=>{\n            try {\n                const item = window.localStorage.getItem(key);\n                if (item) {\n                    setStoredValue(JSON.parse(item));\n                }\n            } catch (error) {\n                console.error(`Error reading localStorage key \"${key}\":`, error);\n            }\n        }\n    }[\"useLocalStorage.useEffect\"], [\n        key\n    ]);\n    const setValue = (value)=>{\n        try {\n            const valueToStore = value instanceof Function ? value(storedValue) : value;\n            setStoredValue(valueToStore);\n            window.localStorage.setItem(key, JSON.stringify(valueToStore));\n        } catch (error) {\n            console.error(`Error setting localStorage key \"${key}\":`, error);\n        }\n    };\n    return [\n        storedValue,\n        setValue\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-local-storage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/available-agents.ts":
/*!*************************************!*\
  !*** ./src/lib/available-agents.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AvailableAgents: () => (/* binding */ AvailableAgents)\n/* harmony export */ });\nvar AvailableAgents = /*#__PURE__*/ function(AvailableAgents) {\n    AvailableAgents[\"RESTAURANT_INTELLIGENCE\"] = \"restaurant-intelligence\";\n    AvailableAgents[\"RESEARCH_AGENT\"] = \"research_agent\";\n    AvailableAgents[\"MCP_AGENT\"] = \"mcp-agent\";\n    return AvailableAgents;\n}({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2F2YWlsYWJsZS1hZ2VudHMudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLDZDQUFLQTs7OztXQUFBQTtNQUlYIiwic291cmNlcyI6WyIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvc3JjL2xpYi9hdmFpbGFibGUtYWdlbnRzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBlbnVtIEF2YWlsYWJsZUFnZW50cyB7XG4gIFJFU1RBVVJBTlRfSU5URUxMSUdFTkNFID0gXCJyZXN0YXVyYW50LWludGVsbGlnZW5jZVwiLFxuICBSRVNFQVJDSF9BR0VOVCA9IFwicmVzZWFyY2hfYWdlbnRcIixcbiAgTUNQX0FHRU5UID0gXCJtY3AtYWdlbnRcIixcbn0iXSwibmFtZXMiOlsiQXZhaWxhYmxlQWdlbnRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/available-agents.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/mcp-config-types.ts":
/*!*************************************!*\
  !*** ./src/lib/mcp-config-types.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MCP_STORAGE_KEY: () => (/* binding */ MCP_STORAGE_KEY)\n/* harmony export */ });\n// Local storage key for saving MCP configurations\nconst MCP_STORAGE_KEY = \"mcp-server-configs\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL21jcC1jb25maWctdHlwZXMudHMiLCJtYXBwaW5ncyI6Ijs7OztBQW1CQSxrREFBa0Q7QUFDM0MsTUFBTUEsa0JBQWtCLHFCQUFxQiIsInNvdXJjZXMiOlsiL3RlYW1zcGFjZS9zdHVkaW9zL3RoaXNfc3R1ZGlvL29wZW4tbXVsdGktYWdlbnQtY2FudmFzL2JpdGViYXNlX2FnZW50L3NyYy9saWIvbWNwLWNvbmZpZy10eXBlcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdHlwZSBDb25uZWN0aW9uVHlwZSA9IFwic3RkaW9cIiB8IFwic3NlXCI7XG5cbmV4cG9ydCBpbnRlcmZhY2UgU3RkaW9Db25maWcge1xuICBjb21tYW5kOiBzdHJpbmc7XG4gIGFyZ3M6IHN0cmluZ1tdO1xuICB0cmFuc3BvcnQ6IFwic3RkaW9cIjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTU0VDb25maWcge1xuICB1cmw6IHN0cmluZztcbiAgdHJhbnNwb3J0OiBcInNzZVwiO1xufVxuXG5leHBvcnQgdHlwZSBTZXJ2ZXJDb25maWcgPSBTdGRpb0NvbmZpZyB8IFNTRUNvbmZpZztcblxuZXhwb3J0IGludGVyZmFjZSBNQ1BDb25maWcge1xuICBtY3BfY29uZmlnOiBSZWNvcmQ8c3RyaW5nLCBTZXJ2ZXJDb25maWc+O1xufVxuXG4vLyBMb2NhbCBzdG9yYWdlIGtleSBmb3Igc2F2aW5nIE1DUCBjb25maWd1cmF0aW9uc1xuZXhwb3J0IGNvbnN0IE1DUF9TVE9SQUdFX0tFWSA9IFwibWNwLXNlcnZlci1jb25maWdzXCI7Il0sIm5hbWVzIjpbIk1DUF9TVE9SQUdFX0tFWSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/mcp-config-types.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL3RlYW1zcGFjZS9zdHVkaW9zL3RoaXNfc3R1ZGlvL29wZW4tbXVsdGktYWdlbnQtY2FudmFzL2JpdGViYXNlX2FnZW50L3NyYy9saWIvdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59Il0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/Providers.tsx":
/*!*************************************!*\
  !*** ./src/providers/Providers.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/@copilotkit/react-core/dist/chunk-6WTWBXEJ.mjs\");\n/* harmony import */ var _components_coagents_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/coagents-provider */ \"(ssr)/./src/components/coagents-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient();\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_5__.CopilotKit, {\n                showDevConsole: false,\n                publicLicenseKey: \"ck_pub_4bae12d076311a78139ec12d2215c973\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_coagents_provider__WEBPACK_IMPORTED_MODULE_2__.CoAgentsProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/providers/Providers.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/providers/Providers.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_6__.ReactQueryDevtools, {\n                initialIsOpen: false\n            }, void 0, false, {\n                fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/providers/Providers.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/teamspace/studios/this_studio/open-multi-agent-canvas/bitebase_agent/src/providers/Providers.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/Providers.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@tanstack","vendor-chunks/refractor","vendor-chunks/@copilotkit","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/micromark-core-commonmark","vendor-chunks/hastscript","vendor-chunks/hast-util-to-parse5","vendor-chunks/property-information","vendor-chunks/parse5","vendor-chunks/lucide-react","vendor-chunks/@babel","vendor-chunks/micromark","vendor-chunks/zod","vendor-chunks/prop-types","vendor-chunks/react-syntax-highlighter","vendor-chunks/mdast-util-definitions","vendor-chunks/hast-util-from-parse5","vendor-chunks/graphql","vendor-chunks/entities","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/micromark-extension-math","vendor-chunks/vfile","vendor-chunks/uvu","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/@urql","vendor-chunks/style-to-js","vendor-chunks/zwitch","vendor-chunks/wonka","vendor-chunks/web-namespaces","vendor-chunks/vfile-message","vendor-chunks/vfile-location","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/unist-util-generated","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/tailwind-merge","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-math","vendor-chunks/remark-gfm","vendor-chunks/rehype-raw","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-math","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/kleur","vendor-chunks/is-plain-obj","vendor-chunks/html-void-elements","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/hast-util-raw","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/diff","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/clsx","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/@0no-co","vendor-chunks/xtend","vendor-chunks/untruncate-json","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/object-assign","vendor-chunks/ms","vendor-chunks/is-buffer","vendor-chunks/inline-style-parser","vendor-chunks/hast-util-parse-selector","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fteamspace%2Fstudios%2Fthis_studio%2Fopen-multi-agent-canvas%2Fbitebase_agent&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();