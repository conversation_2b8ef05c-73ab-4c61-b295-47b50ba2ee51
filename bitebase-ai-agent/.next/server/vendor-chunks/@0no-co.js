"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@0no-co";
exports.ids = ["vendor-chunks/@0no-co"];
exports.modules = {

/***/ "(ssr)/./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BREAK: () => (/* binding */ l),\n/* harmony export */   GraphQLError: () => (/* binding */ GraphQLError),\n/* harmony export */   Kind: () => (/* binding */ e),\n/* harmony export */   OperationTypeNode: () => (/* binding */ r),\n/* harmony export */   Source: () => (/* binding */ Source),\n/* harmony export */   isSelectionNode: () => (/* binding */ isSelectionNode),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseType: () => (/* binding */ parseType),\n/* harmony export */   parseValue: () => (/* binding */ parseValue),\n/* harmony export */   print: () => (/* binding */ print),\n/* harmony export */   printBlockString: () => (/* binding */ printBlockString),\n/* harmony export */   printString: () => (/* binding */ printString),\n/* harmony export */   valueFromASTUntyped: () => (/* binding */ valueFromASTUntyped),\n/* harmony export */   valueFromTypeNode: () => (/* binding */ valueFromTypeNode),\n/* harmony export */   visit: () => (/* binding */ visit)\n/* harmony export */ });\nvar e = {\n  NAME: \"Name\",\n  DOCUMENT: \"Document\",\n  OPERATION_DEFINITION: \"OperationDefinition\",\n  VARIABLE_DEFINITION: \"VariableDefinition\",\n  SELECTION_SET: \"SelectionSet\",\n  FIELD: \"Field\",\n  ARGUMENT: \"Argument\",\n  FRAGMENT_SPREAD: \"FragmentSpread\",\n  INLINE_FRAGMENT: \"InlineFragment\",\n  FRAGMENT_DEFINITION: \"FragmentDefinition\",\n  VARIABLE: \"Variable\",\n  INT: \"IntValue\",\n  FLOAT: \"FloatValue\",\n  STRING: \"StringValue\",\n  BOOLEAN: \"BooleanValue\",\n  NULL: \"NullValue\",\n  ENUM: \"EnumValue\",\n  LIST: \"ListValue\",\n  OBJECT: \"ObjectValue\",\n  OBJECT_FIELD: \"ObjectField\",\n  DIRECTIVE: \"Directive\",\n  NAMED_TYPE: \"NamedType\",\n  LIST_TYPE: \"ListType\",\n  NON_NULL_TYPE: \"NonNullType\"\n};\n\nvar r = {\n  QUERY: \"query\",\n  MUTATION: \"mutation\",\n  SUBSCRIPTION: \"subscription\"\n};\n\nclass GraphQLError extends Error {\n  constructor(e, r, i, n, t, a, o) {\n    if (super(e), this.name = \"GraphQLError\", this.message = e, t) {\n      this.path = t;\n    }\n    if (r) {\n      this.nodes = Array.isArray(r) ? r : [ r ];\n    }\n    if (i) {\n      this.source = i;\n    }\n    if (n) {\n      this.positions = n;\n    }\n    if (a) {\n      this.originalError = a;\n    }\n    var l = o;\n    if (!l && a) {\n      var d = a.extensions;\n      if (d && \"object\" == typeof d) {\n        l = d;\n      }\n    }\n    this.extensions = l || {};\n  }\n  toJSON() {\n    return {\n      ...this,\n      message: this.message\n    };\n  }\n  toString() {\n    return this.message;\n  }\n  get [Symbol.toStringTag]() {\n    return \"GraphQLError\";\n  }\n}\n\nvar i;\n\nvar n;\n\nfunction error(e) {\n  return new GraphQLError(`Syntax Error: Unexpected token at ${n} in ${e}`);\n}\n\nfunction advance(e) {\n  if (e.lastIndex = n, e.test(i)) {\n    return i.slice(n, n = e.lastIndex);\n  }\n}\n\nvar t = / +(?=[^\\s])/y;\n\nfunction blockString(e) {\n  var r = e.split(\"\\n\");\n  var i = \"\";\n  var n = 0;\n  var a = 0;\n  var o = r.length - 1;\n  for (var l = 0; l < r.length; l++) {\n    if (t.lastIndex = 0, t.test(r[l])) {\n      if (l && (!n || t.lastIndex < n)) {\n        n = t.lastIndex;\n      }\n      a = a || l, o = l;\n    }\n  }\n  for (var d = a; d <= o; d++) {\n    if (d !== a) {\n      i += \"\\n\";\n    }\n    i += r[d].slice(n).replace(/\\\\\"\"\"/g, '\"\"\"');\n  }\n  return i;\n}\n\nfunction ignored() {\n  for (var e = 0 | i.charCodeAt(n++); 9 === e || 10 === e || 13 === e || 32 === e || 35 === e || 44 === e || 65279 === e; e = 0 | i.charCodeAt(n++)) {\n    if (35 === e) {\n      for (;(e = 0 | i.charCodeAt(n++)) && 10 !== e && 13 !== e; ) {}\n    }\n  }\n  n--;\n}\n\nfunction name() {\n  var e = n;\n  for (var r = 0 | i.charCodeAt(n++); r >= 48 && r <= 57 || r >= 65 && r <= 90 || 95 === r || r >= 97 && r <= 122; r = 0 | i.charCodeAt(n++)) {}\n  if (e === n - 1) {\n    throw error(\"Name\");\n  }\n  var t = i.slice(e, --n);\n  return ignored(), t;\n}\n\nfunction nameNode() {\n  return {\n    kind: \"Name\",\n    value: name()\n  };\n}\n\nvar a = /(?:\"\"\"|(?:[\\s\\S]*?[^\\\\])\"\"\")/y;\n\nvar o = /(?:(?:\\.\\d+)?[eE][+-]?\\d+|\\.\\d+)/y;\n\nfunction value(e) {\n  var r;\n  switch (i.charCodeAt(n)) {\n   case 91:\n    n++, ignored();\n    var t = [];\n    for (;93 !== i.charCodeAt(n); ) {\n      t.push(value(e));\n    }\n    return n++, ignored(), {\n      kind: \"ListValue\",\n      values: t\n    };\n\n   case 123:\n    n++, ignored();\n    var l = [];\n    for (;125 !== i.charCodeAt(n); ) {\n      var d = nameNode();\n      if (58 !== i.charCodeAt(n++)) {\n        throw error(\"ObjectField\");\n      }\n      ignored(), l.push({\n        kind: \"ObjectField\",\n        name: d,\n        value: value(e)\n      });\n    }\n    return n++, ignored(), {\n      kind: \"ObjectValue\",\n      fields: l\n    };\n\n   case 36:\n    if (e) {\n      throw error(\"Variable\");\n    }\n    return n++, {\n      kind: \"Variable\",\n      name: nameNode()\n    };\n\n   case 34:\n    if (34 === i.charCodeAt(n + 1) && 34 === i.charCodeAt(n + 2)) {\n      if (n += 3, null == (r = advance(a))) {\n        throw error(\"StringValue\");\n      }\n      return ignored(), {\n        kind: \"StringValue\",\n        value: blockString(r.slice(0, -3)),\n        block: !0\n      };\n    } else {\n      var u = n;\n      var s;\n      n++;\n      var c = !1;\n      for (s = 0 | i.charCodeAt(n++); 92 === s && (n++, c = !0) || 10 !== s && 13 !== s && 34 !== s && s; s = 0 | i.charCodeAt(n++)) {}\n      if (34 !== s) {\n        throw error(\"StringValue\");\n      }\n      return r = i.slice(u, n), ignored(), {\n        kind: \"StringValue\",\n        value: c ? JSON.parse(r) : r.slice(1, -1),\n        block: !1\n      };\n    }\n\n   case 45:\n   case 48:\n   case 49:\n   case 50:\n   case 51:\n   case 52:\n   case 53:\n   case 54:\n   case 55:\n   case 56:\n   case 57:\n    var v = n++;\n    var f;\n    for (;(f = 0 | i.charCodeAt(n++)) >= 48 && f <= 57; ) {}\n    var m = i.slice(v, --n);\n    if (46 === (f = i.charCodeAt(n)) || 69 === f || 101 === f) {\n      if (null == (r = advance(o))) {\n        throw error(\"FloatValue\");\n      }\n      return ignored(), {\n        kind: \"FloatValue\",\n        value: m + r\n      };\n    } else {\n      return ignored(), {\n        kind: \"IntValue\",\n        value: m\n      };\n    }\n\n   case 110:\n    if (117 === i.charCodeAt(n + 1) && 108 === i.charCodeAt(n + 2) && 108 === i.charCodeAt(n + 3)) {\n      return n += 4, ignored(), {\n        kind: \"NullValue\"\n      };\n    } else {\n      break;\n    }\n\n   case 116:\n    if (114 === i.charCodeAt(n + 1) && 117 === i.charCodeAt(n + 2) && 101 === i.charCodeAt(n + 3)) {\n      return n += 4, ignored(), {\n        kind: \"BooleanValue\",\n        value: !0\n      };\n    } else {\n      break;\n    }\n\n   case 102:\n    if (97 === i.charCodeAt(n + 1) && 108 === i.charCodeAt(n + 2) && 115 === i.charCodeAt(n + 3) && 101 === i.charCodeAt(n + 4)) {\n      return n += 5, ignored(), {\n        kind: \"BooleanValue\",\n        value: !1\n      };\n    } else {\n      break;\n    }\n  }\n  return {\n    kind: \"EnumValue\",\n    value: name()\n  };\n}\n\nfunction arguments_(e) {\n  if (40 === i.charCodeAt(n)) {\n    var r = [];\n    n++, ignored();\n    do {\n      var t = nameNode();\n      if (58 !== i.charCodeAt(n++)) {\n        throw error(\"Argument\");\n      }\n      ignored(), r.push({\n        kind: \"Argument\",\n        name: t,\n        value: value(e)\n      });\n    } while (41 !== i.charCodeAt(n));\n    return n++, ignored(), r;\n  }\n}\n\nfunction directives(e) {\n  if (64 === i.charCodeAt(n)) {\n    var r = [];\n    do {\n      n++, r.push({\n        kind: \"Directive\",\n        name: nameNode(),\n        arguments: arguments_(e)\n      });\n    } while (64 === i.charCodeAt(n));\n    return r;\n  }\n}\n\nfunction type() {\n  var e = 0;\n  for (;91 === i.charCodeAt(n); ) {\n    e++, n++, ignored();\n  }\n  var r = {\n    kind: \"NamedType\",\n    name: nameNode()\n  };\n  do {\n    if (33 === i.charCodeAt(n)) {\n      n++, ignored(), r = {\n        kind: \"NonNullType\",\n        type: r\n      };\n    }\n    if (e) {\n      if (93 !== i.charCodeAt(n++)) {\n        throw error(\"NamedType\");\n      }\n      ignored(), r = {\n        kind: \"ListType\",\n        type: r\n      };\n    }\n  } while (e--);\n  return r;\n}\n\nfunction selectionSetStart() {\n  if (123 !== i.charCodeAt(n++)) {\n    throw error(\"SelectionSet\");\n  }\n  return ignored(), selectionSet();\n}\n\nfunction selectionSet() {\n  var e = [];\n  do {\n    if (46 === i.charCodeAt(n)) {\n      if (46 !== i.charCodeAt(++n) || 46 !== i.charCodeAt(++n)) {\n        throw error(\"SelectionSet\");\n      }\n      switch (n++, ignored(), i.charCodeAt(n)) {\n       case 64:\n        e.push({\n          kind: \"InlineFragment\",\n          typeCondition: void 0,\n          directives: directives(!1),\n          selectionSet: selectionSetStart()\n        });\n        break;\n\n       case 111:\n        if (110 === i.charCodeAt(n + 1)) {\n          n += 2, ignored(), e.push({\n            kind: \"InlineFragment\",\n            typeCondition: {\n              kind: \"NamedType\",\n              name: nameNode()\n            },\n            directives: directives(!1),\n            selectionSet: selectionSetStart()\n          });\n        } else {\n          e.push({\n            kind: \"FragmentSpread\",\n            name: nameNode(),\n            directives: directives(!1)\n          });\n        }\n        break;\n\n       case 123:\n        n++, ignored(), e.push({\n          kind: \"InlineFragment\",\n          typeCondition: void 0,\n          directives: void 0,\n          selectionSet: selectionSet()\n        });\n        break;\n\n       default:\n        e.push({\n          kind: \"FragmentSpread\",\n          name: nameNode(),\n          directives: directives(!1)\n        });\n      }\n    } else {\n      var r = nameNode();\n      var t = void 0;\n      if (58 === i.charCodeAt(n)) {\n        n++, ignored(), t = r, r = nameNode();\n      }\n      var a = arguments_(!1);\n      var o = directives(!1);\n      var l = void 0;\n      if (123 === i.charCodeAt(n)) {\n        n++, ignored(), l = selectionSet();\n      }\n      e.push({\n        kind: \"Field\",\n        alias: t,\n        name: r,\n        arguments: a,\n        directives: o,\n        selectionSet: l\n      });\n    }\n  } while (125 !== i.charCodeAt(n));\n  return n++, ignored(), {\n    kind: \"SelectionSet\",\n    selections: e\n  };\n}\n\nfunction variableDefinitions() {\n  if (ignored(), 40 === i.charCodeAt(n)) {\n    var e = [];\n    n++, ignored();\n    do {\n      var r = void 0;\n      if (34 === i.charCodeAt(n)) {\n        r = value(!0);\n      }\n      if (36 !== i.charCodeAt(n++)) {\n        throw error(\"Variable\");\n      }\n      var t = nameNode();\n      if (58 !== i.charCodeAt(n++)) {\n        throw error(\"VariableDefinition\");\n      }\n      ignored();\n      var a = type();\n      var o = void 0;\n      if (61 === i.charCodeAt(n)) {\n        n++, ignored(), o = value(!0);\n      }\n      ignored();\n      var l = {\n        kind: \"VariableDefinition\",\n        variable: {\n          kind: \"Variable\",\n          name: t\n        },\n        type: a,\n        defaultValue: o,\n        directives: directives(!0)\n      };\n      if (r) {\n        l.description = r;\n      }\n      e.push(l);\n    } while (41 !== i.charCodeAt(n));\n    return n++, ignored(), e;\n  }\n}\n\nfunction fragmentDefinition(e) {\n  var r = nameNode();\n  if (111 !== i.charCodeAt(n++) || 110 !== i.charCodeAt(n++)) {\n    throw error(\"FragmentDefinition\");\n  }\n  ignored();\n  var t = {\n    kind: \"FragmentDefinition\",\n    name: r,\n    typeCondition: {\n      kind: \"NamedType\",\n      name: nameNode()\n    },\n    directives: directives(!1),\n    selectionSet: selectionSetStart()\n  };\n  if (e) {\n    t.description = e;\n  }\n  return t;\n}\n\nfunction definitions() {\n  var e = [];\n  do {\n    var r = void 0;\n    if (34 === i.charCodeAt(n)) {\n      r = value(!0);\n    }\n    if (123 === i.charCodeAt(n)) {\n      if (r) {\n        throw error(\"Document\");\n      }\n      n++, ignored(), e.push({\n        kind: \"OperationDefinition\",\n        operation: \"query\",\n        name: void 0,\n        variableDefinitions: void 0,\n        directives: void 0,\n        selectionSet: selectionSet()\n      });\n    } else {\n      var t = name();\n      switch (t) {\n       case \"fragment\":\n        e.push(fragmentDefinition(r));\n        break;\n\n       case \"query\":\n       case \"mutation\":\n       case \"subscription\":\n        var a;\n        var o = void 0;\n        if (40 !== (a = i.charCodeAt(n)) && 64 !== a && 123 !== a) {\n          o = nameNode();\n        }\n        var l = {\n          kind: \"OperationDefinition\",\n          operation: t,\n          name: o,\n          variableDefinitions: variableDefinitions(),\n          directives: directives(!1),\n          selectionSet: selectionSetStart()\n        };\n        if (r) {\n          l.description = r;\n        }\n        e.push(l);\n        break;\n\n       default:\n        throw error(\"Document\");\n      }\n    }\n  } while (n < i.length);\n  return e;\n}\n\nfunction parse(e, r) {\n  if (i = e.body ? e.body : e, n = 0, ignored(), r && r.noLocation) {\n    return {\n      kind: \"Document\",\n      definitions: definitions()\n    };\n  } else {\n    return {\n      kind: \"Document\",\n      definitions: definitions(),\n      loc: {\n        start: 0,\n        end: i.length,\n        startToken: void 0,\n        endToken: void 0,\n        source: {\n          body: i,\n          name: \"graphql.web\",\n          locationOffset: {\n            line: 1,\n            column: 1\n          }\n        }\n      }\n    };\n  }\n}\n\nfunction parseValue(e, r) {\n  return i = e.body ? e.body : e, n = 0, ignored(), value(!1);\n}\n\nfunction parseType(e, r) {\n  return i = e.body ? e.body : e, n = 0, type();\n}\n\nvar l = {};\n\nfunction visit(e, r) {\n  var i = [];\n  var n = [];\n  try {\n    var t = function traverse(e, t, a) {\n      var o = !1;\n      var d = r[e.kind] && r[e.kind].enter || r[e.kind] || r.enter;\n      var u = d && d.call(r, e, t, a, n, i);\n      if (!1 === u) {\n        return e;\n      } else if (null === u) {\n        return null;\n      } else if (u === l) {\n        throw l;\n      } else if (u && \"string\" == typeof u.kind) {\n        o = u !== e, e = u;\n      }\n      if (a) {\n        i.push(a);\n      }\n      var s;\n      var c = {\n        ...e\n      };\n      for (var v in e) {\n        n.push(v);\n        var f = e[v];\n        if (Array.isArray(f)) {\n          var m = [];\n          for (var p = 0; p < f.length; p++) {\n            if (null != f[p] && \"string\" == typeof f[p].kind) {\n              if (i.push(e), n.push(p), s = traverse(f[p], p, f), n.pop(), i.pop(), null == s) {\n                o = !0;\n              } else {\n                o = o || s !== f[p], m.push(s);\n              }\n            }\n          }\n          f = m;\n        } else if (null != f && \"string\" == typeof f.kind) {\n          if (void 0 !== (s = traverse(f, v, e))) {\n            o = o || f !== s, f = s;\n          }\n        }\n        if (n.pop(), o) {\n          c[v] = f;\n        }\n      }\n      if (a) {\n        i.pop();\n      }\n      var h = r[e.kind] && r[e.kind].leave || r.leave;\n      var g = h && h.call(r, e, t, a, n, i);\n      if (g === l) {\n        throw l;\n      } else if (void 0 !== g) {\n        return g;\n      } else if (void 0 !== u) {\n        return o ? c : u;\n      } else {\n        return o ? c : e;\n      }\n    }(e);\n    return void 0 !== t && !1 !== t ? t : e;\n  } catch (r) {\n    if (r !== l) {\n      throw r;\n    }\n    return e;\n  }\n}\n\nfunction mapJoin(e, r, i) {\n  var n = \"\";\n  for (var t = 0; t < e.length; t++) {\n    if (t) {\n      n += r;\n    }\n    n += i(e[t]);\n  }\n  return n;\n}\n\nfunction printString(e) {\n  return JSON.stringify(e);\n}\n\nfunction printBlockString(e) {\n  return '\"\"\"\\n' + e.replace(/\"\"\"/g, '\\\\\"\"\"') + '\\n\"\"\"';\n}\n\nvar d = \"\\n\";\n\nvar u = {\n  OperationDefinition(e) {\n    var r = \"\";\n    if (e.description) {\n      r += u.StringValue(e.description) + \"\\n\";\n    }\n    if (r += e.operation, e.name) {\n      r += \" \" + e.name.value;\n    }\n    if (e.variableDefinitions && e.variableDefinitions.length) {\n      if (!e.name) {\n        r += \" \";\n      }\n      r += \"(\" + mapJoin(e.variableDefinitions, \", \", u.VariableDefinition) + \")\";\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", u.Directive);\n    }\n    var i = u.SelectionSet(e.selectionSet);\n    return \"query\" !== r ? r + \" \" + i : i;\n  },\n  VariableDefinition(e) {\n    var r = \"\";\n    if (e.description) {\n      r += u.StringValue(e.description) + \" \";\n    }\n    if (r += u.Variable(e.variable) + \": \" + _print(e.type), e.defaultValue) {\n      r += \" = \" + _print(e.defaultValue);\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", u.Directive);\n    }\n    return r;\n  },\n  Field(e) {\n    var r = e.alias ? e.alias.value + \": \" + e.name.value : e.name.value;\n    if (e.arguments && e.arguments.length) {\n      var i = mapJoin(e.arguments, \", \", u.Argument);\n      if (r.length + i.length + 2 > 80) {\n        r += \"(\" + (d += \"  \") + mapJoin(e.arguments, d, u.Argument) + (d = d.slice(0, -2)) + \")\";\n      } else {\n        r += \"(\" + i + \")\";\n      }\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", u.Directive);\n    }\n    if (e.selectionSet && e.selectionSet.selections.length) {\n      r += \" \" + u.SelectionSet(e.selectionSet);\n    }\n    return r;\n  },\n  StringValue(e) {\n    if (e.block) {\n      return printBlockString(e.value).replace(/\\n/g, d);\n    } else {\n      return printString(e.value);\n    }\n  },\n  BooleanValue: e => \"\" + e.value,\n  NullValue: e => \"null\",\n  IntValue: e => e.value,\n  FloatValue: e => e.value,\n  EnumValue: e => e.value,\n  Name: e => e.value,\n  Variable: e => \"$\" + e.name.value,\n  ListValue: e => \"[\" + mapJoin(e.values, \", \", _print) + \"]\",\n  ObjectValue: e => \"{\" + mapJoin(e.fields, \", \", u.ObjectField) + \"}\",\n  ObjectField: e => e.name.value + \": \" + _print(e.value),\n  Document(e) {\n    if (!e.definitions || !e.definitions.length) {\n      return \"\";\n    } else {\n      return mapJoin(e.definitions, \"\\n\\n\", _print);\n    }\n  },\n  SelectionSet: e => \"{\" + (d += \"  \") + mapJoin(e.selections, d, _print) + (d = d.slice(0, -2)) + \"}\",\n  Argument: e => e.name.value + \": \" + _print(e.value),\n  FragmentSpread(e) {\n    var r = \"...\" + e.name.value;\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", u.Directive);\n    }\n    return r;\n  },\n  InlineFragment(e) {\n    var r = \"...\";\n    if (e.typeCondition) {\n      r += \" on \" + e.typeCondition.name.value;\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", u.Directive);\n    }\n    return r += \" \" + u.SelectionSet(e.selectionSet);\n  },\n  FragmentDefinition(e) {\n    var r = \"\";\n    if (e.description) {\n      r += u.StringValue(e.description) + \"\\n\";\n    }\n    if (r += \"fragment \" + e.name.value, r += \" on \" + e.typeCondition.name.value, e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", u.Directive);\n    }\n    return r + \" \" + u.SelectionSet(e.selectionSet);\n  },\n  Directive(e) {\n    var r = \"@\" + e.name.value;\n    if (e.arguments && e.arguments.length) {\n      r += \"(\" + mapJoin(e.arguments, \", \", u.Argument) + \")\";\n    }\n    return r;\n  },\n  NamedType: e => e.name.value,\n  ListType: e => \"[\" + _print(e.type) + \"]\",\n  NonNullType: e => _print(e.type) + \"!\"\n};\n\nvar _print = e => u[e.kind](e);\n\nfunction print(e) {\n  return d = \"\\n\", u[e.kind] ? u[e.kind](e) : \"\";\n}\n\nfunction valueFromASTUntyped(e, r) {\n  switch (e.kind) {\n   case \"NullValue\":\n    return null;\n\n   case \"IntValue\":\n    return parseInt(e.value, 10);\n\n   case \"FloatValue\":\n    return parseFloat(e.value);\n\n   case \"StringValue\":\n   case \"EnumValue\":\n   case \"BooleanValue\":\n    return e.value;\n\n   case \"ListValue\":\n    var i = [];\n    for (var n = 0, t = e.values.length; n < t; n++) {\n      i.push(valueFromASTUntyped(e.values[n], r));\n    }\n    return i;\n\n   case \"ObjectValue\":\n    var a = Object.create(null);\n    for (var o = 0, l = e.fields.length; o < l; o++) {\n      var d = e.fields[o];\n      a[d.name.value] = valueFromASTUntyped(d.value, r);\n    }\n    return a;\n\n   case \"Variable\":\n    return r && r[e.name.value];\n  }\n}\n\nfunction valueFromTypeNode(e, r, i) {\n  if (\"Variable\" === e.kind) {\n    return i ? valueFromTypeNode(i[e.name.value], r, i) : void 0;\n  } else if (\"NonNullType\" === r.kind) {\n    return \"NullValue\" !== e.kind ? valueFromTypeNode(e, r, i) : void 0;\n  } else if (\"NullValue\" === e.kind) {\n    return null;\n  } else if (\"ListType\" === r.kind) {\n    if (\"ListValue\" === e.kind) {\n      var n = [];\n      for (var t = 0, a = e.values.length; t < a; t++) {\n        var o = valueFromTypeNode(e.values[t], r.type, i);\n        if (void 0 === o) {\n          return;\n        } else {\n          n.push(o);\n        }\n      }\n      return n;\n    }\n  } else if (\"NamedType\" === r.kind) {\n    switch (r.name.value) {\n     case \"Int\":\n     case \"Float\":\n     case \"String\":\n     case \"Bool\":\n      return r.name.value + \"Value\" === e.kind ? valueFromASTUntyped(e, i) : void 0;\n\n     default:\n      return valueFromASTUntyped(e, i);\n    }\n  }\n}\n\nfunction isSelectionNode(e) {\n  return \"Field\" === e.kind || \"FragmentSpread\" === e.kind || \"InlineFragment\" === e.kind;\n}\n\nfunction Source(e, r, i) {\n  return {\n    body: e,\n    name: r,\n    locationOffset: i || {\n      line: 1,\n      column: 1\n    }\n  };\n}\n\n\n//# sourceMappingURL=graphql.web.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs\n");

/***/ })

};
;