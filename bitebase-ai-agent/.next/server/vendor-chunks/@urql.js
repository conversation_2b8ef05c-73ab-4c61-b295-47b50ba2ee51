"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@urql";
exports.ids = ["vendor-chunks/@urql"];
exports.modules = {

/***/ "(ssr)/./node_modules/@urql/core/dist/urql-core-chunk.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@urql/core/dist/urql-core-chunk.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   C: () => (/* binding */ CombinedError),\n/* harmony export */   a: () => (/* binding */ makeFetchBody),\n/* harmony export */   b: () => (/* binding */ makeErrorResult),\n/* harmony export */   c: () => (/* binding */ mergeResultPatch),\n/* harmony export */   d: () => (/* binding */ makeFetchURL),\n/* harmony export */   e: () => (/* binding */ makeFetchOptions),\n/* harmony export */   f: () => (/* binding */ makeFetchSource),\n/* harmony export */   g: () => (/* binding */ getOperationType),\n/* harmony export */   h: () => (/* binding */ createRequest),\n/* harmony export */   i: () => (/* binding */ stringifyVariables),\n/* harmony export */   j: () => (/* binding */ getOperationName),\n/* harmony export */   k: () => (/* binding */ keyDocument),\n/* harmony export */   m: () => (/* binding */ makeResult),\n/* harmony export */   s: () => (/* binding */ stringifyDocument)\n/* harmony export */ });\n/* harmony import */ var _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @0no-co/graphql.web */ \"(ssr)/./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs\");\n/* harmony import */ var wonka__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! wonka */ \"(ssr)/./node_modules/wonka/dist/wonka.mjs\");\n\n\n\n\nvar rehydrateGraphQlError = r => {\n  if (r && \"string\" == typeof r.message && (r.extensions || \"GraphQLError\" === r.name)) {\n    return r;\n  } else if (\"object\" == typeof r && \"string\" == typeof r.message) {\n    return new _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.GraphQLError(r.message, r.nodes, r.source, r.positions, r.path, r, r.extensions || {});\n  } else {\n    return new _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.GraphQLError(r);\n  }\n};\n\nclass CombinedError extends Error {\n  constructor(e) {\n    var r = (e.graphQLErrors || []).map(rehydrateGraphQlError);\n    var t = ((e, r) => {\n      var t = \"\";\n      if (e) {\n        return `[Network] ${e.message}`;\n      }\n      if (r) {\n        for (var a = 0, n = r.length; a < n; a++) {\n          if (t) {\n            t += \"\\n\";\n          }\n          t += `[GraphQL] ${r[a].message}`;\n        }\n      }\n      return t;\n    })(e.networkError, r);\n    super(t);\n    this.name = \"CombinedError\";\n    this.message = t;\n    this.graphQLErrors = r;\n    this.networkError = e.networkError;\n    this.response = e.response;\n  }\n  toString() {\n    return this.message;\n  }\n}\n\nvar phash = (e, r) => {\n  var t = 0 | (r || 5381);\n  for (var a = 0, n = 0 | e.length; a < n; a++) {\n    t = (t << 5) + t + e.charCodeAt(a);\n  }\n  return t;\n};\n\nvar i = new Set;\n\nvar f = new WeakMap;\n\nvar stringify = (e, r) => {\n  if (null === e || i.has(e)) {\n    return \"null\";\n  } else if (\"object\" != typeof e) {\n    return JSON.stringify(e) || \"\";\n  } else if (e.toJSON) {\n    return stringify(e.toJSON(), r);\n  } else if (Array.isArray(e)) {\n    var t = \"[\";\n    for (var a = 0, n = e.length; a < n; a++) {\n      if (t.length > 1) {\n        t += \",\";\n      }\n      t += stringify(e[a], r) || \"null\";\n    }\n    return t += \"]\";\n  } else if (!r && (d !== NoopConstructor && e instanceof d || l !== NoopConstructor && e instanceof l)) {\n    return \"null\";\n  }\n  var o = Object.keys(e).sort();\n  if (!o.length && e.constructor && Object.getPrototypeOf(e).constructor !== Object.prototype.constructor) {\n    var s = f.get(e) || Math.random().toString(36).slice(2);\n    f.set(e, s);\n    return stringify({\n      __key: s\n    }, r);\n  }\n  i.add(e);\n  var c = \"{\";\n  for (var v = 0, u = o.length; v < u; v++) {\n    var p = stringify(e[o[v]], r);\n    if (p) {\n      if (c.length > 1) {\n        c += \",\";\n      }\n      c += stringify(o[v], r) + \":\" + p;\n    }\n  }\n  i.delete(e);\n  return c += \"}\";\n};\n\nvar extract = (e, r, t) => {\n  if (null == t || \"object\" != typeof t || t.toJSON || i.has(t)) {} else if (Array.isArray(t)) {\n    for (var a = 0, n = t.length; a < n; a++) {\n      extract(e, `${r}.${a}`, t[a]);\n    }\n  } else if (t instanceof d || t instanceof l) {\n    e.set(r, t);\n  } else {\n    i.add(t);\n    for (var o in t) {\n      extract(e, `${r}.${o}`, t[o]);\n    }\n  }\n};\n\nvar stringifyVariables = (e, r) => {\n  i.clear();\n  return stringify(e, r || !1);\n};\n\nclass NoopConstructor {}\n\nvar d = \"undefined\" != typeof File ? File : NoopConstructor;\n\nvar l = \"undefined\" != typeof Blob ? Blob : NoopConstructor;\n\nvar c = /(\"{3}[\\s\\S]*\"{3}|\"(?:\\\\.|[^\"])*\")/g;\n\nvar v = /(?:#[^\\n\\r]+)?(?:[\\r\\n]+|$)/g;\n\nvar replaceOutsideStrings = (e, r) => r % 2 == 0 ? e.replace(v, \"\\n\") : e;\n\nvar sanitizeDocument = e => e.split(c).map(replaceOutsideStrings).join(\"\").trim();\n\nvar u = new Map;\n\nvar p = new Map;\n\nvar stringifyDocument = e => {\n  var t;\n  if (\"string\" == typeof e) {\n    t = sanitizeDocument(e);\n  } else if (e.loc && p.get(e.__key) === e) {\n    t = e.loc.source.body;\n  } else {\n    t = u.get(e) || sanitizeDocument((0,_0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.print)(e));\n    u.set(e, t);\n  }\n  if (\"string\" != typeof e && !e.loc) {\n    e.loc = {\n      start: 0,\n      end: t.length,\n      source: {\n        body: t,\n        name: \"gql\",\n        locationOffset: {\n          line: 1,\n          column: 1\n        }\n      }\n    };\n  }\n  return t;\n};\n\nvar hashDocument = e => {\n  var r;\n  if (e.documentId) {\n    r = phash(e.documentId);\n  } else {\n    r = phash(stringifyDocument(e));\n    if (e.definitions) {\n      var t = getOperationName(e);\n      if (t) {\n        r = phash(`\\n# ${t}`, r);\n      }\n    }\n  }\n  return r;\n};\n\nvar keyDocument = e => {\n  var r;\n  var t;\n  if (\"string\" == typeof e) {\n    r = hashDocument(e);\n    t = p.get(r) || (0,_0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.parse)(e, {\n      noLocation: !0\n    });\n  } else {\n    r = e.__key || hashDocument(e);\n    t = p.get(r) || e;\n  }\n  if (!t.loc) {\n    stringifyDocument(t);\n  }\n  t.__key = r;\n  p.set(r, t);\n  return t;\n};\n\nvar createRequest = (e, r, t) => {\n  var a = r || {};\n  var n = keyDocument(e);\n  var o = stringifyVariables(a, !0);\n  var s = n.__key;\n  if (\"{}\" !== o) {\n    s = phash(o, s);\n  }\n  return {\n    key: s,\n    query: n,\n    variables: a,\n    extensions: t\n  };\n};\n\nvar getOperationName = e => {\n  for (var r = 0, a = e.definitions.length; r < a; r++) {\n    var n = e.definitions[r];\n    if (n.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.Kind.OPERATION_DEFINITION) {\n      return n.name ? n.name.value : void 0;\n    }\n  }\n};\n\nvar getOperationType = e => {\n  for (var r = 0, a = e.definitions.length; r < a; r++) {\n    var n = e.definitions[r];\n    if (n.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.Kind.OPERATION_DEFINITION) {\n      return n.operation;\n    }\n  }\n};\n\nvar makeResult = (e, r, t) => {\n  if (!(\"data\" in r || \"errors\" in r && Array.isArray(r.errors))) {\n    throw new Error(\"No Content\");\n  }\n  var a = \"subscription\" === e.kind;\n  return {\n    operation: e,\n    data: r.data,\n    error: Array.isArray(r.errors) ? new CombinedError({\n      graphQLErrors: r.errors,\n      response: t\n    }) : void 0,\n    extensions: r.extensions ? {\n      ...r.extensions\n    } : void 0,\n    hasNext: null == r.hasNext ? a : r.hasNext,\n    stale: !1\n  };\n};\n\nvar deepMerge = (e, r) => {\n  if (\"object\" == typeof e && null != e) {\n    if (Array.isArray(e)) {\n      e = [ ...e ];\n      for (var t = 0, a = r.length; t < a; t++) {\n        e[t] = deepMerge(e[t], r[t]);\n      }\n      return e;\n    }\n    if (!e.constructor || e.constructor === Object) {\n      e = {\n        ...e\n      };\n      for (var n in r) {\n        e[n] = deepMerge(e[n], r[n]);\n      }\n      return e;\n    }\n  }\n  return r;\n};\n\nvar mergeResultPatch = (e, r, t, a) => {\n  var n = e.error ? e.error.graphQLErrors : [];\n  var o = !!e.extensions || !!(r.payload || r).extensions;\n  var s = {\n    ...e.extensions,\n    ...(r.payload || r).extensions\n  };\n  var i = r.incremental;\n  if (\"path\" in r) {\n    i = [ r ];\n  }\n  var f = {\n    data: e.data\n  };\n  if (i) {\n    var _loop = function() {\n      var e = i[d];\n      if (Array.isArray(e.errors)) {\n        n.push(...e.errors);\n      }\n      if (e.extensions) {\n        Object.assign(s, e.extensions);\n        o = !0;\n      }\n      var r = \"data\";\n      var t = f;\n      var l = [];\n      if (e.path) {\n        l = e.path;\n      } else if (a) {\n        var c = a.find((r => r.id === e.id));\n        if (e.subPath) {\n          l = [ ...c.path, ...e.subPath ];\n        } else {\n          l = c.path;\n        }\n      }\n      for (var v = 0, u = l.length; v < u; r = l[v++]) {\n        t = t[r] = Array.isArray(t[r]) ? [ ...t[r] ] : {\n          ...t[r]\n        };\n      }\n      if (e.items) {\n        var p = +r >= 0 ? r : 0;\n        for (var h = 0, y = e.items.length; h < y; h++) {\n          t[p + h] = deepMerge(t[p + h], e.items[h]);\n        }\n      } else if (void 0 !== e.data) {\n        t[r] = deepMerge(t[r], e.data);\n      }\n    };\n    for (var d = 0, l = i.length; d < l; d++) {\n      _loop();\n    }\n  } else {\n    f.data = (r.payload || r).data || e.data;\n    n = r.errors || r.payload && r.payload.errors || n;\n  }\n  return {\n    operation: e.operation,\n    data: f.data,\n    error: n.length ? new CombinedError({\n      graphQLErrors: n,\n      response: t\n    }) : void 0,\n    extensions: o ? s : void 0,\n    hasNext: null != r.hasNext ? r.hasNext : e.hasNext,\n    stale: !1\n  };\n};\n\nvar makeErrorResult = (e, r, t) => ({\n  operation: e,\n  data: void 0,\n  error: new CombinedError({\n    networkError: r,\n    response: t\n  }),\n  extensions: void 0,\n  hasNext: !1,\n  stale: !1\n});\n\nfunction makeFetchBody(e) {\n  var r = {\n    query: void 0,\n    documentId: void 0,\n    operationName: getOperationName(e.query),\n    variables: e.variables || void 0,\n    extensions: e.extensions\n  };\n  if (\"documentId\" in e.query && e.query.documentId && (!e.query.definitions || !e.query.definitions.length)) {\n    r.documentId = e.query.documentId;\n  } else if (!e.extensions || !e.extensions.persistedQuery || e.extensions.persistedQuery.miss) {\n    r.query = stringifyDocument(e.query);\n  }\n  return r;\n}\n\nvar makeFetchURL = (e, r) => {\n  var t = \"query\" === e.kind && e.context.preferGetMethod;\n  if (!t || !r) {\n    return e.context.url;\n  }\n  var a = splitOutSearchParams(e.context.url);\n  for (var n in r) {\n    var o = r[n];\n    if (o) {\n      a[1].set(n, \"object\" == typeof o ? stringifyVariables(o) : o);\n    }\n  }\n  var s = a.join(\"?\");\n  if (s.length > 2047 && \"force\" !== t) {\n    e.context.preferGetMethod = !1;\n    return e.context.url;\n  }\n  return s;\n};\n\nvar splitOutSearchParams = e => {\n  var r = e.indexOf(\"?\");\n  return r > -1 ? [ e.slice(0, r), new URLSearchParams(e.slice(r + 1)) ] : [ e, new URLSearchParams ];\n};\n\nvar serializeBody = (e, r) => {\n  if (r && !(\"query\" === e.kind && !!e.context.preferGetMethod)) {\n    var t = stringifyVariables(r);\n    var a = (e => {\n      var r = new Map;\n      if (d !== NoopConstructor || l !== NoopConstructor) {\n        i.clear();\n        extract(r, \"variables\", e);\n      }\n      return r;\n    })(r.variables);\n    if (a.size) {\n      var n = new FormData;\n      n.append(\"operations\", t);\n      n.append(\"map\", stringifyVariables({\n        ...[ ...a.keys() ].map((e => [ e ]))\n      }));\n      var o = 0;\n      for (var s of a.values()) {\n        n.append(\"\" + o++, s);\n      }\n      return n;\n    }\n    return t;\n  }\n};\n\nvar makeFetchOptions = (e, r) => {\n  var t = {\n    accept: \"subscription\" === e.kind ? \"text/event-stream, multipart/mixed\" : \"application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed\"\n  };\n  var a = (\"function\" == typeof e.context.fetchOptions ? e.context.fetchOptions() : e.context.fetchOptions) || {};\n  if (a.headers) {\n    if ((e => \"has\" in e && !Object.keys(e).length)(a.headers)) {\n      a.headers.forEach(((e, r) => {\n        t[r] = e;\n      }));\n    } else if (Array.isArray(a.headers)) {\n      a.headers.forEach(((e, r) => {\n        if (Array.isArray(e)) {\n          if (t[e[0]]) {\n            t[e[0]] = `${t[e[0]]},${e[1]}`;\n          } else {\n            t[e[0]] = e[1];\n          }\n        } else {\n          t[r] = e;\n        }\n      }));\n    } else {\n      for (var n in a.headers) {\n        t[n.toLowerCase()] = a.headers[n];\n      }\n    }\n  }\n  var o = serializeBody(e, r);\n  if (\"string\" == typeof o && !t[\"content-type\"]) {\n    t[\"content-type\"] = \"application/json\";\n  }\n  return {\n    ...a,\n    method: o ? \"POST\" : \"GET\",\n    body: o,\n    headers: t\n  };\n};\n\nvar h = /boundary=\"?([^=\";]+)\"?/i;\n\nvar y = /data: ?([^\\n]+)/;\n\nasync function* streamBody(e) {\n  if (e.body[Symbol.asyncIterator]) {\n    for await (var r of e.body) {\n      yield r;\n    }\n  } else {\n    var t = e.body.getReader();\n    var a;\n    try {\n      while (!(a = await t.read()).done) {\n        yield a.value;\n      }\n    } finally {\n      t.cancel();\n    }\n  }\n}\n\nasync function* streamToBoundedChunks(e, r) {\n  var t = \"undefined\" != typeof TextDecoder ? new TextDecoder : null;\n  var a = \"\";\n  var n;\n  for await (var o of e) {\n    a += \"Buffer\" === o.constructor.name ? o.toString() : t.decode(o, {\n      stream: !0\n    });\n    while ((n = a.indexOf(r)) > -1) {\n      yield a.slice(0, n);\n      a = a.slice(n + r.length);\n    }\n  }\n}\n\nasync function* fetchOperation(e, r, t) {\n  var a = !0;\n  var n = null;\n  var o;\n  try {\n    yield await Promise.resolve();\n    var s = (o = await (e.context.fetch || fetch)(r, t)).headers.get(\"Content-Type\") || \"\";\n    var i;\n    if (/multipart\\/mixed/i.test(s)) {\n      i = async function* parseMultipartMixed(e, r) {\n        var t = e.match(h);\n        var a = \"--\" + (t ? t[1] : \"-\");\n        var n = !0;\n        var o;\n        for await (var s of streamToBoundedChunks(streamBody(r), \"\\r\\n\" + a)) {\n          if (n) {\n            n = !1;\n            var i = s.indexOf(a);\n            if (i > -1) {\n              s = s.slice(i + a.length);\n            } else {\n              continue;\n            }\n          }\n          try {\n            yield o = JSON.parse(s.slice(s.indexOf(\"\\r\\n\\r\\n\") + 4));\n          } catch (e) {\n            if (!o) {\n              throw e;\n            }\n          }\n          if (o && !1 === o.hasNext) {\n            break;\n          }\n        }\n        if (o && !1 !== o.hasNext) {\n          yield {\n            hasNext: !1\n          };\n        }\n      }(s, o);\n    } else if (/text\\/event-stream/i.test(s)) {\n      i = async function* parseEventStream(e) {\n        var r;\n        for await (var t of streamToBoundedChunks(streamBody(e), \"\\n\\n\")) {\n          var a = t.match(y);\n          if (a) {\n            var n = a[1];\n            try {\n              yield r = JSON.parse(n);\n            } catch (e) {\n              if (!r) {\n                throw e;\n              }\n            }\n            if (r && !1 === r.hasNext) {\n              break;\n            }\n          }\n        }\n        if (r && !1 !== r.hasNext) {\n          yield {\n            hasNext: !1\n          };\n        }\n      }(o);\n    } else if (!/text\\//i.test(s)) {\n      i = async function* parseJSON(e) {\n        yield JSON.parse(await e.text());\n      }(o);\n    } else {\n      i = async function* parseMaybeJSON(e) {\n        var r = await e.text();\n        try {\n          var t = JSON.parse(r);\n          if (true) {\n            console.warn('Found response with content-type \"text/plain\" but it had a valid \"application/json\" response.');\n          }\n          yield t;\n        } catch (e) {\n          throw new Error(r);\n        }\n      }(o);\n    }\n    var f;\n    for await (var d of i) {\n      if (d.pending && !n) {\n        f = d.pending;\n      } else if (d.pending) {\n        f = [ ...f, ...d.pending ];\n      }\n      n = n ? mergeResultPatch(n, d, o, f) : makeResult(e, d, o);\n      a = !1;\n      yield n;\n      a = !0;\n    }\n    if (!n) {\n      yield n = makeResult(e, {}, o);\n    }\n  } catch (r) {\n    if (!a) {\n      throw r;\n    }\n    yield makeErrorResult(e, o && (o.status < 200 || o.status >= 300) && o.statusText ? new Error(o.statusText) : r, o);\n  }\n}\n\nfunction makeFetchSource(e, r, t) {\n  var a;\n  if (\"undefined\" != typeof AbortController) {\n    t.signal = (a = new AbortController).signal;\n  }\n  return (0,wonka__WEBPACK_IMPORTED_MODULE_1__.onEnd)((() => {\n    if (a) {\n      a.abort();\n    }\n  }))((0,wonka__WEBPACK_IMPORTED_MODULE_1__.filter)((e => !!e))((0,wonka__WEBPACK_IMPORTED_MODULE_1__.fromAsyncIterable)(fetchOperation(e, r, t))));\n}\n\n\n//# sourceMappingURL=urql-core-chunk.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@urql/core/dist/urql-core-chunk.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@urql/core/dist/urql-core.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@urql/core/dist/urql-core.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ C),\n/* harmony export */   CombinedError: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.C),\n/* harmony export */   cacheExchange: () => (/* binding */ cacheExchange),\n/* harmony export */   composeExchanges: () => (/* binding */ composeExchanges),\n/* harmony export */   createClient: () => (/* binding */ Q),\n/* harmony export */   createRequest: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   debugExchange: () => (/* binding */ debugExchange),\n/* harmony export */   errorExchange: () => (/* binding */ mapExchange),\n/* harmony export */   fetchExchange: () => (/* binding */ fetchExchange),\n/* harmony export */   formatDocument: () => (/* binding */ formatDocument),\n/* harmony export */   getOperationName: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   gql: () => (/* binding */ gql),\n/* harmony export */   makeErrorResult: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   makeOperation: () => (/* binding */ makeOperation),\n/* harmony export */   makeResult: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   mapExchange: () => (/* binding */ mapExchange),\n/* harmony export */   mergeResultPatch: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   ssrExchange: () => (/* binding */ ssrExchange),\n/* harmony export */   stringifyDocument: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   stringifyVariables: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.i),\n/* harmony export */   subscriptionExchange: () => (/* binding */ subscriptionExchange)\n/* harmony export */ });\n/* harmony import */ var _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @0no-co/graphql.web */ \"(ssr)/./node_modules/@0no-co/graphql.web/dist/graphql.web.mjs\");\n/* harmony import */ var _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./urql-core-chunk.mjs */ \"(ssr)/./node_modules/@urql/core/dist/urql-core-chunk.mjs\");\n/* harmony import */ var wonka__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wonka */ \"(ssr)/./node_modules/wonka/dist/wonka.mjs\");\n\n\n\n\n\n\n\n\nvar collectTypes = (e, r) => {\n  if (Array.isArray(e)) {\n    for (var t = 0, n = e.length; t < n; t++) {\n      collectTypes(e[t], r);\n    }\n  } else if (\"object\" == typeof e && null !== e) {\n    for (var a in e) {\n      if (\"__typename\" === a && \"string\" == typeof e[a]) {\n        r.add(e[a]);\n      } else {\n        collectTypes(e[a], r);\n      }\n    }\n  }\n  return r;\n};\n\nvar formatNode = r => {\n  if (\"definitions\" in r) {\n    var t = [];\n    for (var n = 0, a = r.definitions.length; n < a; n++) {\n      var i = formatNode(r.definitions[n]);\n      t.push(i);\n    }\n    return {\n      ...r,\n      definitions: t\n    };\n  }\n  if (\"directives\" in r && r.directives && r.directives.length) {\n    var o = [];\n    var s = {};\n    for (var c = 0, u = r.directives.length; c < u; c++) {\n      var p = r.directives[c];\n      var d = p.name.value;\n      if (\"_\" !== d[0]) {\n        o.push(p);\n      } else {\n        d = d.slice(1);\n      }\n      s[d] = p;\n    }\n    r = {\n      ...r,\n      directives: o,\n      _directives: s\n    };\n  }\n  if (\"selectionSet\" in r) {\n    var v = [];\n    var l = r.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.OPERATION_DEFINITION;\n    if (r.selectionSet) {\n      for (var f = 0, h = r.selectionSet.selections.length; f < h; f++) {\n        var k = r.selectionSet.selections[f];\n        l = l || k.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.FIELD && \"__typename\" === k.name.value && !k.alias;\n        var y = formatNode(k);\n        v.push(y);\n      }\n      if (!l) {\n        v.push({\n          kind: _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.FIELD,\n          name: {\n            kind: _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.NAME,\n            value: \"__typename\"\n          },\n          _generated: !0\n        });\n      }\n      return {\n        ...r,\n        selectionSet: {\n          ...r.selectionSet,\n          selections: v\n        }\n      };\n    }\n  }\n  return r;\n};\n\nvar I = new Map;\n\nvar formatDocument = e => {\n  var t = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.k)(e);\n  var n = I.get(t.__key);\n  if (!n) {\n    I.set(t.__key, n = formatNode(t));\n    Object.defineProperty(n, \"__key\", {\n      value: t.__key,\n      enumerable: !1\n    });\n  }\n  return n;\n};\n\nfunction withPromise(e) {\n  var source$ = r => e(r);\n  source$.toPromise = () => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.toPromise)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.take)(1)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => !e.stale && !e.hasNext))(source$)));\n  source$.then = (e, r) => source$.toPromise().then(e, r);\n  source$.subscribe = e => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.subscribe)(e)(source$);\n  return source$;\n}\n\nfunction makeOperation(e, r, t) {\n  return {\n    ...r,\n    kind: e,\n    context: r.context ? {\n      ...r.context,\n      ...t\n    } : t || r.context\n  };\n}\n\nvar addMetadata = (e, r) => makeOperation(e.kind, e, {\n  meta: {\n    ...e.context.meta,\n    ...r\n  }\n});\n\nvar noop = () => {};\n\nfunction gql(n) {\n  var a = new Map;\n  var i = [];\n  var o = [];\n  var s = Array.isArray(n) ? n[0] : n || \"\";\n  for (var c = 1; c < arguments.length; c++) {\n    var u = arguments[c];\n    if (u && u.definitions) {\n      o.push(u);\n    } else {\n      s += u;\n    }\n    s += arguments[0][c];\n  }\n  o.unshift((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.k)(s));\n  for (var p = 0; p < o.length; p++) {\n    for (var d = 0; d < o[p].definitions.length; d++) {\n      var v = o[p].definitions[d];\n      if (v.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.FRAGMENT_DEFINITION) {\n        var l = v.name.value;\n        var f = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(v);\n        if (!a.has(l)) {\n          a.set(l, f);\n          i.push(v);\n        } else if ( true && a.get(l) !== f) {\n          console.warn(\"[WARNING: Duplicate Fragment] A fragment with name `\" + l + \"` already exists in this document.\\nWhile fragment names may not be unique across your source, each name must be unique per document.\");\n        }\n      } else {\n        i.push(v);\n      }\n    }\n  }\n  return (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.k)({\n    kind: _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.DOCUMENT,\n    definitions: i\n  });\n}\n\nvar shouldSkip = ({kind: e}) => \"mutation\" !== e && \"query\" !== e;\n\nvar mapTypeNames = e => {\n  var r = formatDocument(e.query);\n  if (r !== e.query) {\n    var t = makeOperation(e.kind, e);\n    t.query = r;\n    return t;\n  } else {\n    return e;\n  }\n};\n\nvar cacheExchange = ({forward: e, client: r, dispatchDebug: t}) => {\n  var a = new Map;\n  var i = new Map;\n  var isOperationCached = e => \"query\" === e.kind && \"network-only\" !== e.context.requestPolicy && (\"cache-only\" === e.context.requestPolicy || a.has(e.key));\n  return o => {\n    var s = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)((e => {\n      var i = a.get(e.key);\n       true && t({\n        operation: e,\n        ...i ? {\n          type: \"cacheHit\",\n          message: \"The result was successfully retrieved from the cache\"\n        } : {\n          type: \"cacheMiss\",\n          message: \"The result could not be retrieved from the cache\"\n        },\n        source: \"cacheExchange\"\n      });\n      var o = i || (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(e, {\n        data: null\n      });\n      o = {\n        ...o,\n        operation: addMetadata(e, {\n          cacheOutcome: i ? \"hit\" : \"miss\"\n        })\n      };\n      if (\"cache-and-network\" === e.context.requestPolicy) {\n        o.stale = !0;\n        reexecuteOperation(r, e);\n      }\n      return o;\n    }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => !shouldSkip(e) && isOperationCached(e)))(o));\n    var c = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((e => {\n      var {operation: n} = e;\n      if (!n) {\n        return;\n      }\n      var o = n.context.additionalTypenames || [];\n      if (\"subscription\" !== e.operation.kind) {\n        o = (e => [ ...collectTypes(e, new Set) ])(e.data).concat(o);\n      }\n      if (\"mutation\" === e.operation.kind || \"subscription\" === e.operation.kind) {\n        var s = new Set;\n         true && t({\n          type: \"cacheInvalidation\",\n          message: `The following typenames have been invalidated: ${o}`,\n          operation: n,\n          data: {\n            typenames: o,\n            response: e\n          },\n          source: \"cacheExchange\"\n        });\n        for (var c = 0; c < o.length; c++) {\n          var u = o[c];\n          var p = i.get(u);\n          if (!p) {\n            i.set(u, p = new Set);\n          }\n          for (var d of p.values()) {\n            s.add(d);\n          }\n          p.clear();\n        }\n        for (var v of s.values()) {\n          if (a.has(v)) {\n            n = a.get(v).operation;\n            a.delete(v);\n            reexecuteOperation(r, n);\n          }\n        }\n      } else if (\"query\" === n.kind && e.data) {\n        a.set(n.key, e);\n        for (var l = 0; l < o.length; l++) {\n          var f = o[l];\n          var h = i.get(f);\n          if (!h) {\n            i.set(f, h = new Set);\n          }\n          h.add(n.key);\n        }\n      }\n    }))(e((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"query\" !== e.kind || \"cache-only\" !== e.context.requestPolicy))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)((e => addMetadata(e, {\n      cacheOutcome: \"miss\"\n    })))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ (0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)(mapTypeNames)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => !shouldSkip(e) && !isOperationCached(e)))(o)), (0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => shouldSkip(e)))(o) ])))));\n    return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ s, c ]);\n  };\n};\n\nvar reexecuteOperation = (e, r) => e.reexecuteOperation(makeOperation(r.kind, r, {\n  requestPolicy: \"network-only\"\n}));\n\nvar T = new Set;\n\nvar ssrExchange = (e = {}) => {\n  var r = !!e.staleWhileRevalidate;\n  var t = !!e.includeExtensions;\n  var n = {};\n  var i = [];\n  var invalidate = e => {\n    i.push(e.operation.key);\n    if (1 === i.length) {\n      Promise.resolve().then((() => {\n        var e;\n        while (e = i.shift()) {\n          n[e] = null;\n        }\n      }));\n    }\n  };\n  var ssr = ({client: i, forward: o}) => s => {\n    var c = e && \"boolean\" == typeof e.isClient ? !!e.isClient : !i.suspense;\n    var u = o((0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)(mapTypeNames)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" === e.kind || !n[e.key] || !!n[e.key].hasNext || \"network-only\" === e.context.requestPolicy))(s)));\n    var p = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)((e => {\n      var o = ((e, r, t) => ({\n        operation: e,\n        data: r.data ? JSON.parse(r.data) : void 0,\n        extensions: t && r.extensions ? JSON.parse(r.extensions) : void 0,\n        error: r.error ? new _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.C({\n          networkError: r.error.networkError ? new Error(r.error.networkError) : void 0,\n          graphQLErrors: r.error.graphQLErrors\n        }) : void 0,\n        stale: !1,\n        hasNext: !!r.hasNext\n      }))(e, n[e.key], t);\n      if (r && !T.has(e.key)) {\n        o.stale = !0;\n        T.add(e.key);\n        reexecuteOperation(i, e);\n      }\n      return {\n        ...o,\n        operation: addMetadata(e, {\n          cacheOutcome: \"hit\"\n        })\n      };\n    }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" !== e.kind && !!n[e.key] && \"network-only\" !== e.context.requestPolicy))(s));\n    if (!c) {\n      u = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((e => {\n        var {operation: r} = e;\n        if (\"mutation\" !== r.kind) {\n          var a = ((e, r) => {\n            var t = {\n              hasNext: e.hasNext\n            };\n            if (void 0 !== e.data) {\n              t.data = JSON.stringify(e.data);\n            }\n            if (r && void 0 !== e.extensions) {\n              t.extensions = JSON.stringify(e.extensions);\n            }\n            if (e.error) {\n              t.error = {\n                graphQLErrors: e.error.graphQLErrors.map((e => {\n                  if (!e.path && !e.extensions) {\n                    return e.message;\n                  }\n                  return {\n                    message: e.message,\n                    path: e.path,\n                    extensions: e.extensions\n                  };\n                }))\n              };\n              if (e.error.networkError) {\n                t.error.networkError = \"\" + e.error.networkError;\n              }\n            }\n            return t;\n          })(e, t);\n          n[r.key] = a;\n        }\n      }))(u);\n    } else {\n      p = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)(invalidate)(p);\n    }\n    return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ u, p ]);\n  };\n  ssr.restoreData = e => {\n    for (var r in e) {\n      if (null !== n[r]) {\n        n[r] = e[r];\n      }\n    }\n  };\n  ssr.extractData = () => {\n    var e = {};\n    for (var r in n) {\n      if (null != n[r]) {\n        e[r] = n[r];\n      }\n    }\n    return e;\n  };\n  if (e && e.initialState) {\n    ssr.restoreData(e.initialState);\n  }\n  return ssr;\n};\n\nvar subscriptionExchange = ({forwardSubscription: e, enableAllOperations: r, isSubscriptionOperation: t}) => ({client: a, forward: i}) => {\n  var u = t || (e => \"subscription\" === e.kind || !!r && (\"query\" === e.kind || \"mutation\" === e.kind));\n  return r => {\n    var t = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.mergeMap)((t => {\n      var {key: i} = t;\n      var u = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" === e.kind && e.key === i))(r);\n      return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.takeUntil)(u)((r => {\n        var t = e((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(r), r);\n        return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.make)((e => {\n          var i = !1;\n          var o;\n          var u;\n          function nextResult(t) {\n            e.next(u = u ? (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.c)(u, t) : (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(r, t));\n          }\n          Promise.resolve().then((() => {\n            if (i) {\n              return;\n            }\n            o = t.subscribe({\n              next: nextResult,\n              error(t) {\n                if (Array.isArray(t)) {\n                  nextResult({\n                    errors: t\n                  });\n                } else {\n                  e.next((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(r, t));\n                }\n                e.complete();\n              },\n              complete() {\n                if (!i) {\n                  i = !0;\n                  if (\"subscription\" === r.kind) {\n                    a.reexecuteOperation(makeOperation(\"teardown\", r, r.context));\n                  }\n                  if (u && u.hasNext) {\n                    nextResult({\n                      hasNext: !1\n                    });\n                  }\n                  e.complete();\n                }\n              }\n            });\n          }));\n          return () => {\n            i = !0;\n            if (o) {\n              o.unsubscribe();\n            }\n          };\n        }));\n      })(t));\n    }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" !== e.kind && u(e)))(r));\n    var p = i((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" === e.kind || !u(e)))(r));\n    return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ t, p ]);\n  };\n};\n\nvar debugExchange = ({forward: e}) => {\n  if (false) {} else {\n    return r => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((e => console.debug(\"[Exchange debug]: Completed operation: \", e)))(e((0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((e => console.debug(\"[Exchange debug]: Incoming operation: \", e)))(r)));\n  }\n};\n\nvar fetchExchange = ({forward: e, dispatchDebug: r}) => t => {\n  var n = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.mergeMap)((e => {\n    var n = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(e);\n    var a = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(e, n);\n    var i = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(e, n);\n     true && r({\n      type: \"fetchRequest\",\n      message: \"A fetch request is being executed.\",\n      operation: e,\n      data: {\n        url: a,\n        fetchOptions: i\n      },\n      source: \"fetchExchange\"\n    });\n    var s = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.takeUntil)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => \"teardown\" === r.kind && r.key === e.key))(t))((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(e, a, i));\n    if (true) {\n      return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.onPush)((t => {\n        var n = !t.data ? t.error : void 0;\n         true && r({\n          type: n ? \"fetchError\" : \"fetchSuccess\",\n          message: `A ${n ? \"failed\" : \"successful\"} fetch response has been returned.`,\n          operation: e,\n          data: {\n            url: a,\n            fetchOptions: i,\n            value: n || t\n          },\n          source: \"fetchExchange\"\n        });\n      }))(s);\n    }\n    return s;\n  }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" !== e.kind && (\"subscription\" !== e.kind || !!e.context.fetchSubscriptions)))(t));\n  var a = e((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" === e.kind || \"subscription\" === e.kind && !e.context.fetchSubscriptions))(t));\n  return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ n, a ]);\n};\n\nvar composeExchanges = e => ({client: r, forward: t, dispatchDebug: n}) => e.reduceRight(((e, t) => {\n  var a = !1;\n  return t({\n    client: r,\n    forward(r) {\n      if (true) {\n        if (a) {\n          throw new Error(\"forward() must only be called once in each Exchange.\");\n        }\n        a = !0;\n      }\n      return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.share)(e((0,wonka__WEBPACK_IMPORTED_MODULE_2__.share)(r)));\n    },\n    dispatchDebug(e) {\n       true && n({\n        timestamp: Date.now(),\n        source: t.name,\n        ...e\n      });\n    }\n  });\n}), t);\n\nvar mapExchange = ({onOperation: e, onResult: r, onError: t}) => ({forward: n}) => a => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.mergeMap)((e => {\n  if (t && e.error) {\n    t(e.error, e.operation);\n  }\n  var n = r && r(e) || e;\n  return \"then\" in n ? (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromPromise)(n) : (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)(n);\n}))(n((0,wonka__WEBPACK_IMPORTED_MODULE_2__.mergeMap)((r => {\n  var t = e && e(r) || r;\n  return \"then\" in t ? (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromPromise)(t) : (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)(t);\n}))(a)));\n\nvar fallbackExchange = ({dispatchDebug: e}) => r => {\n  if (true) {\n    r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((r => {\n      if (\"teardown\" !== r.kind && \"production\" !== \"development\") {\n        var t = `No exchange has handled operations of kind \"${r.kind}\". Check whether you've added an exchange responsible for these operations.`;\n         true && e({\n          type: \"fallbackCatch\",\n          message: t,\n          operation: r,\n          source: \"fallbackExchange\"\n        });\n        console.warn(t);\n      }\n    }))(r);\n  }\n  return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => !1))(r);\n};\n\nvar C = function Client(e) {\n  if ( true && !e.url) {\n    throw new Error(\"You are creating an urql-client without a url.\");\n  }\n  var r = 0;\n  var t = new Map;\n  var n = new Map;\n  var a = new Set;\n  var i = [];\n  var o = {\n    url: e.url,\n    fetchSubscriptions: e.fetchSubscriptions,\n    fetchOptions: e.fetchOptions,\n    fetch: e.fetch,\n    preferGetMethod: e.preferGetMethod,\n    requestPolicy: e.requestPolicy || \"cache-first\"\n  };\n  var s = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.makeSubject)();\n  function nextOperation(e) {\n    if (\"mutation\" === e.kind || \"teardown\" === e.kind || !a.has(e.key)) {\n      if (\"teardown\" === e.kind) {\n        a.delete(e.key);\n      } else if (\"mutation\" !== e.kind) {\n        a.add(e.key);\n      }\n      s.next(e);\n    }\n  }\n  var c = !1;\n  function dispatchOperation(e) {\n    if (e) {\n      nextOperation(e);\n    }\n    if (!c) {\n      c = !0;\n      while (c && (e = i.shift())) {\n        nextOperation(e);\n      }\n      c = !1;\n    }\n  }\n  var makeResultSource = e => {\n    var r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.takeUntil)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => \"teardown\" === r.kind && r.key === e.key))(s.source))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => r.operation.kind === e.kind && r.operation.key === e.key && (!r.operation.context._instance || r.operation.context._instance === e.context._instance)))(E));\n    if (\"query\" !== e.kind) {\n      r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.takeWhile)((e => !!e.hasNext), !0)(r);\n    } else {\n      r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.switchMap)((r => {\n        var t = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)(r);\n        return r.stale || r.hasNext ? t : (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ t, (0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)((() => {\n          r.stale = !0;\n          return r;\n        }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.take)(1)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => r.key === e.key))(s.source))) ]);\n      }))(r);\n    }\n    if (\"mutation\" !== e.kind) {\n      r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.onEnd)((() => {\n        a.delete(e.key);\n        t.delete(e.key);\n        n.delete(e.key);\n        c = !1;\n        for (var r = i.length - 1; r >= 0; r--) {\n          if (i[r].key === e.key) {\n            i.splice(r, 1);\n          }\n        }\n        nextOperation(makeOperation(\"teardown\", e, e.context));\n      }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.onPush)((r => {\n        if (r.stale) {\n          if (!r.hasNext) {\n            a.delete(e.key);\n          } else {\n            for (var n = 0; n < i.length; n++) {\n              var o = i[n];\n              if (o.key === r.operation.key) {\n                a.delete(o.key);\n                break;\n              }\n            }\n          }\n        } else if (!r.hasNext) {\n          a.delete(e.key);\n        }\n        t.set(e.key, r);\n      }))(r));\n    } else {\n      r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.onStart)((() => {\n        nextOperation(e);\n      }))(r);\n    }\n    return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.share)(r);\n  };\n  var u = this instanceof Client ? this : Object.create(Client.prototype);\n  var p = Object.assign(u, {\n    suspense: !!e.suspense,\n    operations$: s.source,\n    reexecuteOperation(e) {\n      if (\"teardown\" === e.kind) {\n        dispatchOperation(e);\n      } else if (\"mutation\" === e.kind) {\n        i.push(e);\n        Promise.resolve().then(dispatchOperation);\n      } else if (n.has(e.key)) {\n        var r = !1;\n        for (var t = 0; t < i.length; t++) {\n          if (i[t].key === e.key) {\n            i[t] = e;\n            r = !0;\n          }\n        }\n        if (!(r || a.has(e.key) && \"network-only\" !== e.context.requestPolicy)) {\n          i.push(e);\n          Promise.resolve().then(dispatchOperation);\n        } else {\n          a.delete(e.key);\n          Promise.resolve().then(dispatchOperation);\n        }\n      }\n    },\n    createRequestOperation(e, t, n) {\n      if (!n) {\n        n = {};\n      }\n      var a;\n      if ( true && \"teardown\" !== e && (a = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.g)(t.query)) !== e) {\n        throw new Error(`Expected operation of type \"${e}\" but found \"${a}\"`);\n      }\n      return makeOperation(e, t, {\n        _instance: \"mutation\" === e ? r = r + 1 | 0 : void 0,\n        ...o,\n        ...n,\n        requestPolicy: n.requestPolicy || o.requestPolicy,\n        suspense: n.suspense || !1 !== n.suspense && p.suspense\n      });\n    },\n    executeRequestOperation(e) {\n      if (\"mutation\" === e.kind) {\n        return withPromise(makeResultSource(e));\n      }\n      return withPromise((0,wonka__WEBPACK_IMPORTED_MODULE_2__.lazy)((() => {\n        var r = n.get(e.key);\n        if (!r) {\n          n.set(e.key, r = makeResultSource(e));\n        }\n        r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.onStart)((() => {\n          dispatchOperation(e);\n        }))(r);\n        var a = t.get(e.key);\n        if (\"query\" === e.kind && a && (a.stale || a.hasNext)) {\n          return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.switchMap)(wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ r, (0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => r === t.get(e.key)))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)(a)) ]));\n        } else {\n          return r;\n        }\n      })));\n    },\n    executeQuery(e, r) {\n      var t = p.createRequestOperation(\"query\", e, r);\n      return p.executeRequestOperation(t);\n    },\n    executeSubscription(e, r) {\n      var t = p.createRequestOperation(\"subscription\", e, r);\n      return p.executeRequestOperation(t);\n    },\n    executeMutation(e, r) {\n      var t = p.createRequestOperation(\"mutation\", e, r);\n      return p.executeRequestOperation(t);\n    },\n    readQuery(e, r, t) {\n      var n = null;\n      (0,wonka__WEBPACK_IMPORTED_MODULE_2__.subscribe)((e => {\n        n = e;\n      }))(p.query(e, r, t)).unsubscribe();\n      return n;\n    },\n    query: (e, r, t) => p.executeQuery((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.h)(e, r), t),\n    subscription: (e, r, t) => p.executeSubscription((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.h)(e, r), t),\n    mutation: (e, r, t) => p.executeMutation((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.h)(e, r), t)\n  });\n  var d = noop;\n  if (true) {\n    var {next: f, source: x} = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.makeSubject)();\n    p.subscribeToDebugTarget = e => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.subscribe)(e)(x);\n    d = f;\n  }\n  var w = composeExchanges(e.exchanges);\n  var E = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.share)(w({\n    client: p,\n    dispatchDebug: d,\n    forward: fallbackExchange({\n      dispatchDebug: d\n    })\n  })(s.source));\n  (0,wonka__WEBPACK_IMPORTED_MODULE_2__.publish)(E);\n  return p;\n};\n\nvar Q = C;\n\n\n//# sourceMappingURL=urql-core.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@urql/core/dist/urql-core.mjs\n");

/***/ })

};
;