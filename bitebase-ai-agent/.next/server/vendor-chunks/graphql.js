"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/graphql";
exports.ids = ["vendor-chunks/graphql"];
exports.modules = {

/***/ "(ssr)/./node_modules/graphql/error/GraphQLError.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/graphql/error/GraphQLError.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLError: () => (/* binding */ GraphQLError),\n/* harmony export */   formatError: () => (/* binding */ formatError),\n/* harmony export */   printError: () => (/* binding */ printError)\n/* harmony export */ });\n/* harmony import */ var _jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jsutils/isObjectLike.mjs */ \"(ssr)/./node_modules/graphql/jsutils/isObjectLike.mjs\");\n/* harmony import */ var _language_location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../language/location.mjs */ \"(ssr)/./node_modules/graphql/language/location.mjs\");\n/* harmony import */ var _language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../language/printLocation.mjs */ \"(ssr)/./node_modules/graphql/language/printLocation.mjs\");\n\n\n\n\nfunction toNormalizedOptions(args) {\n  const firstArg = args[0];\n\n  if (firstArg == null || 'kind' in firstArg || 'length' in firstArg) {\n    return {\n      nodes: firstArg,\n      source: args[1],\n      positions: args[2],\n      path: args[3],\n      originalError: args[4],\n      extensions: args[5],\n    };\n  }\n\n  return firstArg;\n}\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nclass GraphQLError extends Error {\n  /**\n   * An array of `{ line, column }` locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n\n  /**\n   * @deprecated Please use the `GraphQLErrorOptions` constructor overload instead.\n   */\n  constructor(message, ...rawArgs) {\n    var _this$nodes, _nodeLocations$, _ref;\n\n    const { nodes, source, positions, path, originalError, extensions } =\n      toNormalizedOptions(rawArgs);\n    super(message);\n    this.name = 'GraphQLError';\n    this.path = path !== null && path !== void 0 ? path : undefined;\n    this.originalError =\n      originalError !== null && originalError !== void 0\n        ? originalError\n        : undefined; // Compute list of blame nodes.\n\n    this.nodes = undefinedIfEmpty(\n      Array.isArray(nodes) ? nodes : nodes ? [nodes] : undefined,\n    );\n    const nodeLocations = undefinedIfEmpty(\n      (_this$nodes = this.nodes) === null || _this$nodes === void 0\n        ? void 0\n        : _this$nodes.map((node) => node.loc).filter((loc) => loc != null),\n    ); // Compute locations in the source for the given nodes/positions.\n\n    this.source =\n      source !== null && source !== void 0\n        ? source\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : (_nodeLocations$ = nodeLocations[0]) === null ||\n          _nodeLocations$ === void 0\n        ? void 0\n        : _nodeLocations$.source;\n    this.positions =\n      positions !== null && positions !== void 0\n        ? positions\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => loc.start);\n    this.locations =\n      positions && source\n        ? positions.map((pos) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(source, pos))\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(loc.source, loc.start));\n    const originalExtensions = (0,_jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__.isObjectLike)(\n      originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions,\n    )\n      ? originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions\n      : undefined;\n    this.extensions =\n      (_ref =\n        extensions !== null && extensions !== void 0\n          ? extensions\n          : originalExtensions) !== null && _ref !== void 0\n        ? _ref\n        : Object.create(null); // Only properties prescribed by the spec should be enumerable.\n    // Keep the rest as non-enumerable.\n\n    Object.defineProperties(this, {\n      message: {\n        writable: true,\n        enumerable: true,\n      },\n      name: {\n        enumerable: false,\n      },\n      nodes: {\n        enumerable: false,\n      },\n      source: {\n        enumerable: false,\n      },\n      positions: {\n        enumerable: false,\n      },\n      originalError: {\n        enumerable: false,\n      },\n    }); // Include (non-enumerable) stack trace.\n\n    /* c8 ignore start */\n    // FIXME: https://github.com/graphql/graphql-js/issues/2317\n\n    if (\n      originalError !== null &&\n      originalError !== void 0 &&\n      originalError.stack\n    ) {\n      Object.defineProperty(this, 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true,\n      });\n    } else if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, GraphQLError);\n    } else {\n      Object.defineProperty(this, 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true,\n      });\n    }\n    /* c8 ignore stop */\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n\n  toString() {\n    let output = this.message;\n\n    if (this.nodes) {\n      for (const node of this.nodes) {\n        if (node.loc) {\n          output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printLocation)(node.loc);\n        }\n      }\n    } else if (this.source && this.locations) {\n      for (const location of this.locations) {\n        output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printSourceLocation)(this.source, location);\n      }\n    }\n\n    return output;\n  }\n\n  toJSON() {\n    const formattedError = {\n      message: this.message,\n    };\n\n    if (this.locations != null) {\n      formattedError.locations = this.locations;\n    }\n\n    if (this.path != null) {\n      formattedError.path = this.path;\n    }\n\n    if (this.extensions != null && Object.keys(this.extensions).length > 0) {\n      formattedError.extensions = this.extensions;\n    }\n\n    return formattedError;\n  }\n}\n\nfunction undefinedIfEmpty(array) {\n  return array === undefined || array.length === 0 ? undefined : array;\n}\n/**\n * See: https://spec.graphql.org/draft/#sec-Errors\n */\n\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n *\n * @deprecated Please use `error.toString` instead. Will be removed in v17\n */\nfunction printError(error) {\n  return error.toString();\n}\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n *\n * @deprecated Please use `error.toJSON` instead. Will be removed in v17\n */\n\nfunction formatError(error) {\n  return error.toJSON();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/error/GraphQLError.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/graphql/jsutils/invariant.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/jsutils/invariant.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: () => (/* binding */ invariant)\n/* harmony export */ });\nfunction invariant(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(\n      message != null ? message : 'Unexpected invariant triggered.',\n    );\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2ludmFyaWFudC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvbm9kZV9tb2R1bGVzL2dyYXBocWwvanN1dGlscy9pbnZhcmlhbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBpbnZhcmlhbnQoY29uZGl0aW9uLCBtZXNzYWdlKSB7XG4gIGNvbnN0IGJvb2xlYW5Db25kaXRpb24gPSBCb29sZWFuKGNvbmRpdGlvbik7XG5cbiAgaWYgKCFib29sZWFuQ29uZGl0aW9uKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgbWVzc2FnZSAhPSBudWxsID8gbWVzc2FnZSA6ICdVbmV4cGVjdGVkIGludmFyaWFudCB0cmlnZ2VyZWQuJyxcbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/jsutils/invariant.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/graphql/jsutils/isObjectLike.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/graphql/jsutils/isObjectLike.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectLike: () => (/* binding */ isObjectLike)\n/* harmony export */ });\n/**\n * Return true if `value` is object-like. A value is object-like if it's not\n * `null` and has a `typeof` result of \"object\".\n */\nfunction isObjectLike(value) {\n  return typeof value == 'object' && value !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2lzT2JqZWN0TGlrZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvbm9kZV9tb2R1bGVzL2dyYXBocWwvanN1dGlscy9pc09iamVjdExpa2UubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmV0dXJuIHRydWUgaWYgYHZhbHVlYCBpcyBvYmplY3QtbGlrZS4gQSB2YWx1ZSBpcyBvYmplY3QtbGlrZSBpZiBpdCdzIG5vdFxuICogYG51bGxgIGFuZCBoYXMgYSBgdHlwZW9mYCByZXN1bHQgb2YgXCJvYmplY3RcIi5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzT2JqZWN0TGlrZSh2YWx1ZSkge1xuICByZXR1cm4gdHlwZW9mIHZhbHVlID09ICdvYmplY3QnICYmIHZhbHVlICE9PSBudWxsO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/jsutils/isObjectLike.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/graphql/language/location.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/language/location.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocation: () => (/* binding */ getLocation)\n/* harmony export */ });\n/* harmony import */ var _jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsutils/invariant.mjs */ \"(ssr)/./node_modules/graphql/jsutils/invariant.mjs\");\n\nconst LineRegExp = /\\r\\n|[\\n\\r]/g;\n/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nfunction getLocation(source, position) {\n  let lastLineStart = 0;\n  let line = 1;\n\n  for (const match of source.body.matchAll(LineRegExp)) {\n    typeof match.index === 'number' || (0,_jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(false);\n\n    if (match.index >= position) {\n      break;\n    }\n\n    lastLineStart = match.index + match[0].length;\n    line += 1;\n  }\n\n  return {\n    line,\n    column: position + 1 - lastLineStart,\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9sYW5ndWFnZS9sb2NhdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7O0FBRUE7QUFDQSx1Q0FBdUMsaUVBQVM7O0FBRWhEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL3RlYW1zcGFjZS9zdHVkaW9zL3RoaXNfc3R1ZGlvL29wZW4tbXVsdGktYWdlbnQtY2FudmFzL2JpdGViYXNlX2FnZW50L25vZGVfbW9kdWxlcy9ncmFwaHFsL2xhbmd1YWdlL2xvY2F0aW9uLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICcuLi9qc3V0aWxzL2ludmFyaWFudC5tanMnO1xuY29uc3QgTGluZVJlZ0V4cCA9IC9cXHJcXG58W1xcblxccl0vZztcbi8qKlxuICogUmVwcmVzZW50cyBhIGxvY2F0aW9uIGluIGEgU291cmNlLlxuICovXG5cbi8qKlxuICogVGFrZXMgYSBTb3VyY2UgYW5kIGEgVVRGLTggY2hhcmFjdGVyIG9mZnNldCwgYW5kIHJldHVybnMgdGhlIGNvcnJlc3BvbmRpbmdcbiAqIGxpbmUgYW5kIGNvbHVtbiBhcyBhIFNvdXJjZUxvY2F0aW9uLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0TG9jYXRpb24oc291cmNlLCBwb3NpdGlvbikge1xuICBsZXQgbGFzdExpbmVTdGFydCA9IDA7XG4gIGxldCBsaW5lID0gMTtcblxuICBmb3IgKGNvbnN0IG1hdGNoIG9mIHNvdXJjZS5ib2R5Lm1hdGNoQWxsKExpbmVSZWdFeHApKSB7XG4gICAgdHlwZW9mIG1hdGNoLmluZGV4ID09PSAnbnVtYmVyJyB8fCBpbnZhcmlhbnQoZmFsc2UpO1xuXG4gICAgaWYgKG1hdGNoLmluZGV4ID49IHBvc2l0aW9uKSB7XG4gICAgICBicmVhaztcbiAgICB9XG5cbiAgICBsYXN0TGluZVN0YXJ0ID0gbWF0Y2guaW5kZXggKyBtYXRjaFswXS5sZW5ndGg7XG4gICAgbGluZSArPSAxO1xuICB9XG5cbiAgcmV0dXJuIHtcbiAgICBsaW5lLFxuICAgIGNvbHVtbjogcG9zaXRpb24gKyAxIC0gbGFzdExpbmVTdGFydCxcbiAgfTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/language/location.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/graphql/language/printLocation.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/graphql/language/printLocation.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   printLocation: () => (/* binding */ printLocation),\n/* harmony export */   printSourceLocation: () => (/* binding */ printSourceLocation)\n/* harmony export */ });\n/* harmony import */ var _location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./location.mjs */ \"(ssr)/./node_modules/graphql/language/location.mjs\");\n\n\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\nfunction printLocation(location) {\n  return printSourceLocation(\n    location.source,\n    (0,_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(location.source, location.start),\n  );\n}\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\n\nfunction printSourceLocation(source, sourceLocation) {\n  const firstLineColumnOffset = source.locationOffset.column - 1;\n  const body = ''.padStart(firstLineColumnOffset) + source.body;\n  const lineIndex = sourceLocation.line - 1;\n  const lineOffset = source.locationOffset.line - 1;\n  const lineNum = sourceLocation.line + lineOffset;\n  const columnOffset = sourceLocation.line === 1 ? firstLineColumnOffset : 0;\n  const columnNum = sourceLocation.column + columnOffset;\n  const locationStr = `${source.name}:${lineNum}:${columnNum}\\n`;\n  const lines = body.split(/\\r\\n|[\\n\\r]/g);\n  const locationLine = lines[lineIndex]; // Special case for minified documents\n\n  if (locationLine.length > 120) {\n    const subLineIndex = Math.floor(columnNum / 80);\n    const subLineColumnNum = columnNum % 80;\n    const subLines = [];\n\n    for (let i = 0; i < locationLine.length; i += 80) {\n      subLines.push(locationLine.slice(i, i + 80));\n    }\n\n    return (\n      locationStr +\n      printPrefixedLines([\n        [`${lineNum} |`, subLines[0]],\n        ...subLines.slice(1, subLineIndex + 1).map((subLine) => ['|', subLine]),\n        ['|', '^'.padStart(subLineColumnNum)],\n        ['|', subLines[subLineIndex + 1]],\n      ])\n    );\n  }\n\n  return (\n    locationStr +\n    printPrefixedLines([\n      // Lines specified like this: [\"prefix\", \"string\"],\n      [`${lineNum - 1} |`, lines[lineIndex - 1]],\n      [`${lineNum} |`, locationLine],\n      ['|', '^'.padStart(columnNum)],\n      [`${lineNum + 1} |`, lines[lineIndex + 1]],\n    ])\n  );\n}\n\nfunction printPrefixedLines(lines) {\n  const existingLines = lines.filter(([_, line]) => line !== undefined);\n  const padLen = Math.max(...existingLines.map(([prefix]) => prefix.length));\n  return existingLines\n    .map(([prefix, line]) => prefix.padStart(padLen) + (line ? ' ' + line : ''))\n    .join('\\n');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/graphql/language/printLocation.mjs\n");

/***/ })

};
;