"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-raw";
exports.ids = ["vendor-chunks/hast-util-raw"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-raw/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/hast-util-raw/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   raw: () => (/* binding */ raw)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-from-parse5 */ \"(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\");\n/* harmony import */ var hast_util_to_parse5__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! hast-util-to-parse5 */ \"(ssr)/./node_modules/hast-util-to-parse5/lib/index.js\");\n/* harmony import */ var html_void_elements__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! html-void-elements */ \"(ssr)/./node_modules/html-void-elements/index.js\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parse5 */ \"(ssr)/./node_modules/parse5/dist/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/web-namespaces/index.js\");\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/zwitch/index.js\");\n/**\n * @import {Options} from 'hast-util-raw'\n * @import {Comment, Doctype, Element, Nodes, RootContent, Root, Text} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {DefaultTreeAdapterMap, ParserOptions} from 'parse5'\n * @import {Point} from 'unist'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {(node: Nodes) => undefined} handle\n *   Add a hast node to the parser.\n * @property {Options} options\n *   User configuration.\n * @property {Parser<DefaultTreeAdapterMap>} parser\n *   Current parser.\n * @property {boolean} stitches\n *   Whether there are stitches.\n */\n\n/**\n * @typedef Stitch\n *   Custom comment-like value we pass through parse5, which contains a\n *   replacement node that we’ll swap back in afterwards.\n * @property {'comment'} type\n *   Node type.\n * @property {{stitch: Nodes}} value\n *   Replacement value.\n */\n\n\n\n\n\n\n\n\n\n\n\nconst gfmTagfilterExpression =\n  /<(\\/?)(iframe|noembed|noframes|plaintext|script|style|textarea|title|xmp)(?=[\\t\\n\\f\\r />])/gi\n\n// Node types associated with MDX.\n// <https://github.com/mdx-js/mdx/blob/8a56312/packages/mdx/lib/node-types.js>\nconst knownMdxNames = new Set([\n  'mdxFlowExpression',\n  'mdxJsxFlowElement',\n  'mdxJsxTextElement',\n  'mdxTextExpression',\n  'mdxjsEsm'\n])\n\n/** @type {ParserOptions<DefaultTreeAdapterMap>} */\nconst parseOptions = {sourceCodeLocationInfo: true, scriptingEnabled: false}\n\n/**\n * Pass a hast tree through an HTML parser, which will fix nesting, and turn\n * raw nodes into actual nodes.\n *\n * @param {Nodes} tree\n *   Original hast tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   Parsed again tree.\n */\nfunction raw(tree, options) {\n  const document = documentMode(tree)\n  /** @type {(node: Nodes, state: State) => undefined} */\n  const one = (0,zwitch__WEBPACK_IMPORTED_MODULE_1__.zwitch)('type', {\n    handlers: {root, element, text, comment, doctype, raw: handleRaw},\n    unknown\n  })\n\n  /** @type {State} */\n  const state = {\n    parser: document\n      ? new parse5__WEBPACK_IMPORTED_MODULE_0__.Parser(parseOptions)\n      : parse5__WEBPACK_IMPORTED_MODULE_0__.Parser.getFragmentParser(undefined, parseOptions),\n    handle(node) {\n      one(node, state)\n    },\n    stitches: false,\n    options: options || {}\n  }\n\n  one(tree, state)\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)())\n\n  const p5 = document ? state.parser.document : state.parser.getFragment()\n  const result = (0,hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_3__.fromParse5)(p5, {\n    // To do: support `space`?\n    file: state.options.file\n  })\n\n  if (state.stitches) {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(result, 'comment', function (node, index, parent) {\n      const stitch = /** @type {Stitch} */ (/** @type {unknown} */ (node))\n      if (stitch.value.stitch && parent && index !== undefined) {\n        /** @type {Array<RootContent>} */\n        const siblings = parent.children\n        // @ts-expect-error: assume the stitch is allowed.\n        siblings[index] = stitch.value.stitch\n        return index\n      }\n    })\n  }\n\n  // Unpack if possible and when not given a `root`.\n  if (\n    result.type === 'root' &&\n    result.children.length === 1 &&\n    result.children[0].type === tree.type\n  ) {\n    return result.children[0]\n  }\n\n  return result\n}\n\n/**\n * Transform all nodes\n *\n * @param {Array<RootContent>} nodes\n *   hast content.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction all(nodes, state) {\n  let index = -1\n\n  /* istanbul ignore else - invalid nodes, see rehypejs/rehype-raw#7. */\n  if (nodes) {\n    while (++index < nodes.length) {\n      state.handle(nodes[index])\n    }\n  }\n}\n\n/**\n * Transform a root.\n *\n * @param {Root} node\n *   hast root node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction root(node, state) {\n  all(node.children, state)\n}\n\n/**\n * Transform an element.\n *\n * @param {Element} node\n *   hast element node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction element(node, state) {\n  startTag(node, state)\n\n  all(node.children, state)\n\n  endTag(node, state)\n}\n\n/**\n * Transform a text.\n *\n * @param {Text} node\n *   hast text node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction text(node, state) {\n  // Allow `DATA` through `PLAINTEXT`,\n  // but when hanging in a tag for example,\n  // switch back to `DATA`.\n  // Note: `State` is not exposed by `parse5`, so these numbers are fragile.\n  // See: <https://github.com/inikulin/parse5/blob/46cba43/packages/parse5/lib/tokenizer/index.ts#L58>\n  if (state.parser.tokenizer.state > 4) {\n    state.parser.tokenizer.state = 0\n  }\n\n  /** @type {Token.CharacterToken} */\n  const token = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.CHARACTER,\n    chars: node.value,\n    location: createParse5Location(node)\n  }\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a doctype.\n *\n * @param {Doctype} node\n *   hast doctype node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction doctype(node, state) {\n  /** @type {Token.DoctypeToken} */\n  const token = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.DOCTYPE,\n    name: 'html',\n    forceQuirks: false,\n    publicId: '',\n    systemId: '',\n    location: createParse5Location(node)\n  }\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a stitch.\n *\n * @param {Nodes} node\n *   unknown node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction stitch(node, state) {\n  // Mark that there are stitches, so we need to walk the tree and revert them.\n  state.stitches = true\n\n  /** @type {Nodes} */\n  const clone = cloneWithoutChildren(node)\n\n  // Recurse, because to somewhat handle `[<x>]</x>` (where `[]` denotes the\n  // passed through node).\n  if ('children' in node && 'children' in clone) {\n    // Root in root out.\n    const fakeRoot = /** @type {Root} */ (\n      raw({type: 'root', children: node.children}, state.options)\n    )\n    clone.children = fakeRoot.children\n  }\n\n  // Hack: `value` is supposed to be a string, but as none of the tools\n  // (`parse5` or `hast-util-from-parse5`) looks at it, we can pass nodes\n  // through.\n  comment({type: 'comment', value: {stitch: clone}}, state)\n}\n\n/**\n * Transform a comment (or stitch).\n *\n * @param {Comment | Stitch} node\n *   hast comment node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction comment(node, state) {\n  /** @type {string} */\n  // @ts-expect-error: we pass stitches through.\n  const data = node.value\n\n  /** @type {Token.CommentToken} */\n  const token = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.COMMENT,\n    data,\n    location: createParse5Location(node)\n  }\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a raw node.\n *\n * @param {Raw} node\n *   hast raw node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction handleRaw(node, state) {\n  // Reset preprocessor:\n  // See: <https://github.com/inikulin/parse5/blob/6f7ca60/packages/parse5/lib/tokenizer/preprocessor.ts#L18-L31>.\n  state.parser.tokenizer.preprocessor.html = ''\n  state.parser.tokenizer.preprocessor.pos = -1\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.lastGapPos = -2\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.gapStack = []\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.skipNextNewLine = false\n  state.parser.tokenizer.preprocessor.lastChunkWritten = false\n  state.parser.tokenizer.preprocessor.endOfChunkHit = false\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.isEol = false\n\n  // Now pass `node.value`.\n  setPoint(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n\n  state.parser.tokenizer.write(\n    state.options.tagfilter\n      ? node.value.replace(gfmTagfilterExpression, '&lt;$1$2')\n      : node.value,\n    false\n  )\n  // @ts-expect-error: private.\n  state.parser.tokenizer._runParsingLoop()\n\n  // Character references hang, so if we ended there, we need to flush\n  // those too.\n  // We reset the preprocessor as if the document ends here.\n  // Then one single call to the relevant state does the trick, parse5\n  // consumes the whole token.\n\n  // Note: `State` is not exposed by `parse5`, so these numbers are fragile.\n  // See: <https://github.com/inikulin/parse5/blob/46cba43/packages/parse5/lib/tokenizer/index.ts#L58>\n  // Note: a change to `parse5`, which breaks this, was merged but not released.\n  // Investigate when it is.\n  // To do: remove next major.\n  /* c8 ignore next 12 -- removed in <https://github.com/inikulin/parse5/pull/897> */\n  if (\n    state.parser.tokenizer.state === 72 /* NAMED_CHARACTER_REFERENCE */ ||\n    // @ts-expect-error: removed.\n    state.parser.tokenizer.state === 78 /* NUMERIC_CHARACTER_REFERENCE_END */\n  ) {\n    state.parser.tokenizer.preprocessor.lastChunkWritten = true\n    /** @type {number} */\n    // @ts-expect-error: private.\n    const cp = state.parser.tokenizer._consume()\n    // @ts-expect-error: private.\n    state.parser.tokenizer._callState(cp)\n  }\n}\n\n/**\n * Crash on an unknown node.\n *\n * @param {unknown} node_\n *   unknown node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Never.\n */\nfunction unknown(node_, state) {\n  const node = /** @type {Nodes} */ (node_)\n\n  if (\n    state.options.passThrough &&\n    state.options.passThrough.includes(node.type)\n  ) {\n    stitch(node, state)\n  } else {\n    let extra = ''\n\n    if (knownMdxNames.has(node.type)) {\n      extra =\n        \". It looks like you are using MDX nodes with `hast-util-raw` (or `rehype-raw`). If you use this because you are using remark or rehype plugins that inject `'html'` nodes, then please raise an issue with that plugin, as its a bad and slow idea. If you use this because you are using markdown syntax, then you have to configure this utility (or plugin) to pass through these nodes (see `passThrough` in docs), but you can also migrate to use the MDX syntax\"\n    }\n\n    throw new Error('Cannot compile `' + node.type + '` node' + extra)\n  }\n}\n\n/**\n * Reset the tokenizer of a parser.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Point | undefined} point\n *   Point.\n * @returns {undefined}\n *   Nothing.\n */\nfunction resetTokenizer(state, point) {\n  setPoint(state, point)\n\n  // Process final characters if they’re still there after hibernating.\n  /** @type {Token.CharacterToken} */\n  // @ts-expect-error: private.\n  const token = state.parser.tokenizer.currentCharacterToken\n\n  if (token && token.location) {\n    token.location.endLine = state.parser.tokenizer.preprocessor.line\n    token.location.endCol = state.parser.tokenizer.preprocessor.col + 1\n    token.location.endOffset = state.parser.tokenizer.preprocessor.offset + 1\n    // @ts-expect-error: private.\n    state.parser.currentToken = token\n    // @ts-expect-error: private.\n    state.parser._processToken(state.parser.currentToken)\n  }\n\n  // Reset tokenizer:\n  // See: <https://github.com/inikulin/parse5/blob/6f7ca60/packages/parse5/lib/tokenizer/index.ts#L187-L223>.\n  // Especially putting it back in the `data` state is useful: some elements,\n  // like textareas and iframes, change the state.\n  // See GH-7.\n  // But also if broken HTML is in `raw`, and then a correct element is given.\n  // See GH-11.\n  // @ts-expect-error: private.\n  state.parser.tokenizer.paused = false\n  // @ts-expect-error: private.\n  state.parser.tokenizer.inLoop = false\n\n  // Note: don’t reset `state`, `inForeignNode`, or `lastStartTagName`, we\n  // manually update those when needed.\n  state.parser.tokenizer.active = false\n  // @ts-expect-error: private.\n  state.parser.tokenizer.returnState = parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.DATA\n  // @ts-expect-error: private.\n  state.parser.tokenizer.charRefCode = -1\n  // @ts-expect-error: private.\n  state.parser.tokenizer.consumedAfterSnapshot = -1\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentLocation = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentCharacterToken = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentToken = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentAttr = {name: '', value: ''}\n}\n\n/**\n * Set current location.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Point | undefined} point\n *   Point.\n * @returns {undefined}\n *   Nothing.\n */\nfunction setPoint(state, point) {\n  if (point && point.offset !== undefined) {\n    /** @type {Token.Location} */\n    const location = {\n      startLine: point.line,\n      startCol: point.column,\n      startOffset: point.offset,\n      endLine: -1,\n      endCol: -1,\n      endOffset: -1\n    }\n\n    // @ts-expect-error: private.\n    // type-coverage:ignore-next-line\n    state.parser.tokenizer.preprocessor.lineStartPos = -point.column + 1 // Looks weird, but ensures we get correct positional info.\n    state.parser.tokenizer.preprocessor.droppedBufferSize = point.offset\n    state.parser.tokenizer.preprocessor.line = point.line\n    // @ts-expect-error: private.\n    state.parser.tokenizer.currentLocation = location\n  }\n}\n\n/**\n * Emit a start tag.\n *\n * @param {Element} node\n *   Element.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction startTag(node, state) {\n  const tagName = node.tagName.toLowerCase()\n\n  // Ignore tags if we’re in plain text.\n  if (state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.PLAINTEXT) return\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n\n  const current = state.parser.openElements.current\n  let ns = 'namespaceURI' in current ? current.namespaceURI : web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.html\n\n  if (ns === web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.html && tagName === 'svg') {\n    ns = web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.svg\n  }\n\n  const result = (0,hast_util_to_parse5__WEBPACK_IMPORTED_MODULE_6__.toParse5)(\n    // Shallow clone to not delve into `children`: we only need the attributes.\n    {...node, children: []},\n    {space: ns === web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.svg ? 'svg' : 'html'}\n  )\n\n  /** @type {Token.TagToken} */\n  const tag = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.START_TAG,\n    tagName,\n    tagID: parse5__WEBPACK_IMPORTED_MODULE_0__.html.getTagID(tagName),\n    // We always send start and end tags.\n    selfClosing: false,\n    ackSelfClosing: false,\n    // Always element.\n    /* c8 ignore next */\n    attrs: 'attrs' in result ? result.attrs : [],\n    location: createParse5Location(node)\n  }\n\n  // The HTML parsing algorithm works by doing half of the state management in\n  // the tokenizer and half in the parser.\n  // We can’t use the tokenizer here, as we don’t have strings.\n  // So we act *as if* the tokenizer emits tokens:\n\n  // @ts-expect-error: private.\n  state.parser.currentToken = tag\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n\n  // …but then we still need a bunch of work that the tokenizer would normally\n  // do, such as:\n\n  // Set a tag name, similar to how the tokenizer would do it.\n  state.parser.tokenizer.lastStartTagName = tagName\n\n  // `inForeignNode` is correctly set by the parser.\n}\n\n/**\n * Emit an end tag.\n *\n * @param {Element} node\n *   Element.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction endTag(node, state) {\n  const tagName = node.tagName.toLowerCase()\n  // Do not emit closing tags for HTML void elements.\n  if (\n    !state.parser.tokenizer.inForeignNode &&\n    html_void_elements__WEBPACK_IMPORTED_MODULE_7__.htmlVoidElements.includes(tagName)\n  ) {\n    return\n  }\n\n  // Ignore tags if we’re in plain text.\n  if (state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.PLAINTEXT) return\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointEnd)(node))\n\n  /** @type {Token.TagToken} */\n  const tag = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.END_TAG,\n    tagName,\n    tagID: parse5__WEBPACK_IMPORTED_MODULE_0__.html.getTagID(tagName),\n    selfClosing: false,\n    ackSelfClosing: false,\n    attrs: [],\n    location: createParse5Location(node)\n  }\n\n  // The HTML parsing algorithm works by doing half of the state management in\n  // the tokenizer and half in the parser.\n  // We can’t use the tokenizer here, as we don’t have strings.\n  // So we act *as if* the tokenizer emits tokens:\n\n  // @ts-expect-error: private.\n  state.parser.currentToken = tag\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n\n  // …but then we still need a bunch of work that the tokenizer would normally\n  // do, such as:\n\n  // Switch back to the data state after alternative states that don’t accept\n  // tags:\n  if (\n    // Current element is closed.\n    tagName === state.parser.tokenizer.lastStartTagName &&\n    // `<textarea>` and `<title>`\n    (state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.RCDATA ||\n      // `<iframe>`, `<noembed>`, `<noframes>`, `<style>`, `<xmp>`\n      state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.RAWTEXT ||\n      // `<script>`\n      state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.SCRIPT_DATA)\n    // Note: `<plaintext>` not needed, as it’s the last element.\n  ) {\n    state.parser.tokenizer.state = parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.DATA\n  }\n}\n\n/**\n * Check if `node` represents a whole document or a fragment.\n *\n * @param {Nodes} node\n *   hast node.\n * @returns {boolean}\n *   Whether this represents a whole document or a fragment.\n */\nfunction documentMode(node) {\n  const head = node.type === 'root' ? node.children[0] : node\n  return Boolean(\n    head &&\n      (head.type === 'doctype' ||\n        (head.type === 'element' && head.tagName.toLowerCase() === 'html'))\n  )\n}\n\n/**\n * Get a `parse5` location from a node.\n *\n * @param {Nodes | Stitch} node\n *   hast node.\n * @returns {Token.Location}\n *   `parse5` location.\n */\nfunction createParse5Location(node) {\n  const start = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node) || {\n    line: undefined,\n    column: undefined,\n    offset: undefined\n  }\n  const end = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointEnd)(node) || {\n    line: undefined,\n    column: undefined,\n    offset: undefined\n  }\n\n  /** @type {Record<keyof Token.Location, number | undefined>} */\n  const location = {\n    startLine: start.line,\n    startCol: start.column,\n    startOffset: start.offset,\n    endLine: end.line,\n    endCol: end.column,\n    endOffset: end.offset\n  }\n\n  // @ts-expect-error: unist point values can be `undefined` in hast, which\n  // `parse5` types don’t want.\n  return location\n}\n\n/**\n * @template {Nodes} NodeType\n *   Node type.\n * @param {NodeType} node\n *   Node to clone.\n * @returns {NodeType}\n *   Cloned node, without children.\n */\nfunction cloneWithoutChildren(node) {\n  return 'children' in node\n    ? (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({...node, children: []})\n    : (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-raw/lib/index.js\n");

/***/ })

};
;