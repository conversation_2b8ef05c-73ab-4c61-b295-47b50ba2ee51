"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/kleur";
exports.ids = ["vendor-chunks/kleur"];
exports.modules = {

/***/ "(ssr)/./node_modules/kleur/index.mjs":
/*!**************************************!*\
  !*** ./node_modules/kleur/index.mjs ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\n\nlet FORCE_COLOR, NODE_DISABLE_COLORS, NO_COLOR, TERM, isTTY=true;\nif (typeof process !== 'undefined') {\n\t({ FORCE_COLOR, NODE_DISABLE_COLORS, NO_COLOR, TERM } = process.env || {});\n\tisTTY = process.stdout && process.stdout.isTTY;\n}\n\nconst $ = {\n\tenabled: !NODE_DISABLE_COLORS && NO_COLOR == null && TERM !== 'dumb' && (\n\t\tFORCE_COLOR != null && FORCE_COLOR !== '0' || isTTY\n\t),\n\n\t// modifiers\n\treset: init(0, 0),\n\tbold: init(1, 22),\n\tdim: init(2, 22),\n\titalic: init(3, 23),\n\tunderline: init(4, 24),\n\tinverse: init(7, 27),\n\thidden: init(8, 28),\n\tstrikethrough: init(9, 29),\n\n\t// colors\n\tblack: init(30, 39),\n\tred: init(31, 39),\n\tgreen: init(32, 39),\n\tyellow: init(33, 39),\n\tblue: init(34, 39),\n\tmagenta: init(35, 39),\n\tcyan: init(36, 39),\n\twhite: init(37, 39),\n\tgray: init(90, 39),\n\tgrey: init(90, 39),\n\n\t// background colors\n\tbgBlack: init(40, 49),\n\tbgRed: init(41, 49),\n\tbgGreen: init(42, 49),\n\tbgYellow: init(43, 49),\n\tbgBlue: init(44, 49),\n\tbgMagenta: init(45, 49),\n\tbgCyan: init(46, 49),\n\tbgWhite: init(47, 49)\n};\n\nfunction run(arr, str) {\n\tlet i=0, tmp, beg='', end='';\n\tfor (; i < arr.length; i++) {\n\t\ttmp = arr[i];\n\t\tbeg += tmp.open;\n\t\tend += tmp.close;\n\t\tif (!!~str.indexOf(tmp.close)) {\n\t\t\tstr = str.replace(tmp.rgx, tmp.close + tmp.open);\n\t\t}\n\t}\n\treturn beg + str + end;\n}\n\nfunction chain(has, keys) {\n\tlet ctx = { has, keys };\n\n\tctx.reset = $.reset.bind(ctx);\n\tctx.bold = $.bold.bind(ctx);\n\tctx.dim = $.dim.bind(ctx);\n\tctx.italic = $.italic.bind(ctx);\n\tctx.underline = $.underline.bind(ctx);\n\tctx.inverse = $.inverse.bind(ctx);\n\tctx.hidden = $.hidden.bind(ctx);\n\tctx.strikethrough = $.strikethrough.bind(ctx);\n\n\tctx.black = $.black.bind(ctx);\n\tctx.red = $.red.bind(ctx);\n\tctx.green = $.green.bind(ctx);\n\tctx.yellow = $.yellow.bind(ctx);\n\tctx.blue = $.blue.bind(ctx);\n\tctx.magenta = $.magenta.bind(ctx);\n\tctx.cyan = $.cyan.bind(ctx);\n\tctx.white = $.white.bind(ctx);\n\tctx.gray = $.gray.bind(ctx);\n\tctx.grey = $.grey.bind(ctx);\n\n\tctx.bgBlack = $.bgBlack.bind(ctx);\n\tctx.bgRed = $.bgRed.bind(ctx);\n\tctx.bgGreen = $.bgGreen.bind(ctx);\n\tctx.bgYellow = $.bgYellow.bind(ctx);\n\tctx.bgBlue = $.bgBlue.bind(ctx);\n\tctx.bgMagenta = $.bgMagenta.bind(ctx);\n\tctx.bgCyan = $.bgCyan.bind(ctx);\n\tctx.bgWhite = $.bgWhite.bind(ctx);\n\n\treturn ctx;\n}\n\nfunction init(open, close) {\n\tlet blk = {\n\t\topen: `\\x1b[${open}m`,\n\t\tclose: `\\x1b[${close}m`,\n\t\trgx: new RegExp(`\\\\x1b\\\\[${close}m`, 'g')\n\t};\n\treturn function (txt) {\n\t\tif (this !== void 0 && this.has !== void 0) {\n\t\t\t!!~this.has.indexOf(open) || (this.has.push(open),this.keys.push(blk));\n\t\t\treturn txt === void 0 ? this : $.enabled ? run(this.keys, txt+'') : txt+'';\n\t\t}\n\t\treturn txt === void 0 ? chain([open], [blk]) : $.enabled ? run([blk], txt+'') : txt+'';\n\t};\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ($);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/kleur/index.mjs\n");

/***/ })

};
;