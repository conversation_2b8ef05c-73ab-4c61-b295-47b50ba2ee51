"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/wonka";
exports.ids = ["vendor-chunks/wonka"];
exports.modules = {

/***/ "(ssr)/./node_modules/wonka/dist/wonka.mjs":
/*!*******************************************!*\
  !*** ./node_modules/wonka/dist/wonka.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buffer: () => (/* binding */ buffer),\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   concatAll: () => (/* binding */ concatAll),\n/* harmony export */   concatMap: () => (/* binding */ concatMap),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   flatten: () => (/* binding */ mergeAll),\n/* harmony export */   forEach: () => (/* binding */ forEach),\n/* harmony export */   fromArray: () => (/* binding */ r),\n/* harmony export */   fromAsyncIterable: () => (/* binding */ fromAsyncIterable),\n/* harmony export */   fromCallbag: () => (/* binding */ fromCallbag),\n/* harmony export */   fromDomEvent: () => (/* binding */ fromDomEvent),\n/* harmony export */   fromIterable: () => (/* binding */ fromIterable),\n/* harmony export */   fromObservable: () => (/* binding */ fromObservable),\n/* harmony export */   fromPromise: () => (/* binding */ fromPromise),\n/* harmony export */   fromValue: () => (/* binding */ fromValue),\n/* harmony export */   interval: () => (/* binding */ interval),\n/* harmony export */   lazy: () => (/* binding */ lazy),\n/* harmony export */   make: () => (/* binding */ make),\n/* harmony export */   makeSubject: () => (/* binding */ makeSubject),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   merge: () => (/* binding */ merge),\n/* harmony export */   mergeAll: () => (/* binding */ mergeAll),\n/* harmony export */   mergeMap: () => (/* binding */ mergeMap),\n/* harmony export */   never: () => (/* binding */ never),\n/* harmony export */   onEnd: () => (/* binding */ onEnd),\n/* harmony export */   onPush: () => (/* binding */ onPush),\n/* harmony export */   onStart: () => (/* binding */ onStart),\n/* harmony export */   pipe: () => (/* binding */ pipe),\n/* harmony export */   publish: () => (/* binding */ publish),\n/* harmony export */   sample: () => (/* binding */ sample),\n/* harmony export */   scan: () => (/* binding */ scan),\n/* harmony export */   share: () => (/* binding */ share),\n/* harmony export */   skip: () => (/* binding */ skip),\n/* harmony export */   skipUntil: () => (/* binding */ skipUntil),\n/* harmony export */   skipWhile: () => (/* binding */ skipWhile),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   switchAll: () => (/* binding */ switchAll),\n/* harmony export */   switchMap: () => (/* binding */ switchMap),\n/* harmony export */   take: () => (/* binding */ take),\n/* harmony export */   takeLast: () => (/* binding */ takeLast),\n/* harmony export */   takeUntil: () => (/* binding */ takeUntil),\n/* harmony export */   takeWhile: () => (/* binding */ takeWhile),\n/* harmony export */   tap: () => (/* binding */ onPush),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toAsyncIterable: () => (/* binding */ toAsyncIterable),\n/* harmony export */   toCallbag: () => (/* binding */ toCallbag),\n/* harmony export */   toObservable: () => (/* binding */ toObservable),\n/* harmony export */   toPromise: () => (/* binding */ toPromise),\n/* harmony export */   zip: () => (/* binding */ zip)\n/* harmony export */ });\nvar teardownPlaceholder = () => {};\n\nvar e = teardownPlaceholder;\n\nfunction start(e) {\n  return {\n    tag: 0,\n    0: e\n  };\n}\n\nfunction push(e) {\n  return {\n    tag: 1,\n    0: e\n  };\n}\n\nvar asyncIteratorSymbol = () => \"function\" == typeof Symbol && Symbol.asyncIterator || \"@@asyncIterator\";\n\nvar observableSymbol = () => \"function\" == typeof Symbol && Symbol.observable || \"@@observable\";\n\nvar identity = e => e;\n\nfunction buffer(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = e;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        n(1);\n        if (a.length) {\n          i(push(a));\n        }\n        i(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n        r((e => {\n          if (l) {} else if (0 === e) {\n            l = !0;\n            f(1);\n            if (a.length) {\n              i(push(a));\n            }\n            i(0);\n          } else if (0 === e.tag) {\n            n = e[0];\n          } else if (a.length) {\n            var r = push(a);\n            a = [];\n            i(r);\n          }\n        }));\n      } else {\n        a.push(e[0]);\n        if (!s) {\n          s = !0;\n          f(0);\n          n(0);\n        } else {\n          s = !1;\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        f(1);\n        n(1);\n      } else if (!l && !s) {\n        s = !0;\n        f(0);\n        n(0);\n      }\n    })));\n  };\n}\n\nfunction concatMap(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = e;\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    var o = !1;\n    function applyInnerSource(e) {\n      u = !0;\n      e((e => {\n        if (0 === e) {\n          if (u) {\n            u = !1;\n            if (a.length) {\n              applyInnerSource(r(a.shift()));\n            } else if (o) {\n              i(0);\n            } else if (!s) {\n              s = !0;\n              f(0);\n            }\n          }\n        } else if (0 === e.tag) {\n          l = !1;\n          (n = e[0])(0);\n        } else if (u) {\n          i(e);\n          if (l) {\n            l = !1;\n          } else {\n            n(0);\n          }\n        }\n      }));\n    }\n    t((e => {\n      if (o) {} else if (0 === e) {\n        o = !0;\n        if (!u && !a.length) {\n          i(0);\n        }\n      } else if (0 === e.tag) {\n        f = e[0];\n      } else {\n        s = !1;\n        if (u) {\n          a.push(e[0]);\n        } else {\n          applyInnerSource(r(e[0]));\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!o) {\n          o = !0;\n          f(1);\n        }\n        if (u) {\n          u = !1;\n          n(1);\n        }\n      } else {\n        if (!o && !s) {\n          s = !0;\n          f(0);\n        }\n        if (u && !l) {\n          l = !0;\n          n(0);\n        }\n      }\n    })));\n  };\n}\n\nfunction concatAll(e) {\n  return concatMap(identity)(e);\n}\n\nfunction concat(e) {\n  return concatAll(r(e));\n}\n\nfunction filter(r) {\n  return t => i => {\n    var a = e;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (!r(e[0])) {\n        a(0);\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction map(e) {\n  return r => t => r((r => {\n    if (0 === r || 0 === r.tag) {\n      t(r);\n    } else {\n      t(push(e(r[0])));\n    }\n  }));\n}\n\nfunction mergeMap(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = !1;\n    var s = !1;\n    t((t => {\n      if (s) {} else if (0 === t) {\n        s = !0;\n        if (!a.length) {\n          i(0);\n        }\n      } else if (0 === t.tag) {\n        f = t[0];\n      } else {\n        n = !1;\n        !function applyInnerSource(r) {\n          var t = e;\n          r((e => {\n            if (0 === e) {\n              if (a.length) {\n                var r = a.indexOf(t);\n                if (r > -1) {\n                  (a = a.slice()).splice(r, 1);\n                }\n                if (!a.length) {\n                  if (s) {\n                    i(0);\n                  } else if (!n) {\n                    n = !0;\n                    f(0);\n                  }\n                }\n              }\n            } else if (0 === e.tag) {\n              a.push(t = e[0]);\n              t(0);\n            } else if (a.length) {\n              i(e);\n              t(0);\n            }\n          }));\n        }(r(t[0]));\n        if (!n) {\n          n = !0;\n          f(0);\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!s) {\n          s = !0;\n          f(1);\n        }\n        for (var r = 0, t = a, i = a.length; r < i; r++) {\n          t[r](1);\n        }\n        a.length = 0;\n      } else {\n        if (!s && !n) {\n          n = !0;\n          f(0);\n        } else {\n          n = !1;\n        }\n        for (var l = 0, u = a, o = a.length; l < o; l++) {\n          u[l](0);\n        }\n      }\n    })));\n  };\n}\n\nfunction mergeAll(e) {\n  return mergeMap(identity)(e);\n}\n\nfunction merge(e) {\n  return mergeAll(r(e));\n}\n\nfunction onEnd(e) {\n  return r => t => {\n    var i = !1;\n    r((r => {\n      if (i) {} else if (0 === r) {\n        i = !0;\n        t(0);\n        e();\n      } else if (0 === r.tag) {\n        var a = r[0];\n        t(start((r => {\n          if (1 === r) {\n            i = !0;\n            a(1);\n            e();\n          } else {\n            a(r);\n          }\n        })));\n      } else {\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction onPush(e) {\n  return r => t => {\n    var i = !1;\n    r((r => {\n      if (i) {} else if (0 === r) {\n        i = !0;\n        t(0);\n      } else if (0 === r.tag) {\n        var a = r[0];\n        t(start((e => {\n          if (1 === e) {\n            i = !0;\n          }\n          a(e);\n        })));\n      } else {\n        e(r[0]);\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction onStart(e) {\n  return r => t => r((r => {\n    if (0 === r) {\n      t(0);\n    } else if (0 === r.tag) {\n      t(r);\n      e();\n    } else {\n      t(r);\n    }\n  }));\n}\n\nfunction sample(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        f(1);\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n      } else {\n        n = e[0];\n        if (!s) {\n          s = !0;\n          f(0);\n          a(0);\n        } else {\n          s = !1;\n        }\n      }\n    }));\n    r((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        a(1);\n        i(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n      } else if (void 0 !== n) {\n        var r = push(n);\n        n = void 0;\n        i(r);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        a(1);\n        f(1);\n      } else if (!l && !s) {\n        s = !0;\n        a(0);\n        f(0);\n      }\n    })));\n  };\n}\n\nfunction scan(e, r) {\n  return t => i => {\n    var a = r;\n    t((r => {\n      if (0 === r) {\n        i(0);\n      } else if (0 === r.tag) {\n        i(r);\n      } else {\n        i(push(a = e(a, r[0])));\n      }\n    }));\n  };\n}\n\nfunction share(r) {\n  var t = [];\n  var i = e;\n  var a = !1;\n  return e => {\n    t.push(e);\n    if (1 === t.length) {\n      r((e => {\n        if (0 === e) {\n          for (var r = 0, f = t, n = t.length; r < n; r++) {\n            f[r](0);\n          }\n          t.length = 0;\n        } else if (0 === e.tag) {\n          i = e[0];\n        } else {\n          a = !1;\n          for (var s = 0, l = t, u = t.length; s < u; s++) {\n            l[s](e);\n          }\n        }\n      }));\n    }\n    e(start((r => {\n      if (1 === r) {\n        var f = t.indexOf(e);\n        if (f > -1) {\n          (t = t.slice()).splice(f, 1);\n        }\n        if (!t.length) {\n          i(1);\n        }\n      } else if (!a) {\n        a = !0;\n        i(0);\n      }\n    })));\n  };\n}\n\nfunction skip(r) {\n  return t => i => {\n    var a = e;\n    var f = r;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (f-- > 0) {\n        a(0);\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction skipUntil(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !0;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        if (n) {\n          f(1);\n        }\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        r((e => {\n          if (0 === e) {\n            if (n) {\n              l = !0;\n              a(1);\n            }\n          } else if (0 === e.tag) {\n            (f = e[0])(0);\n          } else {\n            n = !1;\n            f(1);\n          }\n        }));\n      } else if (!n) {\n        s = !1;\n        i(e);\n      } else if (!s) {\n        s = !0;\n        a(0);\n        f(0);\n      } else {\n        s = !1;\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        a(1);\n        if (n) {\n          f(1);\n        }\n      } else if (!l && !s) {\n        s = !0;\n        if (n) {\n          f(0);\n        }\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction skipWhile(r) {\n  return t => i => {\n    var a = e;\n    var f = !0;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (f) {\n        if (r(e[0])) {\n          a(0);\n        } else {\n          f = !1;\n          i(e);\n        }\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction switchMap(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !1;\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    t((t => {\n      if (u) {} else if (0 === t) {\n        u = !0;\n        if (!l) {\n          i(0);\n        }\n      } else if (0 === t.tag) {\n        a = t[0];\n      } else {\n        if (l) {\n          f(1);\n          f = e;\n        }\n        if (!n) {\n          n = !0;\n          a(0);\n        } else {\n          n = !1;\n        }\n        !function applyInnerSource(e) {\n          l = !0;\n          e((e => {\n            if (!l) {} else if (0 === e) {\n              l = !1;\n              if (u) {\n                i(0);\n              } else if (!n) {\n                n = !0;\n                a(0);\n              }\n            } else if (0 === e.tag) {\n              s = !1;\n              (f = e[0])(0);\n            } else {\n              i(e);\n              if (!s) {\n                f(0);\n              } else {\n                s = !1;\n              }\n            }\n          }));\n        }(r(t[0]));\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!u) {\n          u = !0;\n          a(1);\n        }\n        if (l) {\n          l = !1;\n          f(1);\n        }\n      } else {\n        if (!u && !n) {\n          n = !0;\n          a(0);\n        }\n        if (l && !s) {\n          s = !0;\n          f(0);\n        }\n      }\n    })));\n  };\n}\n\nfunction switchAll(e) {\n  return switchMap(identity)(e);\n}\n\nfunction take(r) {\n  return t => i => {\n    var a = e;\n    var f = !1;\n    var n = 0;\n    t((e => {\n      if (f) {} else if (0 === e) {\n        f = !0;\n        i(0);\n      } else if (0 === e.tag) {\n        if (r <= 0) {\n          f = !0;\n          i(0);\n          e[0](1);\n        } else {\n          a = e[0];\n        }\n      } else if (n++ < r) {\n        i(e);\n        if (!f && n >= r) {\n          f = !0;\n          i(0);\n          a(1);\n        }\n      } else {\n        i(e);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !f) {\n        f = !0;\n        a(1);\n      } else if (0 === e && !f && n < r) {\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction takeLast(t) {\n  return i => a => {\n    var f = [];\n    var n = e;\n    i((e => {\n      if (0 === e) {\n        r(f)(a);\n      } else if (0 === e.tag) {\n        if (t <= 0) {\n          e[0](1);\n          r(f)(a);\n        } else {\n          (n = e[0])(0);\n        }\n      } else {\n        if (f.length >= t && t) {\n          f.shift();\n        }\n        f.push(e[0]);\n        n(0);\n      }\n    }));\n  };\n}\n\nfunction takeUntil(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !1;\n    t((e => {\n      if (n) {} else if (0 === e) {\n        n = !0;\n        f(1);\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        r((e => {\n          if (0 === e) {} else if (0 === e.tag) {\n            (f = e[0])(0);\n          } else {\n            n = !0;\n            f(1);\n            a(1);\n            i(0);\n          }\n        }));\n      } else {\n        i(e);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !n) {\n        n = !0;\n        a(1);\n        f(1);\n      } else if (!n) {\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction takeWhile(r, t) {\n  return i => a => {\n    var f = e;\n    var n = !1;\n    i((e => {\n      if (n) {} else if (0 === e) {\n        n = !0;\n        a(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n        a(e);\n      } else if (!r(e[0])) {\n        n = !0;\n        if (t) {\n          a(e);\n        }\n        a(0);\n        f(1);\n      } else {\n        a(e);\n      }\n    }));\n  };\n}\n\nfunction debounce(e) {\n  return r => t => {\n    var i;\n    var a = !1;\n    var f = !1;\n    r((r => {\n      if (f) {} else if (0 === r) {\n        f = !0;\n        if (i) {\n          a = !0;\n        } else {\n          t(0);\n        }\n      } else if (0 === r.tag) {\n        var n = r[0];\n        t(start((e => {\n          if (1 === e && !f) {\n            f = !0;\n            a = !1;\n            if (i) {\n              clearTimeout(i);\n            }\n            n(1);\n          } else if (!f) {\n            n(0);\n          }\n        })));\n      } else {\n        if (i) {\n          clearTimeout(i);\n        }\n        i = setTimeout((() => {\n          i = void 0;\n          t(r);\n          if (a) {\n            t(0);\n          }\n        }), e(r[0]));\n      }\n    }));\n  };\n}\n\nfunction delay(e) {\n  return r => t => {\n    var i = 0;\n    r((r => {\n      if (0 !== r && 0 === r.tag) {\n        t(r);\n      } else {\n        i++;\n        setTimeout((() => {\n          if (i) {\n            i--;\n            t(r);\n          }\n        }), e);\n      }\n    }));\n  };\n}\n\nfunction throttle(e) {\n  return r => t => {\n    var i = !1;\n    var a;\n    r((r => {\n      if (0 === r) {\n        if (a) {\n          clearTimeout(a);\n        }\n        t(0);\n      } else if (0 === r.tag) {\n        var f = r[0];\n        t(start((e => {\n          if (1 === e) {\n            if (a) {\n              clearTimeout(a);\n            }\n            f(1);\n          } else {\n            f(0);\n          }\n        })));\n      } else if (!i) {\n        i = !0;\n        if (a) {\n          clearTimeout(a);\n        }\n        a = setTimeout((() => {\n          a = void 0;\n          i = !1;\n        }), e(r[0]));\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction lazy(e) {\n  return r => e()(r);\n}\n\nfunction fromAsyncIterable(e) {\n  return r => {\n    var t = e[asyncIteratorSymbol()] && e[asyncIteratorSymbol()]() || e;\n    var i = !1;\n    var a = !1;\n    var f = !1;\n    var n;\n    r(start((async e => {\n      if (1 === e) {\n        i = !0;\n        if (t.return) {\n          t.return();\n        }\n      } else if (a) {\n        f = !0;\n      } else {\n        for (f = a = !0; f && !i; ) {\n          if ((n = await t.next()).done) {\n            i = !0;\n            if (t.return) {\n              await t.return();\n            }\n            r(0);\n          } else {\n            try {\n              f = !1;\n              r(push(n.value));\n            } catch (e) {\n              if (t.throw) {\n                if (i = !!(await t.throw(e)).done) {\n                  r(0);\n                }\n              } else {\n                throw e;\n              }\n            }\n          }\n        }\n        a = !1;\n      }\n    })));\n  };\n}\n\nfunction fromIterable(e) {\n  if (e[Symbol.asyncIterator]) {\n    return fromAsyncIterable(e);\n  }\n  return r => {\n    var t = e[Symbol.iterator]();\n    var i = !1;\n    var a = !1;\n    var f = !1;\n    var n;\n    r(start((e => {\n      if (1 === e) {\n        i = !0;\n        if (t.return) {\n          t.return();\n        }\n      } else if (a) {\n        f = !0;\n      } else {\n        for (f = a = !0; f && !i; ) {\n          if ((n = t.next()).done) {\n            i = !0;\n            if (t.return) {\n              t.return();\n            }\n            r(0);\n          } else {\n            try {\n              f = !1;\n              r(push(n.value));\n            } catch (e) {\n              if (t.throw) {\n                if (i = !!t.throw(e).done) {\n                  r(0);\n                }\n              } else {\n                throw e;\n              }\n            }\n          }\n        }\n        a = !1;\n      }\n    })));\n  };\n}\n\nvar r = fromIterable;\n\nfunction fromValue(e) {\n  return r => {\n    var t = !1;\n    r(start((i => {\n      if (1 === i) {\n        t = !0;\n      } else if (!t) {\n        t = !0;\n        r(push(e));\n        r(0);\n      }\n    })));\n  };\n}\n\nfunction make(e) {\n  return r => {\n    var t = !1;\n    var i = e({\n      next(e) {\n        if (!t) {\n          r(push(e));\n        }\n      },\n      complete() {\n        if (!t) {\n          t = !0;\n          r(0);\n        }\n      }\n    });\n    r(start((e => {\n      if (1 === e && !t) {\n        t = !0;\n        i();\n      }\n    })));\n  };\n}\n\nfunction makeSubject() {\n  var e;\n  var r;\n  return {\n    source: share(make((t => {\n      e = t.next;\n      r = t.complete;\n      return teardownPlaceholder;\n    }))),\n    next(r) {\n      if (e) {\n        e(r);\n      }\n    },\n    complete() {\n      if (r) {\n        r();\n      }\n    }\n  };\n}\n\nvar empty = e => {\n  var r = !1;\n  e(start((t => {\n    if (1 === t) {\n      r = !0;\n    } else if (!r) {\n      r = !0;\n      e(0);\n    }\n  })));\n};\n\nvar never = r => {\n  r(start(e));\n};\n\nfunction interval(e) {\n  return make((r => {\n    var t = 0;\n    var i = setInterval((() => r.next(t++)), e);\n    return () => clearInterval(i);\n  }));\n}\n\nfunction fromDomEvent(e, r) {\n  return make((t => {\n    e.addEventListener(r, t.next);\n    return () => e.removeEventListener(r, t.next);\n  }));\n}\n\nfunction fromPromise(e) {\n  return make((r => {\n    e.then((e => {\n      Promise.resolve(e).then((() => {\n        r.next(e);\n        r.complete();\n      }));\n    }));\n    return teardownPlaceholder;\n  }));\n}\n\nfunction subscribe(r) {\n  return t => {\n    var i = e;\n    var a = !1;\n    t((e => {\n      if (0 === e) {\n        a = !0;\n      } else if (0 === e.tag) {\n        (i = e[0])(0);\n      } else if (!a) {\n        r(e[0]);\n        i(0);\n      }\n    }));\n    return {\n      unsubscribe() {\n        if (!a) {\n          a = !0;\n          i(1);\n        }\n      }\n    };\n  };\n}\n\nfunction forEach(e) {\n  return r => {\n    subscribe(e)(r);\n  };\n}\n\nfunction publish(e) {\n  subscribe((e => {}))(e);\n}\n\nvar t = {\n  done: !0\n};\n\nvar toAsyncIterable = r => {\n  var i = [];\n  var a = !1;\n  var f = !1;\n  var n = !1;\n  var s = e;\n  var l;\n  return {\n    async next() {\n      if (!f) {\n        f = !0;\n        r((e => {\n          if (a) {} else if (0 === e) {\n            if (l) {\n              l = l(t);\n            }\n            a = !0;\n          } else if (0 === e.tag) {\n            n = !0;\n            (s = e[0])(0);\n          } else {\n            n = !1;\n            if (l) {\n              l = l({\n                value: e[0],\n                done: !1\n              });\n            } else {\n              i.push(e[0]);\n            }\n          }\n        }));\n      }\n      if (a && !i.length) {\n        return t;\n      } else if (!a && !n && i.length <= 1) {\n        n = !0;\n        s(0);\n      }\n      return i.length ? {\n        value: i.shift(),\n        done: !1\n      } : new Promise((e => l = e));\n    },\n    async return() {\n      if (!a) {\n        l = s(1);\n      }\n      a = !0;\n      return t;\n    },\n    [asyncIteratorSymbol()]() {\n      return this;\n    }\n  };\n};\n\nfunction toArray(r) {\n  var t = [];\n  var i = e;\n  var a = !1;\n  r((e => {\n    if (0 === e) {\n      a = !0;\n    } else if (0 === e.tag) {\n      (i = e[0])(0);\n    } else {\n      t.push(e[0]);\n      i(0);\n    }\n  }));\n  if (!a) {\n    i(1);\n  }\n  return t;\n}\n\nfunction toPromise(r) {\n  return new Promise((t => {\n    var i = e;\n    var a;\n    r((e => {\n      if (0 === e) {\n        Promise.resolve(a).then(t);\n      } else if (0 === e.tag) {\n        (i = e[0])(0);\n      } else {\n        a = e[0];\n        i(0);\n      }\n    }));\n  }));\n}\n\nfunction zip(r) {\n  var t = Object.keys(r).length;\n  return i => {\n    var a = new Set;\n    var f = Array.isArray(r) ? new Array(t).fill(e) : {};\n    var n = Array.isArray(r) ? new Array(t) : {};\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    var o = 0;\n    var loop = function(v) {\n      r[v]((c => {\n        if (0 === c) {\n          if (o >= t - 1) {\n            u = !0;\n            i(0);\n          } else {\n            o++;\n          }\n        } else if (0 === c.tag) {\n          f[v] = c[0];\n        } else if (!u) {\n          n[v] = c[0];\n          a.add(v);\n          if (!s && a.size < t) {\n            if (!l) {\n              for (var h in r) {\n                if (!a.has(h)) {\n                  (f[h] || e)(0);\n                }\n              }\n            } else {\n              l = !1;\n            }\n          } else {\n            s = !0;\n            l = !1;\n            i(push(Array.isArray(n) ? n.slice() : {\n              ...n\n            }));\n          }\n        }\n      }));\n    };\n    for (var v in r) {\n      loop(v);\n    }\n    i(start((e => {\n      if (u) {} else if (1 === e) {\n        u = !0;\n        for (var r in f) {\n          f[r](1);\n        }\n      } else if (!l) {\n        l = !0;\n        for (var t in f) {\n          f[t](0);\n        }\n      }\n    })));\n  };\n}\n\nfunction combine(...e) {\n  return zip(e);\n}\n\nfunction fromObservable(e) {\n  return r => {\n    var t = (e[observableSymbol()] ? e[observableSymbol()]() : e).subscribe({\n      next(e) {\n        r(push(e));\n      },\n      complete() {\n        r(0);\n      },\n      error(e) {\n        throw e;\n      }\n    });\n    r(start((e => {\n      if (1 === e) {\n        t.unsubscribe();\n      }\n    })));\n  };\n}\n\nfunction toObservable(r) {\n  return {\n    subscribe(t, i, a) {\n      var f = \"object\" == typeof t ? t : {\n        next: t,\n        error: i,\n        complete: a\n      };\n      var n = e;\n      var s = !1;\n      r((e => {\n        if (s) {} else if (0 === e) {\n          s = !0;\n          if (f.complete) {\n            f.complete();\n          }\n        } else if (0 === e.tag) {\n          (n = e[0])(0);\n        } else {\n          f.next(e[0]);\n          n(0);\n        }\n      }));\n      var l = {\n        closed: !1,\n        unsubscribe() {\n          l.closed = !0;\n          s = !0;\n          n(1);\n        }\n      };\n      return l;\n    },\n    [observableSymbol()]() {\n      return this;\n    }\n  };\n}\n\nfunction fromCallbag(e) {\n  return r => {\n    e(0, ((e, t) => {\n      if (0 === e) {\n        r(start((e => {\n          t(e + 1);\n        })));\n      } else if (1 === e) {\n        r(push(t));\n      } else {\n        r(0);\n      }\n    }));\n  };\n}\n\nfunction toCallbag(e) {\n  return (r, t) => {\n    if (0 === r) {\n      e((e => {\n        if (0 === e) {\n          t(2);\n        } else if (0 === e.tag) {\n          t(0, (r => {\n            if (r < 3) {\n              e[0](r - 1);\n            }\n          }));\n        } else {\n          t(1, e[0]);\n        }\n      }));\n    }\n  };\n}\n\nvar pipe = (...e) => {\n  var r = e[0];\n  for (var t = 1, i = e.length; t < i; t++) {\n    r = e[t](r);\n  }\n  return r;\n};\n\n\n//# sourceMappingURL=wonka.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd29ua2EvZGlzdC93b25rYS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLE9BQU87QUFDcEQ7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsNkNBQTZDLE9BQU87QUFDcEQ7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsT0FBTztBQUN0RDtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQSwrQ0FBK0MsT0FBTztBQUN0RDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsU0FBUztBQUNULFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUix5QkFBeUIsU0FBUztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1IseUJBQXlCLFNBQVM7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxLQUFLO0FBQ0w7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0I7QUFDcEI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2YsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCxVQUFVO0FBQ1Y7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGdDQUFnQyxPQUFPO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBOztBQUVnakI7QUFDaGpCIiwic291cmNlcyI6WyIvdGVhbXNwYWNlL3N0dWRpb3MvdGhpc19zdHVkaW8vb3Blbi1tdWx0aS1hZ2VudC1jYW52YXMvYml0ZWJhc2VfYWdlbnQvbm9kZV9tb2R1bGVzL3dvbmthL2Rpc3Qvd29ua2EubWpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciB0ZWFyZG93blBsYWNlaG9sZGVyID0gKCkgPT4ge307XG5cbnZhciBlID0gdGVhcmRvd25QbGFjZWhvbGRlcjtcblxuZnVuY3Rpb24gc3RhcnQoZSkge1xuICByZXR1cm4ge1xuICAgIHRhZzogMCxcbiAgICAwOiBlXG4gIH07XG59XG5cbmZ1bmN0aW9uIHB1c2goZSkge1xuICByZXR1cm4ge1xuICAgIHRhZzogMSxcbiAgICAwOiBlXG4gIH07XG59XG5cbnZhciBhc3luY0l0ZXJhdG9yU3ltYm9sID0gKCkgPT4gXCJmdW5jdGlvblwiID09IHR5cGVvZiBTeW1ib2wgJiYgU3ltYm9sLmFzeW5jSXRlcmF0b3IgfHwgXCJAQGFzeW5jSXRlcmF0b3JcIjtcblxudmFyIG9ic2VydmFibGVTeW1ib2wgPSAoKSA9PiBcImZ1bmN0aW9uXCIgPT0gdHlwZW9mIFN5bWJvbCAmJiBTeW1ib2wub2JzZXJ2YWJsZSB8fCBcIkBAb2JzZXJ2YWJsZVwiO1xuXG52YXIgaWRlbnRpdHkgPSBlID0+IGU7XG5cbmZ1bmN0aW9uIGJ1ZmZlcihyKSB7XG4gIHJldHVybiB0ID0+IGkgPT4ge1xuICAgIHZhciBhID0gW107XG4gICAgdmFyIGYgPSBlO1xuICAgIHZhciBuID0gZTtcbiAgICB2YXIgcyA9ICExO1xuICAgIHZhciBsID0gITE7XG4gICAgdCgoZSA9PiB7XG4gICAgICBpZiAobCkge30gZWxzZSBpZiAoMCA9PT0gZSkge1xuICAgICAgICBsID0gITA7XG4gICAgICAgIG4oMSk7XG4gICAgICAgIGlmIChhLmxlbmd0aCkge1xuICAgICAgICAgIGkocHVzaChhKSk7XG4gICAgICAgIH1cbiAgICAgICAgaSgwKTtcbiAgICAgIH0gZWxzZSBpZiAoMCA9PT0gZS50YWcpIHtcbiAgICAgICAgZiA9IGVbMF07XG4gICAgICAgIHIoKGUgPT4ge1xuICAgICAgICAgIGlmIChsKSB7fSBlbHNlIGlmICgwID09PSBlKSB7XG4gICAgICAgICAgICBsID0gITA7XG4gICAgICAgICAgICBmKDEpO1xuICAgICAgICAgICAgaWYgKGEubGVuZ3RoKSB7XG4gICAgICAgICAgICAgIGkocHVzaChhKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpKDApO1xuICAgICAgICAgIH0gZWxzZSBpZiAoMCA9PT0gZS50YWcpIHtcbiAgICAgICAgICAgIG4gPSBlWzBdO1xuICAgICAgICAgIH0gZWxzZSBpZiAoYS5sZW5ndGgpIHtcbiAgICAgICAgICAgIHZhciByID0gcHVzaChhKTtcbiAgICAgICAgICAgIGEgPSBbXTtcbiAgICAgICAgICAgIGkocik7XG4gICAgICAgICAgfVxuICAgICAgICB9KSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhLnB1c2goZVswXSk7XG4gICAgICAgIGlmICghcykge1xuICAgICAgICAgIHMgPSAhMDtcbiAgICAgICAgICBmKDApO1xuICAgICAgICAgIG4oMCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcyA9ICExO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSkpO1xuICAgIGkoc3RhcnQoKGUgPT4ge1xuICAgICAgaWYgKDEgPT09IGUgJiYgIWwpIHtcbiAgICAgICAgbCA9ICEwO1xuICAgICAgICBmKDEpO1xuICAgICAgICBuKDEpO1xuICAgICAgfSBlbHNlIGlmICghbCAmJiAhcykge1xuICAgICAgICBzID0gITA7XG4gICAgICAgIGYoMCk7XG4gICAgICAgIG4oMCk7XG4gICAgICB9XG4gICAgfSkpKTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gY29uY2F0TWFwKHIpIHtcbiAgcmV0dXJuIHQgPT4gaSA9PiB7XG4gICAgdmFyIGEgPSBbXTtcbiAgICB2YXIgZiA9IGU7XG4gICAgdmFyIG4gPSBlO1xuICAgIHZhciBzID0gITE7XG4gICAgdmFyIGwgPSAhMTtcbiAgICB2YXIgdSA9ICExO1xuICAgIHZhciBvID0gITE7XG4gICAgZnVuY3Rpb24gYXBwbHlJbm5lclNvdXJjZShlKSB7XG4gICAgICB1ID0gITA7XG4gICAgICBlKChlID0+IHtcbiAgICAgICAgaWYgKDAgPT09IGUpIHtcbiAgICAgICAgICBpZiAodSkge1xuICAgICAgICAgICAgdSA9ICExO1xuICAgICAgICAgICAgaWYgKGEubGVuZ3RoKSB7XG4gICAgICAgICAgICAgIGFwcGx5SW5uZXJTb3VyY2UocihhLnNoaWZ0KCkpKTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAobykge1xuICAgICAgICAgICAgICBpKDApO1xuICAgICAgICAgICAgfSBlbHNlIGlmICghcykge1xuICAgICAgICAgICAgICBzID0gITA7XG4gICAgICAgICAgICAgIGYoMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgICAgbCA9ICExO1xuICAgICAgICAgIChuID0gZVswXSkoMCk7XG4gICAgICAgIH0gZWxzZSBpZiAodSkge1xuICAgICAgICAgIGkoZSk7XG4gICAgICAgICAgaWYgKGwpIHtcbiAgICAgICAgICAgIGwgPSAhMTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgbigwKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0pKTtcbiAgICB9XG4gICAgdCgoZSA9PiB7XG4gICAgICBpZiAobykge30gZWxzZSBpZiAoMCA9PT0gZSkge1xuICAgICAgICBvID0gITA7XG4gICAgICAgIGlmICghdSAmJiAhYS5sZW5ndGgpIHtcbiAgICAgICAgICBpKDApO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgIGYgPSBlWzBdO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcyA9ICExO1xuICAgICAgICBpZiAodSkge1xuICAgICAgICAgIGEucHVzaChlWzBdKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBhcHBseUlubmVyU291cmNlKHIoZVswXSkpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSkpO1xuICAgIGkoc3RhcnQoKGUgPT4ge1xuICAgICAgaWYgKDEgPT09IGUpIHtcbiAgICAgICAgaWYgKCFvKSB7XG4gICAgICAgICAgbyA9ICEwO1xuICAgICAgICAgIGYoMSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHUpIHtcbiAgICAgICAgICB1ID0gITE7XG4gICAgICAgICAgbigxKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKCFvICYmICFzKSB7XG4gICAgICAgICAgcyA9ICEwO1xuICAgICAgICAgIGYoMCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHUgJiYgIWwpIHtcbiAgICAgICAgICBsID0gITA7XG4gICAgICAgICAgbigwKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pKSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIGNvbmNhdEFsbChlKSB7XG4gIHJldHVybiBjb25jYXRNYXAoaWRlbnRpdHkpKGUpO1xufVxuXG5mdW5jdGlvbiBjb25jYXQoZSkge1xuICByZXR1cm4gY29uY2F0QWxsKHIoZSkpO1xufVxuXG5mdW5jdGlvbiBmaWx0ZXIocikge1xuICByZXR1cm4gdCA9PiBpID0+IHtcbiAgICB2YXIgYSA9IGU7XG4gICAgdCgoZSA9PiB7XG4gICAgICBpZiAoMCA9PT0gZSkge1xuICAgICAgICBpKDApO1xuICAgICAgfSBlbHNlIGlmICgwID09PSBlLnRhZykge1xuICAgICAgICBhID0gZVswXTtcbiAgICAgICAgaShlKTtcbiAgICAgIH0gZWxzZSBpZiAoIXIoZVswXSkpIHtcbiAgICAgICAgYSgwKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGkoZSk7XG4gICAgICB9XG4gICAgfSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBtYXAoZSkge1xuICByZXR1cm4gciA9PiB0ID0+IHIoKHIgPT4ge1xuICAgIGlmICgwID09PSByIHx8IDAgPT09IHIudGFnKSB7XG4gICAgICB0KHIpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0KHB1c2goZShyWzBdKSkpO1xuICAgIH1cbiAgfSkpO1xufVxuXG5mdW5jdGlvbiBtZXJnZU1hcChyKSB7XG4gIHJldHVybiB0ID0+IGkgPT4ge1xuICAgIHZhciBhID0gW107XG4gICAgdmFyIGYgPSBlO1xuICAgIHZhciBuID0gITE7XG4gICAgdmFyIHMgPSAhMTtcbiAgICB0KCh0ID0+IHtcbiAgICAgIGlmIChzKSB7fSBlbHNlIGlmICgwID09PSB0KSB7XG4gICAgICAgIHMgPSAhMDtcbiAgICAgICAgaWYgKCFhLmxlbmd0aCkge1xuICAgICAgICAgIGkoMCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoMCA9PT0gdC50YWcpIHtcbiAgICAgICAgZiA9IHRbMF07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBuID0gITE7XG4gICAgICAgICFmdW5jdGlvbiBhcHBseUlubmVyU291cmNlKHIpIHtcbiAgICAgICAgICB2YXIgdCA9IGU7XG4gICAgICAgICAgcigoZSA9PiB7XG4gICAgICAgICAgICBpZiAoMCA9PT0gZSkge1xuICAgICAgICAgICAgICBpZiAoYS5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICB2YXIgciA9IGEuaW5kZXhPZih0KTtcbiAgICAgICAgICAgICAgICBpZiAociA+IC0xKSB7XG4gICAgICAgICAgICAgICAgICAoYSA9IGEuc2xpY2UoKSkuc3BsaWNlKHIsIDEpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIWEubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICBpZiAocykge1xuICAgICAgICAgICAgICAgICAgICBpKDApO1xuICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmICghbikge1xuICAgICAgICAgICAgICAgICAgICBuID0gITA7XG4gICAgICAgICAgICAgICAgICAgIGYoMCk7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgICAgICAgIGEucHVzaCh0ID0gZVswXSk7XG4gICAgICAgICAgICAgIHQoMCk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKGEubGVuZ3RoKSB7XG4gICAgICAgICAgICAgIGkoZSk7XG4gICAgICAgICAgICAgIHQoMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSkpO1xuICAgICAgICB9KHIodFswXSkpO1xuICAgICAgICBpZiAoIW4pIHtcbiAgICAgICAgICBuID0gITA7XG4gICAgICAgICAgZigwKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pKTtcbiAgICBpKHN0YXJ0KChlID0+IHtcbiAgICAgIGlmICgxID09PSBlKSB7XG4gICAgICAgIGlmICghcykge1xuICAgICAgICAgIHMgPSAhMDtcbiAgICAgICAgICBmKDEpO1xuICAgICAgICB9XG4gICAgICAgIGZvciAodmFyIHIgPSAwLCB0ID0gYSwgaSA9IGEubGVuZ3RoOyByIDwgaTsgcisrKSB7XG4gICAgICAgICAgdFtyXSgxKTtcbiAgICAgICAgfVxuICAgICAgICBhLmxlbmd0aCA9IDA7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpZiAoIXMgJiYgIW4pIHtcbiAgICAgICAgICBuID0gITA7XG4gICAgICAgICAgZigwKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBuID0gITE7XG4gICAgICAgIH1cbiAgICAgICAgZm9yICh2YXIgbCA9IDAsIHUgPSBhLCBvID0gYS5sZW5ndGg7IGwgPCBvOyBsKyspIHtcbiAgICAgICAgICB1W2xdKDApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSkpKTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gbWVyZ2VBbGwoZSkge1xuICByZXR1cm4gbWVyZ2VNYXAoaWRlbnRpdHkpKGUpO1xufVxuXG5mdW5jdGlvbiBtZXJnZShlKSB7XG4gIHJldHVybiBtZXJnZUFsbChyKGUpKTtcbn1cblxuZnVuY3Rpb24gb25FbmQoZSkge1xuICByZXR1cm4gciA9PiB0ID0+IHtcbiAgICB2YXIgaSA9ICExO1xuICAgIHIoKHIgPT4ge1xuICAgICAgaWYgKGkpIHt9IGVsc2UgaWYgKDAgPT09IHIpIHtcbiAgICAgICAgaSA9ICEwO1xuICAgICAgICB0KDApO1xuICAgICAgICBlKCk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IHIudGFnKSB7XG4gICAgICAgIHZhciBhID0gclswXTtcbiAgICAgICAgdChzdGFydCgociA9PiB7XG4gICAgICAgICAgaWYgKDEgPT09IHIpIHtcbiAgICAgICAgICAgIGkgPSAhMDtcbiAgICAgICAgICAgIGEoMSk7XG4gICAgICAgICAgICBlKCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGEocik7XG4gICAgICAgICAgfVxuICAgICAgICB9KSkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdChyKTtcbiAgICAgIH1cbiAgICB9KSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIG9uUHVzaChlKSB7XG4gIHJldHVybiByID0+IHQgPT4ge1xuICAgIHZhciBpID0gITE7XG4gICAgcigociA9PiB7XG4gICAgICBpZiAoaSkge30gZWxzZSBpZiAoMCA9PT0gcikge1xuICAgICAgICBpID0gITA7XG4gICAgICAgIHQoMCk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IHIudGFnKSB7XG4gICAgICAgIHZhciBhID0gclswXTtcbiAgICAgICAgdChzdGFydCgoZSA9PiB7XG4gICAgICAgICAgaWYgKDEgPT09IGUpIHtcbiAgICAgICAgICAgIGkgPSAhMDtcbiAgICAgICAgICB9XG4gICAgICAgICAgYShlKTtcbiAgICAgICAgfSkpKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGUoclswXSk7XG4gICAgICAgIHQocik7XG4gICAgICB9XG4gICAgfSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBvblN0YXJ0KGUpIHtcbiAgcmV0dXJuIHIgPT4gdCA9PiByKChyID0+IHtcbiAgICBpZiAoMCA9PT0gcikge1xuICAgICAgdCgwKTtcbiAgICB9IGVsc2UgaWYgKDAgPT09IHIudGFnKSB7XG4gICAgICB0KHIpO1xuICAgICAgZSgpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0KHIpO1xuICAgIH1cbiAgfSkpO1xufVxuXG5mdW5jdGlvbiBzYW1wbGUocikge1xuICByZXR1cm4gdCA9PiBpID0+IHtcbiAgICB2YXIgYSA9IGU7XG4gICAgdmFyIGYgPSBlO1xuICAgIHZhciBuO1xuICAgIHZhciBzID0gITE7XG4gICAgdmFyIGwgPSAhMTtcbiAgICB0KChlID0+IHtcbiAgICAgIGlmIChsKSB7fSBlbHNlIGlmICgwID09PSBlKSB7XG4gICAgICAgIGwgPSAhMDtcbiAgICAgICAgZigxKTtcbiAgICAgICAgaSgwKTtcbiAgICAgIH0gZWxzZSBpZiAoMCA9PT0gZS50YWcpIHtcbiAgICAgICAgYSA9IGVbMF07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBuID0gZVswXTtcbiAgICAgICAgaWYgKCFzKSB7XG4gICAgICAgICAgcyA9ICEwO1xuICAgICAgICAgIGYoMCk7XG4gICAgICAgICAgYSgwKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBzID0gITE7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KSk7XG4gICAgcigoZSA9PiB7XG4gICAgICBpZiAobCkge30gZWxzZSBpZiAoMCA9PT0gZSkge1xuICAgICAgICBsID0gITA7XG4gICAgICAgIGEoMSk7XG4gICAgICAgIGkoMCk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgIGYgPSBlWzBdO1xuICAgICAgfSBlbHNlIGlmICh2b2lkIDAgIT09IG4pIHtcbiAgICAgICAgdmFyIHIgPSBwdXNoKG4pO1xuICAgICAgICBuID0gdm9pZCAwO1xuICAgICAgICBpKHIpO1xuICAgICAgfVxuICAgIH0pKTtcbiAgICBpKHN0YXJ0KChlID0+IHtcbiAgICAgIGlmICgxID09PSBlICYmICFsKSB7XG4gICAgICAgIGwgPSAhMDtcbiAgICAgICAgYSgxKTtcbiAgICAgICAgZigxKTtcbiAgICAgIH0gZWxzZSBpZiAoIWwgJiYgIXMpIHtcbiAgICAgICAgcyA9ICEwO1xuICAgICAgICBhKDApO1xuICAgICAgICBmKDApO1xuICAgICAgfVxuICAgIH0pKSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIHNjYW4oZSwgcikge1xuICByZXR1cm4gdCA9PiBpID0+IHtcbiAgICB2YXIgYSA9IHI7XG4gICAgdCgociA9PiB7XG4gICAgICBpZiAoMCA9PT0gcikge1xuICAgICAgICBpKDApO1xuICAgICAgfSBlbHNlIGlmICgwID09PSByLnRhZykge1xuICAgICAgICBpKHIpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaShwdXNoKGEgPSBlKGEsIHJbMF0pKSk7XG4gICAgICB9XG4gICAgfSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBzaGFyZShyKSB7XG4gIHZhciB0ID0gW107XG4gIHZhciBpID0gZTtcbiAgdmFyIGEgPSAhMTtcbiAgcmV0dXJuIGUgPT4ge1xuICAgIHQucHVzaChlKTtcbiAgICBpZiAoMSA9PT0gdC5sZW5ndGgpIHtcbiAgICAgIHIoKGUgPT4ge1xuICAgICAgICBpZiAoMCA9PT0gZSkge1xuICAgICAgICAgIGZvciAodmFyIHIgPSAwLCBmID0gdCwgbiA9IHQubGVuZ3RoOyByIDwgbjsgcisrKSB7XG4gICAgICAgICAgICBmW3JdKDApO1xuICAgICAgICAgIH1cbiAgICAgICAgICB0Lmxlbmd0aCA9IDA7XG4gICAgICAgIH0gZWxzZSBpZiAoMCA9PT0gZS50YWcpIHtcbiAgICAgICAgICBpID0gZVswXTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBhID0gITE7XG4gICAgICAgICAgZm9yICh2YXIgcyA9IDAsIGwgPSB0LCB1ID0gdC5sZW5ndGg7IHMgPCB1OyBzKyspIHtcbiAgICAgICAgICAgIGxbc10oZSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KSk7XG4gICAgfVxuICAgIGUoc3RhcnQoKHIgPT4ge1xuICAgICAgaWYgKDEgPT09IHIpIHtcbiAgICAgICAgdmFyIGYgPSB0LmluZGV4T2YoZSk7XG4gICAgICAgIGlmIChmID4gLTEpIHtcbiAgICAgICAgICAodCA9IHQuc2xpY2UoKSkuc3BsaWNlKGYsIDEpO1xuICAgICAgICB9XG4gICAgICAgIGlmICghdC5sZW5ndGgpIHtcbiAgICAgICAgICBpKDEpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKCFhKSB7XG4gICAgICAgIGEgPSAhMDtcbiAgICAgICAgaSgwKTtcbiAgICAgIH1cbiAgICB9KSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBza2lwKHIpIHtcbiAgcmV0dXJuIHQgPT4gaSA9PiB7XG4gICAgdmFyIGEgPSBlO1xuICAgIHZhciBmID0gcjtcbiAgICB0KChlID0+IHtcbiAgICAgIGlmICgwID09PSBlKSB7XG4gICAgICAgIGkoMCk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgIGEgPSBlWzBdO1xuICAgICAgICBpKGUpO1xuICAgICAgfSBlbHNlIGlmIChmLS0gPiAwKSB7XG4gICAgICAgIGEoMCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpKGUpO1xuICAgICAgfVxuICAgIH0pKTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gc2tpcFVudGlsKHIpIHtcbiAgcmV0dXJuIHQgPT4gaSA9PiB7XG4gICAgdmFyIGEgPSBlO1xuICAgIHZhciBmID0gZTtcbiAgICB2YXIgbiA9ICEwO1xuICAgIHZhciBzID0gITE7XG4gICAgdmFyIGwgPSAhMTtcbiAgICB0KChlID0+IHtcbiAgICAgIGlmIChsKSB7fSBlbHNlIGlmICgwID09PSBlKSB7XG4gICAgICAgIGwgPSAhMDtcbiAgICAgICAgaWYgKG4pIHtcbiAgICAgICAgICBmKDEpO1xuICAgICAgICB9XG4gICAgICAgIGkoMCk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgIGEgPSBlWzBdO1xuICAgICAgICByKChlID0+IHtcbiAgICAgICAgICBpZiAoMCA9PT0gZSkge1xuICAgICAgICAgICAgaWYgKG4pIHtcbiAgICAgICAgICAgICAgbCA9ICEwO1xuICAgICAgICAgICAgICBhKDEpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gZWxzZSBpZiAoMCA9PT0gZS50YWcpIHtcbiAgICAgICAgICAgIChmID0gZVswXSkoMCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIG4gPSAhMTtcbiAgICAgICAgICAgIGYoMSk7XG4gICAgICAgICAgfVxuICAgICAgICB9KSk7XG4gICAgICB9IGVsc2UgaWYgKCFuKSB7XG4gICAgICAgIHMgPSAhMTtcbiAgICAgICAgaShlKTtcbiAgICAgIH0gZWxzZSBpZiAoIXMpIHtcbiAgICAgICAgcyA9ICEwO1xuICAgICAgICBhKDApO1xuICAgICAgICBmKDApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcyA9ICExO1xuICAgICAgfVxuICAgIH0pKTtcbiAgICBpKHN0YXJ0KChlID0+IHtcbiAgICAgIGlmICgxID09PSBlICYmICFsKSB7XG4gICAgICAgIGwgPSAhMDtcbiAgICAgICAgYSgxKTtcbiAgICAgICAgaWYgKG4pIHtcbiAgICAgICAgICBmKDEpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKCFsICYmICFzKSB7XG4gICAgICAgIHMgPSAhMDtcbiAgICAgICAgaWYgKG4pIHtcbiAgICAgICAgICBmKDApO1xuICAgICAgICB9XG4gICAgICAgIGEoMCk7XG4gICAgICB9XG4gICAgfSkpKTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gc2tpcFdoaWxlKHIpIHtcbiAgcmV0dXJuIHQgPT4gaSA9PiB7XG4gICAgdmFyIGEgPSBlO1xuICAgIHZhciBmID0gITA7XG4gICAgdCgoZSA9PiB7XG4gICAgICBpZiAoMCA9PT0gZSkge1xuICAgICAgICBpKDApO1xuICAgICAgfSBlbHNlIGlmICgwID09PSBlLnRhZykge1xuICAgICAgICBhID0gZVswXTtcbiAgICAgICAgaShlKTtcbiAgICAgIH0gZWxzZSBpZiAoZikge1xuICAgICAgICBpZiAocihlWzBdKSkge1xuICAgICAgICAgIGEoMCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgZiA9ICExO1xuICAgICAgICAgIGkoZSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGkoZSk7XG4gICAgICB9XG4gICAgfSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBzd2l0Y2hNYXAocikge1xuICByZXR1cm4gdCA9PiBpID0+IHtcbiAgICB2YXIgYSA9IGU7XG4gICAgdmFyIGYgPSBlO1xuICAgIHZhciBuID0gITE7XG4gICAgdmFyIHMgPSAhMTtcbiAgICB2YXIgbCA9ICExO1xuICAgIHZhciB1ID0gITE7XG4gICAgdCgodCA9PiB7XG4gICAgICBpZiAodSkge30gZWxzZSBpZiAoMCA9PT0gdCkge1xuICAgICAgICB1ID0gITA7XG4gICAgICAgIGlmICghbCkge1xuICAgICAgICAgIGkoMCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoMCA9PT0gdC50YWcpIHtcbiAgICAgICAgYSA9IHRbMF07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpZiAobCkge1xuICAgICAgICAgIGYoMSk7XG4gICAgICAgICAgZiA9IGU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFuKSB7XG4gICAgICAgICAgbiA9ICEwO1xuICAgICAgICAgIGEoMCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgbiA9ICExO1xuICAgICAgICB9XG4gICAgICAgICFmdW5jdGlvbiBhcHBseUlubmVyU291cmNlKGUpIHtcbiAgICAgICAgICBsID0gITA7XG4gICAgICAgICAgZSgoZSA9PiB7XG4gICAgICAgICAgICBpZiAoIWwpIHt9IGVsc2UgaWYgKDAgPT09IGUpIHtcbiAgICAgICAgICAgICAgbCA9ICExO1xuICAgICAgICAgICAgICBpZiAodSkge1xuICAgICAgICAgICAgICAgIGkoMCk7XG4gICAgICAgICAgICAgIH0gZWxzZSBpZiAoIW4pIHtcbiAgICAgICAgICAgICAgICBuID0gITA7XG4gICAgICAgICAgICAgICAgYSgwKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIGlmICgwID09PSBlLnRhZykge1xuICAgICAgICAgICAgICBzID0gITE7XG4gICAgICAgICAgICAgIChmID0gZVswXSkoMCk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICBpKGUpO1xuICAgICAgICAgICAgICBpZiAoIXMpIHtcbiAgICAgICAgICAgICAgICBmKDApO1xuICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHMgPSAhMTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pKTtcbiAgICAgICAgfShyKHRbMF0pKTtcbiAgICAgIH1cbiAgICB9KSk7XG4gICAgaShzdGFydCgoZSA9PiB7XG4gICAgICBpZiAoMSA9PT0gZSkge1xuICAgICAgICBpZiAoIXUpIHtcbiAgICAgICAgICB1ID0gITA7XG4gICAgICAgICAgYSgxKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAobCkge1xuICAgICAgICAgIGwgPSAhMTtcbiAgICAgICAgICBmKDEpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpZiAoIXUgJiYgIW4pIHtcbiAgICAgICAgICBuID0gITA7XG4gICAgICAgICAgYSgwKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAobCAmJiAhcykge1xuICAgICAgICAgIHMgPSAhMDtcbiAgICAgICAgICBmKDApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSkpKTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gc3dpdGNoQWxsKGUpIHtcbiAgcmV0dXJuIHN3aXRjaE1hcChpZGVudGl0eSkoZSk7XG59XG5cbmZ1bmN0aW9uIHRha2Uocikge1xuICByZXR1cm4gdCA9PiBpID0+IHtcbiAgICB2YXIgYSA9IGU7XG4gICAgdmFyIGYgPSAhMTtcbiAgICB2YXIgbiA9IDA7XG4gICAgdCgoZSA9PiB7XG4gICAgICBpZiAoZikge30gZWxzZSBpZiAoMCA9PT0gZSkge1xuICAgICAgICBmID0gITA7XG4gICAgICAgIGkoMCk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgIGlmIChyIDw9IDApIHtcbiAgICAgICAgICBmID0gITA7XG4gICAgICAgICAgaSgwKTtcbiAgICAgICAgICBlWzBdKDEpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGEgPSBlWzBdO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKG4rKyA8IHIpIHtcbiAgICAgICAgaShlKTtcbiAgICAgICAgaWYgKCFmICYmIG4gPj0gcikge1xuICAgICAgICAgIGYgPSAhMDtcbiAgICAgICAgICBpKDApO1xuICAgICAgICAgIGEoMSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGkoZSk7XG4gICAgICB9XG4gICAgfSkpO1xuICAgIGkoc3RhcnQoKGUgPT4ge1xuICAgICAgaWYgKDEgPT09IGUgJiYgIWYpIHtcbiAgICAgICAgZiA9ICEwO1xuICAgICAgICBhKDEpO1xuICAgICAgfSBlbHNlIGlmICgwID09PSBlICYmICFmICYmIG4gPCByKSB7XG4gICAgICAgIGEoMCk7XG4gICAgICB9XG4gICAgfSkpKTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gdGFrZUxhc3QodCkge1xuICByZXR1cm4gaSA9PiBhID0+IHtcbiAgICB2YXIgZiA9IFtdO1xuICAgIHZhciBuID0gZTtcbiAgICBpKChlID0+IHtcbiAgICAgIGlmICgwID09PSBlKSB7XG4gICAgICAgIHIoZikoYSk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgIGlmICh0IDw9IDApIHtcbiAgICAgICAgICBlWzBdKDEpO1xuICAgICAgICAgIHIoZikoYSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgKG4gPSBlWzBdKSgwKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKGYubGVuZ3RoID49IHQgJiYgdCkge1xuICAgICAgICAgIGYuc2hpZnQoKTtcbiAgICAgICAgfVxuICAgICAgICBmLnB1c2goZVswXSk7XG4gICAgICAgIG4oMCk7XG4gICAgICB9XG4gICAgfSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiB0YWtlVW50aWwocikge1xuICByZXR1cm4gdCA9PiBpID0+IHtcbiAgICB2YXIgYSA9IGU7XG4gICAgdmFyIGYgPSBlO1xuICAgIHZhciBuID0gITE7XG4gICAgdCgoZSA9PiB7XG4gICAgICBpZiAobikge30gZWxzZSBpZiAoMCA9PT0gZSkge1xuICAgICAgICBuID0gITA7XG4gICAgICAgIGYoMSk7XG4gICAgICAgIGkoMCk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgIGEgPSBlWzBdO1xuICAgICAgICByKChlID0+IHtcbiAgICAgICAgICBpZiAoMCA9PT0gZSkge30gZWxzZSBpZiAoMCA9PT0gZS50YWcpIHtcbiAgICAgICAgICAgIChmID0gZVswXSkoMCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIG4gPSAhMDtcbiAgICAgICAgICAgIGYoMSk7XG4gICAgICAgICAgICBhKDEpO1xuICAgICAgICAgICAgaSgwKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGkoZSk7XG4gICAgICB9XG4gICAgfSkpO1xuICAgIGkoc3RhcnQoKGUgPT4ge1xuICAgICAgaWYgKDEgPT09IGUgJiYgIW4pIHtcbiAgICAgICAgbiA9ICEwO1xuICAgICAgICBhKDEpO1xuICAgICAgICBmKDEpO1xuICAgICAgfSBlbHNlIGlmICghbikge1xuICAgICAgICBhKDApO1xuICAgICAgfVxuICAgIH0pKSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIHRha2VXaGlsZShyLCB0KSB7XG4gIHJldHVybiBpID0+IGEgPT4ge1xuICAgIHZhciBmID0gZTtcbiAgICB2YXIgbiA9ICExO1xuICAgIGkoKGUgPT4ge1xuICAgICAgaWYgKG4pIHt9IGVsc2UgaWYgKDAgPT09IGUpIHtcbiAgICAgICAgbiA9ICEwO1xuICAgICAgICBhKDApO1xuICAgICAgfSBlbHNlIGlmICgwID09PSBlLnRhZykge1xuICAgICAgICBmID0gZVswXTtcbiAgICAgICAgYShlKTtcbiAgICAgIH0gZWxzZSBpZiAoIXIoZVswXSkpIHtcbiAgICAgICAgbiA9ICEwO1xuICAgICAgICBpZiAodCkge1xuICAgICAgICAgIGEoZSk7XG4gICAgICAgIH1cbiAgICAgICAgYSgwKTtcbiAgICAgICAgZigxKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGEoZSk7XG4gICAgICB9XG4gICAgfSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBkZWJvdW5jZShlKSB7XG4gIHJldHVybiByID0+IHQgPT4ge1xuICAgIHZhciBpO1xuICAgIHZhciBhID0gITE7XG4gICAgdmFyIGYgPSAhMTtcbiAgICByKChyID0+IHtcbiAgICAgIGlmIChmKSB7fSBlbHNlIGlmICgwID09PSByKSB7XG4gICAgICAgIGYgPSAhMDtcbiAgICAgICAgaWYgKGkpIHtcbiAgICAgICAgICBhID0gITA7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdCgwKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmICgwID09PSByLnRhZykge1xuICAgICAgICB2YXIgbiA9IHJbMF07XG4gICAgICAgIHQoc3RhcnQoKGUgPT4ge1xuICAgICAgICAgIGlmICgxID09PSBlICYmICFmKSB7XG4gICAgICAgICAgICBmID0gITA7XG4gICAgICAgICAgICBhID0gITE7XG4gICAgICAgICAgICBpZiAoaSkge1xuICAgICAgICAgICAgICBjbGVhclRpbWVvdXQoaSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBuKDEpO1xuICAgICAgICAgIH0gZWxzZSBpZiAoIWYpIHtcbiAgICAgICAgICAgIG4oMCk7XG4gICAgICAgICAgfVxuICAgICAgICB9KSkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgaWYgKGkpIHtcbiAgICAgICAgICBjbGVhclRpbWVvdXQoaSk7XG4gICAgICAgIH1cbiAgICAgICAgaSA9IHNldFRpbWVvdXQoKCgpID0+IHtcbiAgICAgICAgICBpID0gdm9pZCAwO1xuICAgICAgICAgIHQocik7XG4gICAgICAgICAgaWYgKGEpIHtcbiAgICAgICAgICAgIHQoMCk7XG4gICAgICAgICAgfVxuICAgICAgICB9KSwgZShyWzBdKSk7XG4gICAgICB9XG4gICAgfSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiBkZWxheShlKSB7XG4gIHJldHVybiByID0+IHQgPT4ge1xuICAgIHZhciBpID0gMDtcbiAgICByKChyID0+IHtcbiAgICAgIGlmICgwICE9PSByICYmIDAgPT09IHIudGFnKSB7XG4gICAgICAgIHQocik7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBpKys7XG4gICAgICAgIHNldFRpbWVvdXQoKCgpID0+IHtcbiAgICAgICAgICBpZiAoaSkge1xuICAgICAgICAgICAgaS0tO1xuICAgICAgICAgICAgdChyKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pLCBlKTtcbiAgICAgIH1cbiAgICB9KSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIHRocm90dGxlKGUpIHtcbiAgcmV0dXJuIHIgPT4gdCA9PiB7XG4gICAgdmFyIGkgPSAhMTtcbiAgICB2YXIgYTtcbiAgICByKChyID0+IHtcbiAgICAgIGlmICgwID09PSByKSB7XG4gICAgICAgIGlmIChhKSB7XG4gICAgICAgICAgY2xlYXJUaW1lb3V0KGEpO1xuICAgICAgICB9XG4gICAgICAgIHQoMCk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IHIudGFnKSB7XG4gICAgICAgIHZhciBmID0gclswXTtcbiAgICAgICAgdChzdGFydCgoZSA9PiB7XG4gICAgICAgICAgaWYgKDEgPT09IGUpIHtcbiAgICAgICAgICAgIGlmIChhKSB7XG4gICAgICAgICAgICAgIGNsZWFyVGltZW91dChhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGYoMSk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGYoMCk7XG4gICAgICAgICAgfVxuICAgICAgICB9KSkpO1xuICAgICAgfSBlbHNlIGlmICghaSkge1xuICAgICAgICBpID0gITA7XG4gICAgICAgIGlmIChhKSB7XG4gICAgICAgICAgY2xlYXJUaW1lb3V0KGEpO1xuICAgICAgICB9XG4gICAgICAgIGEgPSBzZXRUaW1lb3V0KCgoKSA9PiB7XG4gICAgICAgICAgYSA9IHZvaWQgMDtcbiAgICAgICAgICBpID0gITE7XG4gICAgICAgIH0pLCBlKHJbMF0pKTtcbiAgICAgICAgdChyKTtcbiAgICAgIH1cbiAgICB9KSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIGxhenkoZSkge1xuICByZXR1cm4gciA9PiBlKCkocik7XG59XG5cbmZ1bmN0aW9uIGZyb21Bc3luY0l0ZXJhYmxlKGUpIHtcbiAgcmV0dXJuIHIgPT4ge1xuICAgIHZhciB0ID0gZVthc3luY0l0ZXJhdG9yU3ltYm9sKCldICYmIGVbYXN5bmNJdGVyYXRvclN5bWJvbCgpXSgpIHx8IGU7XG4gICAgdmFyIGkgPSAhMTtcbiAgICB2YXIgYSA9ICExO1xuICAgIHZhciBmID0gITE7XG4gICAgdmFyIG47XG4gICAgcihzdGFydCgoYXN5bmMgZSA9PiB7XG4gICAgICBpZiAoMSA9PT0gZSkge1xuICAgICAgICBpID0gITA7XG4gICAgICAgIGlmICh0LnJldHVybikge1xuICAgICAgICAgIHQucmV0dXJuKCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoYSkge1xuICAgICAgICBmID0gITA7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBmb3IgKGYgPSBhID0gITA7IGYgJiYgIWk7ICkge1xuICAgICAgICAgIGlmICgobiA9IGF3YWl0IHQubmV4dCgpKS5kb25lKSB7XG4gICAgICAgICAgICBpID0gITA7XG4gICAgICAgICAgICBpZiAodC5yZXR1cm4pIHtcbiAgICAgICAgICAgICAgYXdhaXQgdC5yZXR1cm4oKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHIoMCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGYgPSAhMTtcbiAgICAgICAgICAgICAgcihwdXNoKG4udmFsdWUpKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgaWYgKHQudGhyb3cpIHtcbiAgICAgICAgICAgICAgICBpZiAoaSA9ICEhKGF3YWl0IHQudGhyb3coZSkpLmRvbmUpIHtcbiAgICAgICAgICAgICAgICAgIHIoMCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRocm93IGU7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgYSA9ICExO1xuICAgICAgfVxuICAgIH0pKSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIGZyb21JdGVyYWJsZShlKSB7XG4gIGlmIChlW1N5bWJvbC5hc3luY0l0ZXJhdG9yXSkge1xuICAgIHJldHVybiBmcm9tQXN5bmNJdGVyYWJsZShlKTtcbiAgfVxuICByZXR1cm4gciA9PiB7XG4gICAgdmFyIHQgPSBlW1N5bWJvbC5pdGVyYXRvcl0oKTtcbiAgICB2YXIgaSA9ICExO1xuICAgIHZhciBhID0gITE7XG4gICAgdmFyIGYgPSAhMTtcbiAgICB2YXIgbjtcbiAgICByKHN0YXJ0KChlID0+IHtcbiAgICAgIGlmICgxID09PSBlKSB7XG4gICAgICAgIGkgPSAhMDtcbiAgICAgICAgaWYgKHQucmV0dXJuKSB7XG4gICAgICAgICAgdC5yZXR1cm4oKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChhKSB7XG4gICAgICAgIGYgPSAhMDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGZvciAoZiA9IGEgPSAhMDsgZiAmJiAhaTsgKSB7XG4gICAgICAgICAgaWYgKChuID0gdC5uZXh0KCkpLmRvbmUpIHtcbiAgICAgICAgICAgIGkgPSAhMDtcbiAgICAgICAgICAgIGlmICh0LnJldHVybikge1xuICAgICAgICAgICAgICB0LnJldHVybigpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcigwKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgZiA9ICExO1xuICAgICAgICAgICAgICByKHB1c2gobi52YWx1ZSkpO1xuICAgICAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICBpZiAodC50aHJvdykge1xuICAgICAgICAgICAgICAgIGlmIChpID0gISF0LnRocm93KGUpLmRvbmUpIHtcbiAgICAgICAgICAgICAgICAgIHIoMCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRocm93IGU7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgYSA9ICExO1xuICAgICAgfVxuICAgIH0pKSk7XG4gIH07XG59XG5cbnZhciByID0gZnJvbUl0ZXJhYmxlO1xuXG5mdW5jdGlvbiBmcm9tVmFsdWUoZSkge1xuICByZXR1cm4gciA9PiB7XG4gICAgdmFyIHQgPSAhMTtcbiAgICByKHN0YXJ0KChpID0+IHtcbiAgICAgIGlmICgxID09PSBpKSB7XG4gICAgICAgIHQgPSAhMDtcbiAgICAgIH0gZWxzZSBpZiAoIXQpIHtcbiAgICAgICAgdCA9ICEwO1xuICAgICAgICByKHB1c2goZSkpO1xuICAgICAgICByKDApO1xuICAgICAgfVxuICAgIH0pKSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIG1ha2UoZSkge1xuICByZXR1cm4gciA9PiB7XG4gICAgdmFyIHQgPSAhMTtcbiAgICB2YXIgaSA9IGUoe1xuICAgICAgbmV4dChlKSB7XG4gICAgICAgIGlmICghdCkge1xuICAgICAgICAgIHIocHVzaChlKSk7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICBjb21wbGV0ZSgpIHtcbiAgICAgICAgaWYgKCF0KSB7XG4gICAgICAgICAgdCA9ICEwO1xuICAgICAgICAgIHIoMCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcbiAgICByKHN0YXJ0KChlID0+IHtcbiAgICAgIGlmICgxID09PSBlICYmICF0KSB7XG4gICAgICAgIHQgPSAhMDtcbiAgICAgICAgaSgpO1xuICAgICAgfVxuICAgIH0pKSk7XG4gIH07XG59XG5cbmZ1bmN0aW9uIG1ha2VTdWJqZWN0KCkge1xuICB2YXIgZTtcbiAgdmFyIHI7XG4gIHJldHVybiB7XG4gICAgc291cmNlOiBzaGFyZShtYWtlKCh0ID0+IHtcbiAgICAgIGUgPSB0Lm5leHQ7XG4gICAgICByID0gdC5jb21wbGV0ZTtcbiAgICAgIHJldHVybiB0ZWFyZG93blBsYWNlaG9sZGVyO1xuICAgIH0pKSksXG4gICAgbmV4dChyKSB7XG4gICAgICBpZiAoZSkge1xuICAgICAgICBlKHIpO1xuICAgICAgfVxuICAgIH0sXG4gICAgY29tcGxldGUoKSB7XG4gICAgICBpZiAocikge1xuICAgICAgICByKCk7XG4gICAgICB9XG4gICAgfVxuICB9O1xufVxuXG52YXIgZW1wdHkgPSBlID0+IHtcbiAgdmFyIHIgPSAhMTtcbiAgZShzdGFydCgodCA9PiB7XG4gICAgaWYgKDEgPT09IHQpIHtcbiAgICAgIHIgPSAhMDtcbiAgICB9IGVsc2UgaWYgKCFyKSB7XG4gICAgICByID0gITA7XG4gICAgICBlKDApO1xuICAgIH1cbiAgfSkpKTtcbn07XG5cbnZhciBuZXZlciA9IHIgPT4ge1xuICByKHN0YXJ0KGUpKTtcbn07XG5cbmZ1bmN0aW9uIGludGVydmFsKGUpIHtcbiAgcmV0dXJuIG1ha2UoKHIgPT4ge1xuICAgIHZhciB0ID0gMDtcbiAgICB2YXIgaSA9IHNldEludGVydmFsKCgoKSA9PiByLm5leHQodCsrKSksIGUpO1xuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGkpO1xuICB9KSk7XG59XG5cbmZ1bmN0aW9uIGZyb21Eb21FdmVudChlLCByKSB7XG4gIHJldHVybiBtYWtlKCh0ID0+IHtcbiAgICBlLmFkZEV2ZW50TGlzdGVuZXIociwgdC5uZXh0KTtcbiAgICByZXR1cm4gKCkgPT4gZS5yZW1vdmVFdmVudExpc3RlbmVyKHIsIHQubmV4dCk7XG4gIH0pKTtcbn1cblxuZnVuY3Rpb24gZnJvbVByb21pc2UoZSkge1xuICByZXR1cm4gbWFrZSgociA9PiB7XG4gICAgZS50aGVuKChlID0+IHtcbiAgICAgIFByb21pc2UucmVzb2x2ZShlKS50aGVuKCgoKSA9PiB7XG4gICAgICAgIHIubmV4dChlKTtcbiAgICAgICAgci5jb21wbGV0ZSgpO1xuICAgICAgfSkpO1xuICAgIH0pKTtcbiAgICByZXR1cm4gdGVhcmRvd25QbGFjZWhvbGRlcjtcbiAgfSkpO1xufVxuXG5mdW5jdGlvbiBzdWJzY3JpYmUocikge1xuICByZXR1cm4gdCA9PiB7XG4gICAgdmFyIGkgPSBlO1xuICAgIHZhciBhID0gITE7XG4gICAgdCgoZSA9PiB7XG4gICAgICBpZiAoMCA9PT0gZSkge1xuICAgICAgICBhID0gITA7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgIChpID0gZVswXSkoMCk7XG4gICAgICB9IGVsc2UgaWYgKCFhKSB7XG4gICAgICAgIHIoZVswXSk7XG4gICAgICAgIGkoMCk7XG4gICAgICB9XG4gICAgfSkpO1xuICAgIHJldHVybiB7XG4gICAgICB1bnN1YnNjcmliZSgpIHtcbiAgICAgICAgaWYgKCFhKSB7XG4gICAgICAgICAgYSA9ICEwO1xuICAgICAgICAgIGkoMSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9O1xuICB9O1xufVxuXG5mdW5jdGlvbiBmb3JFYWNoKGUpIHtcbiAgcmV0dXJuIHIgPT4ge1xuICAgIHN1YnNjcmliZShlKShyKTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gcHVibGlzaChlKSB7XG4gIHN1YnNjcmliZSgoZSA9PiB7fSkpKGUpO1xufVxuXG52YXIgdCA9IHtcbiAgZG9uZTogITBcbn07XG5cbnZhciB0b0FzeW5jSXRlcmFibGUgPSByID0+IHtcbiAgdmFyIGkgPSBbXTtcbiAgdmFyIGEgPSAhMTtcbiAgdmFyIGYgPSAhMTtcbiAgdmFyIG4gPSAhMTtcbiAgdmFyIHMgPSBlO1xuICB2YXIgbDtcbiAgcmV0dXJuIHtcbiAgICBhc3luYyBuZXh0KCkge1xuICAgICAgaWYgKCFmKSB7XG4gICAgICAgIGYgPSAhMDtcbiAgICAgICAgcigoZSA9PiB7XG4gICAgICAgICAgaWYgKGEpIHt9IGVsc2UgaWYgKDAgPT09IGUpIHtcbiAgICAgICAgICAgIGlmIChsKSB7XG4gICAgICAgICAgICAgIGwgPSBsKHQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYSA9ICEwO1xuICAgICAgICAgIH0gZWxzZSBpZiAoMCA9PT0gZS50YWcpIHtcbiAgICAgICAgICAgIG4gPSAhMDtcbiAgICAgICAgICAgIChzID0gZVswXSkoMCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIG4gPSAhMTtcbiAgICAgICAgICAgIGlmIChsKSB7XG4gICAgICAgICAgICAgIGwgPSBsKHtcbiAgICAgICAgICAgICAgICB2YWx1ZTogZVswXSxcbiAgICAgICAgICAgICAgICBkb25lOiAhMVxuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIGkucHVzaChlWzBdKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcbiAgICAgIH1cbiAgICAgIGlmIChhICYmICFpLmxlbmd0aCkge1xuICAgICAgICByZXR1cm4gdDtcbiAgICAgIH0gZWxzZSBpZiAoIWEgJiYgIW4gJiYgaS5sZW5ndGggPD0gMSkge1xuICAgICAgICBuID0gITA7XG4gICAgICAgIHMoMCk7XG4gICAgICB9XG4gICAgICByZXR1cm4gaS5sZW5ndGggPyB7XG4gICAgICAgIHZhbHVlOiBpLnNoaWZ0KCksXG4gICAgICAgIGRvbmU6ICExXG4gICAgICB9IDogbmV3IFByb21pc2UoKGUgPT4gbCA9IGUpKTtcbiAgICB9LFxuICAgIGFzeW5jIHJldHVybigpIHtcbiAgICAgIGlmICghYSkge1xuICAgICAgICBsID0gcygxKTtcbiAgICAgIH1cbiAgICAgIGEgPSAhMDtcbiAgICAgIHJldHVybiB0O1xuICAgIH0sXG4gICAgW2FzeW5jSXRlcmF0b3JTeW1ib2woKV0oKSB7XG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gIH07XG59O1xuXG5mdW5jdGlvbiB0b0FycmF5KHIpIHtcbiAgdmFyIHQgPSBbXTtcbiAgdmFyIGkgPSBlO1xuICB2YXIgYSA9ICExO1xuICByKChlID0+IHtcbiAgICBpZiAoMCA9PT0gZSkge1xuICAgICAgYSA9ICEwO1xuICAgIH0gZWxzZSBpZiAoMCA9PT0gZS50YWcpIHtcbiAgICAgIChpID0gZVswXSkoMCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHQucHVzaChlWzBdKTtcbiAgICAgIGkoMCk7XG4gICAgfVxuICB9KSk7XG4gIGlmICghYSkge1xuICAgIGkoMSk7XG4gIH1cbiAgcmV0dXJuIHQ7XG59XG5cbmZ1bmN0aW9uIHRvUHJvbWlzZShyKSB7XG4gIHJldHVybiBuZXcgUHJvbWlzZSgodCA9PiB7XG4gICAgdmFyIGkgPSBlO1xuICAgIHZhciBhO1xuICAgIHIoKGUgPT4ge1xuICAgICAgaWYgKDAgPT09IGUpIHtcbiAgICAgICAgUHJvbWlzZS5yZXNvbHZlKGEpLnRoZW4odCk7XG4gICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgIChpID0gZVswXSkoMCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhID0gZVswXTtcbiAgICAgICAgaSgwKTtcbiAgICAgIH1cbiAgICB9KSk7XG4gIH0pKTtcbn1cblxuZnVuY3Rpb24gemlwKHIpIHtcbiAgdmFyIHQgPSBPYmplY3Qua2V5cyhyKS5sZW5ndGg7XG4gIHJldHVybiBpID0+IHtcbiAgICB2YXIgYSA9IG5ldyBTZXQ7XG4gICAgdmFyIGYgPSBBcnJheS5pc0FycmF5KHIpID8gbmV3IEFycmF5KHQpLmZpbGwoZSkgOiB7fTtcbiAgICB2YXIgbiA9IEFycmF5LmlzQXJyYXkocikgPyBuZXcgQXJyYXkodCkgOiB7fTtcbiAgICB2YXIgcyA9ICExO1xuICAgIHZhciBsID0gITE7XG4gICAgdmFyIHUgPSAhMTtcbiAgICB2YXIgbyA9IDA7XG4gICAgdmFyIGxvb3AgPSBmdW5jdGlvbih2KSB7XG4gICAgICByW3ZdKChjID0+IHtcbiAgICAgICAgaWYgKDAgPT09IGMpIHtcbiAgICAgICAgICBpZiAobyA+PSB0IC0gMSkge1xuICAgICAgICAgICAgdSA9ICEwO1xuICAgICAgICAgICAgaSgwKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgbysrO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmICgwID09PSBjLnRhZykge1xuICAgICAgICAgIGZbdl0gPSBjWzBdO1xuICAgICAgICB9IGVsc2UgaWYgKCF1KSB7XG4gICAgICAgICAgblt2XSA9IGNbMF07XG4gICAgICAgICAgYS5hZGQodik7XG4gICAgICAgICAgaWYgKCFzICYmIGEuc2l6ZSA8IHQpIHtcbiAgICAgICAgICAgIGlmICghbCkge1xuICAgICAgICAgICAgICBmb3IgKHZhciBoIGluIHIpIHtcbiAgICAgICAgICAgICAgICBpZiAoIWEuaGFzKGgpKSB7XG4gICAgICAgICAgICAgICAgICAoZltoXSB8fCBlKSgwKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIGwgPSAhMTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcyA9ICEwO1xuICAgICAgICAgICAgbCA9ICExO1xuICAgICAgICAgICAgaShwdXNoKEFycmF5LmlzQXJyYXkobikgPyBuLnNsaWNlKCkgOiB7XG4gICAgICAgICAgICAgIC4uLm5cbiAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0pKTtcbiAgICB9O1xuICAgIGZvciAodmFyIHYgaW4gcikge1xuICAgICAgbG9vcCh2KTtcbiAgICB9XG4gICAgaShzdGFydCgoZSA9PiB7XG4gICAgICBpZiAodSkge30gZWxzZSBpZiAoMSA9PT0gZSkge1xuICAgICAgICB1ID0gITA7XG4gICAgICAgIGZvciAodmFyIHIgaW4gZikge1xuICAgICAgICAgIGZbcl0oMSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoIWwpIHtcbiAgICAgICAgbCA9ICEwO1xuICAgICAgICBmb3IgKHZhciB0IGluIGYpIHtcbiAgICAgICAgICBmW3RdKDApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSkpKTtcbiAgfTtcbn1cblxuZnVuY3Rpb24gY29tYmluZSguLi5lKSB7XG4gIHJldHVybiB6aXAoZSk7XG59XG5cbmZ1bmN0aW9uIGZyb21PYnNlcnZhYmxlKGUpIHtcbiAgcmV0dXJuIHIgPT4ge1xuICAgIHZhciB0ID0gKGVbb2JzZXJ2YWJsZVN5bWJvbCgpXSA/IGVbb2JzZXJ2YWJsZVN5bWJvbCgpXSgpIDogZSkuc3Vic2NyaWJlKHtcbiAgICAgIG5leHQoZSkge1xuICAgICAgICByKHB1c2goZSkpO1xuICAgICAgfSxcbiAgICAgIGNvbXBsZXRlKCkge1xuICAgICAgICByKDApO1xuICAgICAgfSxcbiAgICAgIGVycm9yKGUpIHtcbiAgICAgICAgdGhyb3cgZTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICByKHN0YXJ0KChlID0+IHtcbiAgICAgIGlmICgxID09PSBlKSB7XG4gICAgICAgIHQudW5zdWJzY3JpYmUoKTtcbiAgICAgIH1cbiAgICB9KSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiB0b09ic2VydmFibGUocikge1xuICByZXR1cm4ge1xuICAgIHN1YnNjcmliZSh0LCBpLCBhKSB7XG4gICAgICB2YXIgZiA9IFwib2JqZWN0XCIgPT0gdHlwZW9mIHQgPyB0IDoge1xuICAgICAgICBuZXh0OiB0LFxuICAgICAgICBlcnJvcjogaSxcbiAgICAgICAgY29tcGxldGU6IGFcbiAgICAgIH07XG4gICAgICB2YXIgbiA9IGU7XG4gICAgICB2YXIgcyA9ICExO1xuICAgICAgcigoZSA9PiB7XG4gICAgICAgIGlmIChzKSB7fSBlbHNlIGlmICgwID09PSBlKSB7XG4gICAgICAgICAgcyA9ICEwO1xuICAgICAgICAgIGlmIChmLmNvbXBsZXRlKSB7XG4gICAgICAgICAgICBmLmNvbXBsZXRlKCk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKDAgPT09IGUudGFnKSB7XG4gICAgICAgICAgKG4gPSBlWzBdKSgwKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBmLm5leHQoZVswXSk7XG4gICAgICAgICAgbigwKTtcbiAgICAgICAgfVxuICAgICAgfSkpO1xuICAgICAgdmFyIGwgPSB7XG4gICAgICAgIGNsb3NlZDogITEsXG4gICAgICAgIHVuc3Vic2NyaWJlKCkge1xuICAgICAgICAgIGwuY2xvc2VkID0gITA7XG4gICAgICAgICAgcyA9ICEwO1xuICAgICAgICAgIG4oMSk7XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgICByZXR1cm4gbDtcbiAgICB9LFxuICAgIFtvYnNlcnZhYmxlU3ltYm9sKCldKCkge1xuICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICB9O1xufVxuXG5mdW5jdGlvbiBmcm9tQ2FsbGJhZyhlKSB7XG4gIHJldHVybiByID0+IHtcbiAgICBlKDAsICgoZSwgdCkgPT4ge1xuICAgICAgaWYgKDAgPT09IGUpIHtcbiAgICAgICAgcihzdGFydCgoZSA9PiB7XG4gICAgICAgICAgdChlICsgMSk7XG4gICAgICAgIH0pKSk7XG4gICAgICB9IGVsc2UgaWYgKDEgPT09IGUpIHtcbiAgICAgICAgcihwdXNoKHQpKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHIoMCk7XG4gICAgICB9XG4gICAgfSkpO1xuICB9O1xufVxuXG5mdW5jdGlvbiB0b0NhbGxiYWcoZSkge1xuICByZXR1cm4gKHIsIHQpID0+IHtcbiAgICBpZiAoMCA9PT0gcikge1xuICAgICAgZSgoZSA9PiB7XG4gICAgICAgIGlmICgwID09PSBlKSB7XG4gICAgICAgICAgdCgyKTtcbiAgICAgICAgfSBlbHNlIGlmICgwID09PSBlLnRhZykge1xuICAgICAgICAgIHQoMCwgKHIgPT4ge1xuICAgICAgICAgICAgaWYgKHIgPCAzKSB7XG4gICAgICAgICAgICAgIGVbMF0ociAtIDEpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0KDEsIGVbMF0pO1xuICAgICAgICB9XG4gICAgICB9KSk7XG4gICAgfVxuICB9O1xufVxuXG52YXIgcGlwZSA9ICguLi5lKSA9PiB7XG4gIHZhciByID0gZVswXTtcbiAgZm9yICh2YXIgdCA9IDEsIGkgPSBlLmxlbmd0aDsgdCA8IGk7IHQrKykge1xuICAgIHIgPSBlW3RdKHIpO1xuICB9XG4gIHJldHVybiByO1xufTtcblxuZXhwb3J0IHsgYnVmZmVyLCBjb21iaW5lLCBjb25jYXQsIGNvbmNhdEFsbCwgY29uY2F0TWFwLCBkZWJvdW5jZSwgZGVsYXksIGVtcHR5LCBmaWx0ZXIsIG1lcmdlQWxsIGFzIGZsYXR0ZW4sIGZvckVhY2gsIHIgYXMgZnJvbUFycmF5LCBmcm9tQXN5bmNJdGVyYWJsZSwgZnJvbUNhbGxiYWcsIGZyb21Eb21FdmVudCwgZnJvbUl0ZXJhYmxlLCBmcm9tT2JzZXJ2YWJsZSwgZnJvbVByb21pc2UsIGZyb21WYWx1ZSwgaW50ZXJ2YWwsIGxhenksIG1ha2UsIG1ha2VTdWJqZWN0LCBtYXAsIG1lcmdlLCBtZXJnZUFsbCwgbWVyZ2VNYXAsIG5ldmVyLCBvbkVuZCwgb25QdXNoLCBvblN0YXJ0LCBwaXBlLCBwdWJsaXNoLCBzYW1wbGUsIHNjYW4sIHNoYXJlLCBza2lwLCBza2lwVW50aWwsIHNraXBXaGlsZSwgc3Vic2NyaWJlLCBzd2l0Y2hBbGwsIHN3aXRjaE1hcCwgdGFrZSwgdGFrZUxhc3QsIHRha2VVbnRpbCwgdGFrZVdoaWxlLCBvblB1c2ggYXMgdGFwLCB0aHJvdHRsZSwgdG9BcnJheSwgdG9Bc3luY0l0ZXJhYmxlLCB0b0NhbGxiYWcsIHRvT2JzZXJ2YWJsZSwgdG9Qcm9taXNlLCB6aXAgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXdvbmthLm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/wonka/dist/wonka.mjs\n");

/***/ })

};
;