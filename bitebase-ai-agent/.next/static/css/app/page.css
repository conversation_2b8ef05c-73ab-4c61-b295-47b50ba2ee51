/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[10].use[3]!./node_modules/@copilotkit/react-ui/dist/index.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/* src/css/colors.css */
:root {
  --copilot-kit-primary-color: rgb(28, 28, 28);
  --copilot-kit-contrast-color: rgb(255, 255, 255);
  --copilot-kit-background-color: rgb(255 255 255);
  --copilot-kit-input-background-color: #fbfbfb;
  --copilot-kit-secondary-color: rgb(255 255 255);
  --copilot-kit-secondary-contrast-color: rgb(28, 28, 28);
  --copilot-kit-separator-color: rgb(200 200 200);
  --copilot-kit-muted-color: rgb(200 200 200);
  --copilot-kit-error-background: #fef2f2;
  --copilot-kit-error-border: #fecaca;
  --copilot-kit-error-text: #dc2626;
  --copilot-kit-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --copilot-kit-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --copilot-kit-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --copilot-kit-dev-console-bg: #f8f8fa;
  --copilot-kit-dev-console-text: black;
}
.dark,
html.dark,
body.dark,
[data-theme=dark],
html[style*="color-scheme: dark"],
body[style*="color-scheme: dark"] :root {
  --copilot-kit-primary-color: rgb(255, 255, 255);
  --copilot-kit-contrast-color: rgb(28, 28, 28);
  --copilot-kit-background-color: rgb(17, 17, 17);
  --copilot-kit-input-background-color: #2c2c2c;
  --copilot-kit-secondary-color: rgb(28, 28, 28);
  --copilot-kit-secondary-contrast-color: rgb(255, 255, 255);
  --copilot-kit-separator-color: rgb(45, 45, 45);
  --copilot-kit-muted-color: rgb(45, 45, 45);
  --copilot-kit-error-background: #7f1d1d;
  --copilot-kit-error-border: #dc2626;
  --copilot-kit-error-text: #fca5a5;
  --copilot-kit-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --copilot-kit-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --copilot-kit-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

/* src/css/popup.css */
.copilotKitPopup {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 30;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  touch-action: manipulation;
}
.copilotKitPopup svg {
  display: inline-block;
  vertical-align: middle;
}

/* src/css/sidebar.css */
.copilotKitSidebar {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 30;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  touch-action: manipulation;
}
.copilotKitSidebar svg {
  display: inline-block;
  vertical-align: middle;
}
.copilotKitSidebarContentWrapper {
  overflow: visible;
  margin-right: 0px;
  transition: margin-right 0.3s ease;
}
@media (min-width: 640px) {
  .copilotKitSidebarContentWrapper.sidebarExpanded {
    margin-right: 28rem;
  }
}

/* src/css/button.css */
.copilotKitButton {
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--copilot-kit-primary-color);
  outline: none;
  position: relative;
  transform: scale(1);
  transition: all 0.2s ease;
  background-color: var(--copilot-kit-primary-color);
  color: var(--copilot-kit-contrast-color);
  cursor: pointer;
  box-shadow: var(--copilot-kit-shadow-sm);
}
.copilotKitButton:hover {
  transform: scale(1.05);
  box-shadow: var(--copilot-kit-shadow-md);
}
.copilotKitButton:active {
  transform: scale(0.95);
  box-shadow: var(--copilot-kit-shadow-sm);
}
.copilotKitButtonIcon {
  transition: opacity 100ms, transform 300ms;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.copilotKitButtonIcon svg {
  width: 1.5rem;
  height: 1.5rem;
}
.copilotKitButton.open .copilotKitButtonIconOpen {
  transform: translate(-50%, -50%) scale(0) rotate(90deg);
  opacity: 0;
}
.copilotKitButton.open .copilotKitButtonIconClose {
  transform: translate(-50%, -50%) scale(1) rotate(0deg);
  opacity: 1;
}
.copilotKitButton:not(.open) .copilotKitButtonIconOpen {
  transform: translate(-50%, -50%) scale(1) rotate(0deg);
  opacity: 1;
}
.copilotKitButton:not(.open) .copilotKitButtonIconClose {
  transform: translate(-50%, -50%) scale(0) rotate(-90deg);
  opacity: 0;
}

/* src/css/header.css */
.copilotKitHeader {
  height: 56px;
  font-weight: 500;
  display: flex;
  align-items: center;
  position: relative;
  color: var(--copilot-kit-primary-color);
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom: 1px solid var(--copilot-kit-separator-color);
  padding-left: 1.5rem;
  background-color: var(--copilot-kit-contrast-color);
  justify-content: space-between;
  z-index: 2;
}
.copilotKitSidebar .copilotKitHeader {
  border-radius: 0;
}
.copilotKitHeaderControls {
  display: flex;
}
.copilotKitHeaderCloseButton {
  background: none;
  border: none;
}
@media (min-width: 640px) {
  .copilotKitHeader {
    padding-left: 1.5rem;
    padding-right: 24px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
}
.copilotKitHeader > button {
  border: 0;
  padding: 8px;
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  outline: none;
  color: var(--copilot-kit-muted-color);
  background-color: transparent;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  width: 35px;
  height: 35px;
}
.copilotKitHeader > button:hover {
  color: color-mix(in srgb, var(--copilot-kit-muted-color) 80%, black);
}
.copilotKitHeader > button:focus {
  outline: none;
}

/* src/css/input.css */
.copilotKitInput {
  cursor: text;
  position: relative;
  background-color: var(--copilot-kit-input-background-color);
  border-radius: 20px;
  border: 1px solid var(--copilot-kit-separator-color);
  padding: 12px 14px;
  min-height: 75px;
  margin: 0 auto;
  width: 95%;
}
.copilotKitInputContainer {
  width: 100%;
  padding: 0;
  padding-bottom: 15px;
  background: var(--copilot-kit-background-color);
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
.copilotKitSidebar .copilotKitInputContainer {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.copilotKitInputControlButton {
  padding: 0;
  cursor: pointer;
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  transform: scale(1);
  color: rgba(0, 0, 0, 0.25);
  -webkit-appearance: button;
  appearance: button;
  background-color: transparent;
  background-image: none;
  text-transform: none;
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  border: 0;
  margin: 0;
  text-indent: 0px;
  text-shadow: none;
  display: inline-block;
  text-align: center;
  width: 24px;
  height: 24px;
}
.copilotKitInputControlButton:not([disabled]) {
  color: var(--copilot-kit-primary-color);
}
.copilotKitInputControlButton:not([disabled]):hover {
  color: color-mix(in srgb, var(--copilot-kit-primary-color) 80%, black);
  transform: scale(1.05);
}
.copilotKitInputControlButton[disabled] {
  color: var(--copilot-kit-muted-color);
  cursor: default;
}
.copilotKitInputControls {
  display: flex;
  gap: 3px;
}
.copilotKitInput > textarea {
  flex: 1;
  outline: 2px solid transparent;
  outline-offset: 2px;
  resize: none;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: text;
  font-size: 0.875rem;
  line-height: 1.5rem;
  margin: 0;
  padding: 0;
  font-family: inherit;
  font-weight: inherit;
  color: var(--copilot-kit-secondary-contrast-color);
  border: 0px;
  background-color: transparent;
  width: 100%;
}
.copilotKitInput > textarea::placeholder {
  color: #808080;
  opacity: 1;
}
.copilotKitInputControlButton.copilotKitPushToTalkRecording {
  background-color: #ec0000;
  color: white;
  border-radius: 50%;
  animation: copilotKitPulseAnimation 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
.copilotKitInput textarea::-webkit-scrollbar {
  width: 9px;
}
.copilotKitInput textarea::-webkit-scrollbar-track {
  background: transparent;
}
.copilotKitInput textarea::-webkit-scrollbar-thumb {
  background-color: rgb(200 200 200);
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  cursor: pointer;
}
.copilotKitInput textarea::-webkit-scrollbar-thumb:hover {
  background-color: color-mix(in srgb, rgb(200 200 200) 80%, black);
}
.poweredByContainer {
  padding: 0;
}
.poweredBy {
  background: var(--copilot-kit-background-color) !important;
  visibility: visible !important;
  display: block !important;
  position: static !important;
  text-align: center !important;
  font-size: 12px !important;
  padding: 3px 0 !important;
  color: rgb(214, 214, 214) !important;
  margin: 0 !important;
}
.dark,
html.dark,
body.dark,
[data-theme=dark],
html[style*="color-scheme: dark"],
body[style*="color-scheme: dark"] .poweredBy {
  color: rgb(69, 69, 69) !important;
}

/* src/css/messages.css */
.copilotKitMessages {
  overflow-y: scroll;
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--copilot-kit-background-color);
  justify-content: space-between;
  z-index: 1;
}
.copilotKitMessagesContainer {
  padding: 1rem 24px;
  display: flex;
  flex-direction: column;
}
.copilotKitMessagesFooter {
  display: flex;
  padding: 0.5rem 0.75rem;
  margin: 8px auto 0 auto;
  justify-content: flex-start;
  flex-direction: column;
  width: 97%;
}
.copilotKitMessages::-webkit-scrollbar {
  width: 6px;
}
.copilotKitMessages::-webkit-scrollbar-thumb {
  background-color: var(--copilot-kit-separator-color);
  border-radius: 10rem;
  border: 2px solid var(--copilot-kit-background-color);
}
.copilotKitMessages::-webkit-scrollbar-track-piece:start {
  background: transparent;
}
.copilotKitMessages::-webkit-scrollbar-track-piece:end {
  background: transparent;
}
.copilotKitMessage {
  border-radius: 15px;
  padding: 8px 12px;
  font-size: 1rem;
  line-height: 1.5;
  overflow-wrap: break-word;
  max-width: 80%;
  margin-bottom: 0.5rem;
}
.copilotKitMessage.copilotKitUserMessage {
  background: var(--copilot-kit-primary-color);
  color: var(--copilot-kit-contrast-color);
  margin-left: auto;
  white-space: pre-wrap;
  line-height: 1.75;
  font-size: 1rem;
}
.copilotKitMessage.copilotKitAssistantMessage {
  background: transparent;
  margin-right: auto;
  padding-left: 0;
  position: relative;
  max-width: 100%;
  color: var(--copilot-kit-secondary-contrast-color);
}
.copilotKitMessage.copilotKitAssistantMessage .copilotKitMessageControls {
  position: absolute;
  left: 0;
  display: flex;
  gap: 1rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  padding: 5px 0 0 0;
}
.copilotKitMessageControls.currentMessage {
  opacity: 1 !important;
}
.copilotKitMessage.copilotKitAssistantMessage:hover .copilotKitMessageControls {
  opacity: 1;
}
@media (max-width: 768px) {
  .copilotKitMessage.copilotKitAssistantMessage .copilotKitMessageControls {
    opacity: 1;
  }
}
.copilotKitMessageControlButton {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  border-radius: 0.5rem;
  justify-content: center;
  color: var(--copilot-kit-primary-color);
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  z-index: 10;
  margin: 0;
  background: transparent;
  border: none;
}
.copilotKitMessageControlButton:hover {
  color: color-mix(in srgb, var(--copilot-kit-primary-color) 80%, black);
  transform: scale(1.05);
}
.copilotKitMessageControlButton:active {
  color: color-mix(in srgb, var(--copilot-kit-primary-color) 80%, black);
  transform: scale(1.05);
}
.copilotKitMessageControlButton svg {
  width: 1rem;
  height: 1rem;
  display: block;
  pointer-events: none;
}
.copilotKitMessage.copilotKitAssistantMessage + .copilotKitMessage.copilotKitUserMessage {
  margin-top: 1.5rem;
}
.copilotKitCustomAssistantMessage {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.copilotKitMessage .inProgressLabel {
  margin-left: 10px;
  opacity: 0.7;
}
@keyframes copilotKitSpinAnimation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.copilotKitSpinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--copilot-kit-contrast-color);
  border-radius: 50%;
  border-top-color: var(--copilot-kit-primary-color);
  animation: copilotKitSpinAnimation 1s linear infinite;
  margin-left: 8px;
}
@keyframes copilotKitActivityDotAnimation {
  0%, 80%, 100% {
    transform: scale(0.5);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
.copilotKitActivityDot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--copilot-kit-primary-color);
  animation: copilotKitActivityDotAnimation 1.4s infinite ease-in-out both;
}
.copilotKitImageRendering {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.copilotKitImageRenderingImage {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: var(--copilot-kit-shadow-sm);
}
.copilotKitImageRenderingContent {
  margin-top: 8px;
  padding: 0 16px;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--copilot-kit-secondary-contrast-color);
}
.copilotKitImageRenderingError {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  border: 1px solid var(--copilot-kit-separator-color);
  border-radius: 8px;
  background-color: var(--copilot-kit-input-background-color);
}
.copilotKitImageRenderingErrorMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--copilot-kit-error-background);
  border: 1px solid var(--copilot-kit-error-border);
  border-radius: 6px;
  color: var(--copilot-kit-error-text);
  font-size: 0.875rem;
  font-weight: 500;
}
.copilotKitImageRenderingErrorMessage::before {
  content: "\26a0\fe0f";
  font-size: 1rem;
}

/* src/css/window.css */
.copilotKitWindow {
  position: fixed;
  inset: 0px;
  transform-origin: bottom;
  border-color: rgb(229 231 235);
  background-color: var(--copilot-kit-background-color);
  border-radius: 0.75rem;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 5px 40px;
  flex-direction: column;
  transition: opacity 100ms ease-out, transform 200ms ease-out;
  opacity: 0;
  transform: scale(0.95) translateY(20px);
  display: flex;
  pointer-events: none;
}
.copilotKitSidebar .copilotKitWindow {
  border-radius: 0;
  opacity: 1;
  transform: translateX(100%);
}
.copilotKitWindow.open {
  opacity: 1;
  transform: scale(1) translateY(0);
  pointer-events: auto;
}
.copilotKitSidebar .copilotKitWindow.open {
  transform: translateX(0);
}
@media (min-width: 640px) {
  .copilotKitWindow {
    transform-origin: bottom right;
    bottom: 5rem;
    right: 1rem;
    top: auto;
    left: auto;
    border-width: 0px;
    margin-bottom: 1rem;
    width: 24rem;
    height: 600px;
    min-height: 200px;
    max-height: calc(100% - 6rem);
  }
  .copilotKitSidebar .copilotKitWindow {
    bottom: 0;
    right: 0;
    top: auto;
    left: auto;
    width: 28rem;
    min-height: 100%;
    margin-bottom: 0;
    max-height: none;
  }
}

/* src/css/animations.css */
.copilotKitActivityDot1 {
  animation: copilotKitActivityDotsAnimation 1.05s infinite;
}
.copilotKitActivityDot2 {
  animation-delay: 0.1s;
}
.copilotKitActivityDot3 {
  animation-delay: 0.2s;
}
@keyframes copilotKitActivityDotsAnimation {
  0%, 57.14% {
    animation-timing-function: cubic-bezier(0.33, 0.66, 0.66, 1);
    transform: translate(0);
  }
  28.57% {
    animation-timing-function: cubic-bezier(0.33, 0, 0.66, 0.33);
    transform: translateY(-6px);
  }
  100% {
    transform: translate(0);
  }
}
@keyframes copilotKitSpinAnimation {
  to {
    transform: rotate(360deg);
  }
}
@keyframes copilotKitPulseAnimation {
  50% {
    opacity: 0.5;
  }
}

/* src/css/markdown.css */
h1.copilotKitMarkdownElement,
h2.copilotKitMarkdownElement,
h3.copilotKitMarkdownElement,
h4.copilotKitMarkdownElement,
h5.copilotKitMarkdownElement,
h6.copilotKitMarkdownElement {
  font-weight: bold;
  line-height: 1.2;
}
h1.copilotKitMarkdownElement:not(:last-child),
h2.copilotKitMarkdownElement:not(:last-child),
h3.copilotKitMarkdownElement:not(:last-child),
h4.copilotKitMarkdownElement:not(:last-child),
h5.copilotKitMarkdownElement:not(:last-child),
h6.copilotKitMarkdownElement:not(:last-child) {
  margin-bottom: 1rem;
}
h1.copilotKitMarkdownElement {
  font-size: 1.5em;
}
h2.copilotKitMarkdownElement {
  font-size: 1.25em;
  font-weight: 600;
}
h3.copilotKitMarkdownElement {
  font-size: 1.1em;
}
h4.copilotKitMarkdownElement {
  font-size: 1em;
}
h5.copilotKitMarkdownElement {
  font-size: 0.9em;
}
h6.copilotKitMarkdownElement {
  font-size: 0.8em;
}
a.copilotKitMarkdownElement {
  color: blue;
  text-decoration: underline;
}
p.copilotKitMarkdownElement {
  padding: 0px;
  margin: 0px;
  line-height: 1.75;
  font-size: 1rem;
}
p.copilotKitMarkdownElement:not(:last-child),
pre.copilotKitMarkdownElement:not(:last-child),
ol.copilotKitMarkdownElement:not(:last-child),
ul.copilotKitMarkdownElement:not(:last-child),
blockquote.copilotKitMarkdownElement:not(:last-child) {
  margin-bottom: 1.25em;
}
blockquote.copilotKitMarkdownElement {
  border-color: rgb(142, 142, 160);
  border-left-width: 2px;
  border-left-style: solid;
  line-height: 1.2;
  padding-left: 10px;
}
blockquote.copilotKitMarkdownElement p {
  padding: 0.7em 0;
}
ul.copilotKitMarkdownElement {
  list-style-type: disc;
  padding-left: 20px;
  overflow: visible;
}
li.copilotKitMarkdownElement {
  list-style-type: inherit;
  list-style-position: outside;
  margin-left: 0;
  padding-left: 0;
  position: relative;
  overflow: visible;
}
.copilotKitCodeBlock {
  position: relative;
  width: 100%;
  background-color: rgb(9 9 11);
  border-radius: 0.375rem;
}
.copilotKitCodeBlockToolbar {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  background-color: rgb(39 39 42);
  padding-left: 1rem;
  padding-top: 0.09rem;
  padding-bottom: 0.09rem;
  color: rgb(228, 228, 228);
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  font-family: sans-serif;
}
.copilotKitCodeBlockToolbarLanguage {
  font-size: 0.75rem;
  line-height: 1rem;
  text-transform: lowercase;
}
.copilotKitCodeBlockToolbarButtons {
  display: flex;
  align-items: center;
  margin-right: 0.25rem;
  margin-left: 0.25rem;
}
.copilotKitCodeBlockToolbarButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  height: 2.5rem;
  padding: 3px;
  margin: 2px;
}
.copilotKitCodeBlockToolbarButton:hover {
  background-color: rgb(55, 55, 58);
}
.copilotKitInlineCode {
  background-color: var(--copilot-kit-input-background-color);
  border: 1px solid var(--copilot-kit-separator-color);
  border-radius: 0.375rem;
  padding: 0.05rem 0.4rem;
  font-size: 15px;
}

/* src/css/suggestions.css */
.copilotKitMessages footer .suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}
.copilotKitMessages footer h6 {
  font-weight: 500;
  font-size: 0.7rem;
  margin-bottom: 8px;
}
.copilotKitMessages footer .suggestions .suggestion {
  padding: 6px 10px;
  font-size: 0.7rem;
  border-radius: 15px;
  border: 1px solid var(--copilot-kit-muted-color);
  color: var(--copilot-kit-secondary-contrast-color);
  box-shadow: 0 5px 5px 0px rgba(0, 0, 0, .01), 0 2px 3px 0px rgba(0, 0, 0, .02);
}
.copilotKitMessages footer .suggestions .suggestion.loading {
  padding: 0;
  font-size: 0.7rem;
  border: none;
  color: var(--copilot-kit-secondary-contrast-color);
}
.copilotKitMessages footer .suggestions button {
  transition: transform 0.3s ease;
}
.copilotKitMessages footer .suggestions button:not(:disabled):hover {
  transform: scale(1.03);
}
.copilotKitMessages footer .suggestions button:disabled {
  cursor: wait;
}
.copilotKitMessages footer .suggestions button svg {
  margin-right: 6px;
}

/* src/css/panel.css */
.copilotKitChat {
  z-index: 30;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  background: var(--copilot-kit-background-color);
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  touch-action: manipulation;
  display: flex;
  flex-direction: column;
}
.copilotKitChat svg {
  display: inline-block;
  vertical-align: middle;
}
.copilotKitChat .copilotKitMessages {
  flex-grow: 1;
}

/* src/css/console.css */
.copilotKitDevConsole {
  display: flex;
  align-items: center;
  gap: 5px;
  margin: 0 15px;
}
.copilotKitDevConsole.copilotKitDevConsoleWarnOutdated {
  background-color: var(--copilot-kit-dev-console-bg);
}
.copilotKitDevConsole .copilotKitVersionInfo {
  display: flex;
  position: absolute;
  bottom: -25px;
  padding: 3px 5px;
  left: 0;
  width: 100%;
  justify-content: center;
  gap: 10px;
  font-size: 0.8rem;
  align-items: center;
  background: #ebb305;
}
.copilotKitDevConsole .copilotKitVersionInfo button {
  font-size: 11px;
  font-weight: normal;
  font-family: monospace;
  background-color: var(--copilot-kit-dev-console-bg);
  border: 1px solid #979797;
  padding: 1px 12px;
  padding-left: 5px;
  border-radius: 4px;
  display: inline-block;
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  width: 260px;
  text-overflow: ellipsis;
}
.copilotKitDevConsole .copilotKitVersionInfo aside {
  display: inline;
  font-weight: normal;
  color: #7f7a7a;
  margin-left: 5px;
}
.copilotKitDevConsole .copilotKitVersionInfo svg {
  margin-left: 3px;
  margin-top: -3px;
}
.copilotKitDevConsole .copilotKitDebugMenuTriggerButton {
  font-size: 11px;
  font-weight: bold;
  display: flex;
  padding: 0 10px;
  height: 30px;
  background-color: transparent;
  border: 1px solid var(--copilot-kit-muted-color);
  border-radius: 20px;
  align-items: center;
  justify-content: center;
  outline: none;
}
.copilotKitDebugMenuTriggerButton.compact {
  width: 35px;
  color: var(--copilot-kit-dev-console-bg);
  justify-content: center;
  outline: none;
  font-size: 8px;
}
.copilotKitDevConsole .copilotKitDebugMenuTriggerButton:hover {
  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 85%, black);
  color: var(--copilot-kit-dev-console-text);
}
.dark,
html.dark,
body.dark,
[data-theme=dark],
html[style*="color-scheme: dark"],
body[style*="color-scheme: dark"] .copilotKitDevConsole .copilotKitDebugMenuTriggerButton {
  color: white;
}
.dark,
html.dark,
body.dark,
[data-theme=dark],
html[style*="color-scheme: dark"],
body[style*="color-scheme: dark"] .copilotKitDevConsole .copilotKitDebugMenuTriggerButton:hover {
  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 20%, black);
}
.copilotKitDevConsole .copilotKitDebugMenuTriggerButton > svg {
  margin-left: 10px;
}
.copilotKitDebugMenu {
  --copilot-kit-dev-console-border: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 80%, black);
  margin-top: 2px;
  border-radius: 6px;
  background-color: var(--copilot-kit-dev-console-bg);
  border: 1px solid var(--copilot-kit-dev-console-border);
  padding: 0.25rem;
  outline: none;
  font-size: 13px;
}
.copilotKitDebugMenuItem {
  padding-top: 3px;
  padding-bottom: 3px;
  padding-left: 10px;
  padding-right: 10px;
  display: block;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--copilot-kit-dev-console-text);
}
.copilotKitDebugMenuItem:hover {
  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 95%, black);
  border-radius: 4px;
}
.copilotKitDebugMenu[data-closed] {
  transform: scale(0.95);
  opacity: 0;
}
.copilotKitDebugMenu hr {
  height: 1px;
  border: none;
  background-color: var(--copilot-kit-dev-console-border);
  margin: 0.25rem;
}
.copilotKitHelpModal {
  background-color: var(--copilot-kit-dev-console-bg);
  color: var(--copilot-kit-dev-console-text);
}
.copilotKitHelpItemButton {
  display: block;
  text-align: center;
  width: 100%;
  padding: 4px 6px;
  border-radius: 15px;
  font-size: 0.8rem;
  border: 1px solid var(--copilot-kit-muted-color);
  color: var(--copilot-kit-dev-console-text);
  box-shadow: 0 5px 5px 0px rgba(0, 0, 0, .01), 0 2px 3px 0px rgba(0, 0, 0, .02);
  background-color: var(--copilot-kit-dev-console-bg);
}
.copilotKitHelpItemButton:hover {
  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 95%, black);
}

/* src/css/crew.css */
.copilotkit-response {
  text-align: right;
}
.copilotkit-response-content {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  background-color: #f9fafb;
  padding: 0.5rem;
  border-radius: 0.25rem;
  text-align: left;
}
.copilotkit-response-actions {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-end;
}
.copilotkit-response-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
}
.copilotkit-toggle-button {
  margin-right: 0.25rem;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.copilotkit-icon {
  height: 0.75rem;
  width: 0.75rem;
  color: #6b7280;
}
.copilotkit-response-buttons {
  display: flex;
  gap: 0.5rem;
}
.copilotkit-response-button {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border-radius: 0.25rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}
.copilotkit-response-button:hover {
  background-color: #e5e7eb;
}
.copilotkit-response-button:focus {
  outline: none;
}
.copilotkit-response-completed-feedback {
  background-color: #f9fafb;
  padding: 0.5rem;
  border-radius: 0.375rem;
  display: inline-flex;
  align-items: center;
}
.copilotkit-response-completed-feedback span {
  color: #4b5563;
  font-size: 0.75rem;
  font-weight: 500;
}
.copilotkit-state {
  font-size: 0.875rem;
  margin-bottom: 1rem;
}
.copilotkit-state-header {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  cursor: pointer;
  user-select: none;
  margin-bottom: 0.25rem;
}
.copilotkit-state-label {
  color: #4b5563;
  font-size: 0.875rem;
}
.copilotkit-state-label-loading {
  display: inline-flex;
  align-items: center;
  animation: pulse 1.5s infinite;
}
.copilotkit-state-content {
  padding-left: 1rem;
  max-height: 250px;
  overflow: auto;
  padding-top: 0.375rem;
  border-left: 1px solid #e5e7eb;
  margin-left: 0.375rem;
}
.copilotkit-state-item {
  padding: 0.25rem 0;
  margin-bottom: 0.25rem;
  transition: all 0.3s ease;
}
.copilotkit-state-item-newest {
  animation: appear 0.5s ease-out;
}
.copilotkit-state-item-header {
  font-size: 0.75rem;
  opacity: 0.7;
}
.copilotkit-state-item-thought {
  margin-top: 0.125rem;
  font-size: 0.75rem;
  opacity: 0.8;
}
.copilotkit-state-item-result {
  margin-top: 0.125rem;
  font-size: 0.75rem;
}
.copilotkit-state-item-description {
  margin-top: 0.125rem;
  font-size: 0.75rem;
  opacity: 0.8;
}
.copilotkit-state-empty {
  padding: 0.25rem 0;
  font-size: 0.75rem;
  opacity: 0.7;
}
.copilotkit-skeleton {
  padding: 0.125rem 0;
  animation: pulse 1.5s infinite;
}
.copilotkit-skeleton-header {
  display: flex;
  justify-content: space-between;
}
.copilotkit-skeleton-title {
  height: 0.625rem;
  width: 4rem;
  background-color: #e5e7eb;
  border-radius: 0.25rem;
}
.copilotkit-skeleton-subtitle {
  height: 0.5rem;
  width: 2rem;
  background-color: #e5e7eb;
  border-radius: 0.25rem;
}
.copilotkit-skeleton-content {
  margin-top: 0.125rem;
  height: 1.5rem;
  background-color: #e5e7eb;
  border-radius: 0.25rem;
}
.copilotkit-loader {
  animation: spin 1.5s linear infinite;
}
.copilotkit-spinner {
  animation: spin 1.5s linear infinite;
}
@keyframes appear {
  0% {
    opacity: 0;
    transform: translateY(8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes pulse {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media (prefers-color-scheme: dark) {
  .copilotkit-response-content {
    color: #9ca3af;
    background-color: #1f2937;
  }
  .copilotkit-response-label {
    color: #9ca3af;
  }
  .copilotkit-icon {
    color: #9ca3af;
  }
  .copilotkit-response-button {
    background-color: #1f2937;
    color: #d1d5db;
  }
  .copilotkit-response-button:hover {
    background-color: #374151;
  }
  .copilotkit-response-completed-feedback {
    background-color: #1f2937;
  }
  .copilotkit-response-completed-feedback span {
    color: #e5e7eb;
  }
  .copilotkit-state-label {
    color: #d1d5db;
  }
  .copilotkit-state-content {
    border-left-color: #374151;
  }
  .copilotkit-skeleton-title,
  .copilotkit-skeleton-subtitle,
  .copilotkit-skeleton-content {
    background-color: #374151;
  }
}

/* src/styles.css */
/*# sourceMappingURL=index.css.map */
