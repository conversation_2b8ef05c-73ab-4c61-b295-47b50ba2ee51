{"name": "agent_js", "version": "1.0.0", "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/html-to-text": "^9.0.4", "@types/node": "^22.9.0", "typescript": "^5.6.3"}, "dependencies": {"@copilotkit/sdk-js": "^1.10.4", "@langchain/anthropic": "^0.3.8", "@langchain/core": "^0.3.18", "@langchain/google-genai": "^0.1.4", "@langchain/langgraph": "^0.2.57", "@langchain/langgraph-checkpoint": "^0.0.16", "@langchain/openai": "^0.3.14", "@types/uuid": "^10.0.0", "uuid": "^13.0.0", "zod": "^3.23.8"}, "pnpm": {"overrides": {"@langchain/community@<0.3.3": ">=0.3.3"}}}