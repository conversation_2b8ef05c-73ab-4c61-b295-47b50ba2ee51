/**
 * Restaurant Intelligence Agent
 *
 * This agent specializes in restaurant analytics and business intelligence,
 * providing comprehensive reports for location analysis, competitor benchmarking,
 * menu optimization, sales forecasting, customer segmentation, and franchise expansion.
 */

import { z } from "zod";
import { RunnableConfig } from "@langchain/core/runnables";
import { tool } from "@langchain/core/tools";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { AIMessage, SystemMessage } from "@langchain/core/messages";
import { MemorySaver, START, StateGraph } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { convertActionsToDynamicStructuredTools } from "@copilotkit/sdk-js/langgraph";
import { BaseMessage } from "@langchain/core/messages";
import { Annotation } from "@langchain/langgraph";
import {
  generateLocationAnalysis,
  generateCompetitorBenchmarking,
  generateMenuOptimization,
  generateSalesForecasting,
  generateCustomerSegmentation,
  generateFranchiseExpansion,
} from "./restaurant-intelligence-server.js";

// 1. Define our agent state for Restaurant Intelligence
const AgentStateAnnotation = Annotation.Root({
  // Define a 'messages' channel to store an array of BaseMessage objects
  messages: Annotation<BaseMessage[]>({
    // Reducer function: Combines the current state with new messages
    reducer: (currentState, updateValue) => currentState.concat(updateValue),
    // Default function: Initialize the channel with an empty array
    default: () => [],
  }),
  tools: Annotation<any[]>, // ag-ui tools will be added here
});

// 2. Define the type for our agent state
export type AgentState = typeof AgentStateAnnotation.State;

// 3. Define restaurant intelligence tools
const locationAnalysisTool = tool(
  async ({ location, conceptType = "Italian Restaurant", analysisRadius = "1 mile" }) => {
    return generateLocationAnalysis(location, conceptType, analysisRadius);
  },
  {
    name: "generate_location_analysis",
    description: "Generate a location analysis report for restaurant site selection",
    schema: z.object({
      location: z.string().describe("Target location for analysis (e.g., 'Downtown Seattle', 'Brooklyn Heights')"),
      conceptType: z.string().optional().describe("Type of restaurant concept (e.g., 'Italian Restaurant', 'Fast Casual')"),
      analysisRadius: z.string().optional().describe("Radius for competitive analysis (e.g., '0.5 mile', '1 mile')"),
    }),
  }
);

const competitorBenchmarkingTool = tool(
  async ({ marketArea, competitorFocus = "Direct Competitors", analysisPeriod = "Last 6 months" }) => {
    return generateCompetitorBenchmarking(marketArea, competitorFocus, analysisPeriod);
  },
  {
    name: "generate_competitor_benchmarking",
    description: "Generate a competitor benchmarking report comparing performance against market competitors",
    schema: z.object({
      marketArea: z.string().describe("Geographic market for analysis (e.g., 'Manhattan', 'Silicon Valley')"),
      competitorFocus: z.string().optional().describe("Type of competitors to analyze ('Direct Competitors', 'All Competitors', 'Category Leaders')"),
      analysisPeriod: z.string().optional().describe("Time period for analysis ('Last 3 months', 'Last 6 months', 'Last year')"),
    }),
  }
);

const menuOptimizationTool = tool(
  async ({ menuFocus = "Profitability", analysisType = "Full Menu", optimizationGoal = "Increase Profit Margins" }) => {
    return generateMenuOptimization(menuFocus, analysisType, optimizationGoal);
  },
  {
    name: "generate_menu_optimization",
    description: "Generate a menu optimization report analyzing menu performance and recommendations",
    schema: z.object({
      menuFocus: z.string().optional().describe("Primary optimization focus ('Profitability', 'Popularity', 'Both')"),
      analysisType: z.string().optional().describe("Scope of analysis ('Full Menu', 'Entrees Only', 'Top Performers')"),
      optimizationGoal: z.string().optional().describe("Primary goal ('Increase Profit Margins', 'Boost Sales', 'Reduce Costs')"),
    }),
  }
);

const salesForecastingTool = tool(
  async ({ forecastPeriod = "Next 3 months", forecastType = "Revenue", seasonalAdjustment = "Include Seasonality" }) => {
    return generateSalesForecasting(forecastPeriod, forecastType, seasonalAdjustment);
  },
  {
    name: "generate_sales_forecasting",
    description: "Generate a sales forecasting report with predictive analytics",
    schema: z.object({
      forecastPeriod: z.string().optional().describe("Time period to forecast ('Next 3 months', 'Next 6 months', 'Next year')"),
      forecastType: z.string().optional().describe("Type of forecast ('Revenue', 'Customer Count', 'Both')"),
      seasonalAdjustment: z.string().optional().describe("Whether to include seasonal factors ('Include Seasonality', 'Exclude Seasonality')"),
    }),
  }
);

const customerSegmentationTool = tool(
  async ({ segmentationCriteria = "RFM Analysis", customerPeriod = "Last 12 months", segmentFocus = "All Customers" }) => {
    return generateCustomerSegmentation(segmentationCriteria, customerPeriod, segmentFocus);
  },
  {
    name: "generate_customer_segmentation",
    description: "Generate a customer segmentation report analyzing customer behavior patterns",
    schema: z.object({
      segmentationCriteria: z.string().optional().describe("Method for segmentation ('RFM Analysis', 'Demographic', 'Behavioral')"),
      customerPeriod: z.string().optional().describe("Time period for analysis ('Last 6 months', 'Last 12 months', 'All time')"),
      segmentFocus: z.string().optional().describe("Customer subset to analyze ('All Customers', 'Active Only', 'High Value')"),
    }),
  }
);

const franchiseExpansionTool = tool(
  async ({ targetMarket, expansionTimeline = "18 months", investmentBudget = "500000" }) => {
    return generateFranchiseExpansion(targetMarket, expansionTimeline, investmentBudget);
  },
  {
    name: "generate_franchise_expansion",
    description: "Generate a franchise expansion feasibility report for new market entry",
    schema: z.object({
      targetMarket: z.string().describe("Target market for expansion (e.g., 'Austin, TX', 'Portland, OR')"),
      expansionTimeline: z.string().optional().describe("Timeline for expansion ('12 months', '18 months', '24 months')"),
      investmentBudget: z.string().optional().describe("Available investment budget ('250000', '500000', '1000000')"),
    }),
  }
);

// 4. Put our restaurant intelligence tools into an array
const tools = [
  locationAnalysisTool,
  competitorBenchmarkingTool,
  menuOptimizationTool,
  salesForecastingTool,
  customerSegmentationTool,
  franchiseExpansionTool,
];

// 5. Define the chat node, which will handle the restaurant intelligence logic
async function chat_node(state: AgentState, config: RunnableConfig) {
  // 5.1 Define the model, lower temperature for more consistent business analysis
  const model = new ChatOpenAI({ temperature: 0.1, model: "gpt-4o" });

  // 5.2 Bind the tools to the model, include CopilotKit actions
  const modelWithTools = model.bindTools!(
    [
      ...convertActionsToDynamicStructuredTools(state.tools || []),
      ...tools,
    ],
  );

  // 5.3 Define the specialized system message for restaurant intelligence
  const systemMessage = new SystemMessage({
    content: `You are a Restaurant Intelligence Analyst specializing in data-driven business insights for restaurants.

You have access to powerful analytics tools that can generate comprehensive reports for:
- Location Analysis: Site selection and trade area analysis
- Competitor Benchmarking: Market positioning and competitive intelligence
- Menu Optimization: Profit and popularity analysis with strategic recommendations
- Sales Forecasting: Predictive revenue modeling with seasonal adjustments
- Customer Segmentation: Behavioral analysis and targeting strategies
- Franchise Expansion: Market feasibility and financial projections

Your approach should be:

1. **Consultative**: Ask clarifying questions to understand the user's specific business needs
2. **Strategic**: Focus on actionable insights that drive business decisions
3. **Comprehensive**: Use multiple analysis types when they provide complementary insights
4. **Professional**: Present findings in a clear, business-focused manner

When generating reports:
- Always explain the business context and implications
- Highlight the most critical findings and recommendations
- Suggest follow-up analyses that could provide additional value
- Present data in a way that supports strategic decision-making

Always focus on delivering actionable business intelligence that helps restaurant operators make informed decisions.`,
  });

  // 5.4 Invoke the model with the system message and the messages in the state
  const response = await modelWithTools.invoke(
    [systemMessage, ...state.messages],
    config
  );

  // 5.5 Return the response, which will be added to the state
  return {
    messages: response,
  };
}

// 6. Define the function that determines whether to continue or not
function shouldContinue({ messages, tools }: AgentState) {
  // 6.1 Get the last message from the state
  const lastMessage = messages[messages.length - 1] as AIMessage;

  // 6.2 If the LLM makes a tool call, then we route to the "tools" node
  if (lastMessage.tool_calls?.length) {
    const toolCallName = lastMessage.tool_calls![0].name;

    // 6.3 Only route to the tool node if the tool call is not a CopilotKit action
    if (!tools || tools.every((tool) => tool.name !== toolCallName)) {
      return "tool_node"
    }
  }

  // 6.4 Otherwise, we stop (reply to the user) using the special "__end__" node
  return "__end__";
}

// Define the restaurant intelligence workflow graph
const workflow = new StateGraph(AgentStateAnnotation)
  .addNode("chat_node", chat_node)
  .addNode("tool_node", new ToolNode(tools))
  .addEdge(START, "chat_node")
  .addEdge("tool_node", "chat_node")
  .addConditionalEdges("chat_node", shouldContinue as any);

const memory = new MemorySaver();

export const graph = workflow.compile({
  checkpointer: memory,
});
