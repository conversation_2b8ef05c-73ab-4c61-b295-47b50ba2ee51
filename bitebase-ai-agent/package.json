{"name": "bitebase-agent", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:ui\" \"npm run dev:agent\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:studio": "concurrently \"npm run dev:ui\" \"npm run dev:agent:studio\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:agent": "cd agent && npx @langchain/langgraph-cli dev --port 8123 --no-browser", "dev:agent:studio": "cd agent && npx @langchain/langgraph-cli dev --port 8123", "dev:ui": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ag-ui/langgraph": "0.0.7", "@copilotkit/react-core": "^1.10.4", "@copilotkit/react-textarea": "^1.10.4", "@copilotkit/react-ui": "^1.10.4", "@copilotkit/runtime": "^1.10.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@types/leaflet": "^1.9.16", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "leaflet": "^1.9.4", "leaflet-defaulticon-compatibility": "^0.1.2", "lucide-react": "^0.474.0", "next": "^15.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "react-markdown": "^9.0.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^13.0.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@langchain/langgraph-cli": "0.0.40", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "cursor-tools": "latest", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}