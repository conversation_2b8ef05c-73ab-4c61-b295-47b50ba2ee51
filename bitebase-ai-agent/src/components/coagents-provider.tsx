"use client";
import { useCoAgent } from "@copilotkit/react-core";
import { createContext, useContext, useRef } from "react";
import { AvailableAgents } from "@/lib/available-agents";
import { ResearchAgentState } from "./agents/researcher";
import { MCPAgentState } from "./agents/mcp-agent";
import { MCP_STORAGE_KEY, ServerConfig } from "@/lib/mcp-config-types";
import { useLocalStorage } from "@/hooks/use-local-storage";

/**
 * Base Agent State
 */
export type BaseAgentState = {
  __name__: AvailableAgents;
};

/**
 * Restaurant Intelligence Agent Types
 */
export type RestaurantData = {
  id: string;
  name: string;
  address: string;
  pos_system: string;
  monthly_revenue: number;
  customer_count: number;
  avg_order_value: number;
};

export type LocationAnalysis = {
  id: string;
  address: string;
  suitability_score: number;
  foot_traffic: number;
  competition_density: number;
  demographic_score: number;
};

export type MenuItem = {
  id: string;
  name: string;
  price: number;
  popularity_score: number;
  profit_margin: number;
  category: string;
};

export type AnalysisProgress = {
  task: string;
  progress: number;
  completed: boolean;
};

export type RestaurantIntelligenceState = BaseAgentState & {
  restaurants: RestaurantData[];
  selected_restaurant_id: string | null;
  location_analyses: LocationAnalysis[];
  menu_items: MenuItem[];
  analysis_progress?: AnalysisProgress[];
  current_report?: string;
};

/**
 * Research Agent Types
 */
export interface Section {
  title: string;
  content: string;
  idx: number;
  footer?: string;
  id: string;
}

export interface Source {
  content: string;
  published_date: string;
  score: number;
  title: string;
  url: string;
}
export type Sources = Record<string, Source>;

export interface Log {
  message: string;
  done: boolean;
}

export const AgentsContext = createContext<
  Array<RestaurantIntelligenceState | ResearchAgentState | MCPAgentState>
>([]);

/**
 * This provider wraps state from all agents
 */
export const CoAgentsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  // Use ref to avoid re-rendering issues
  const configsRef = useRef<Record<string, ServerConfig>>({});
  
  // Get saved MCP configurations from localStorage
  const [savedConfigs] = useLocalStorage<Record<string, ServerConfig>>(MCP_STORAGE_KEY, {});
  
  // Set the ref value once we have the saved configs
  if (Object.keys(savedConfigs).length > 0 && Object.keys(configsRef.current).length === 0) {
    configsRef.current = savedConfigs;
  }

  const { state: restaurantIntelligenceState } = useCoAgent({
    name: AvailableAgents.RESTAURANT_INTELLIGENCE,
    initialState: {
      restaurants: [],
      selected_restaurant_id: null,
      location_analyses: [],
      menu_items: [],
      analysis_progress: [],
      current_report: "",
    },
  });

  const { state: aiResearchAgentState } = useCoAgent({
    name: AvailableAgents.RESEARCH_AGENT,
    initialState: {
      model: "openai",
      research_question: "",
      resources: [],
      report: "",
      logs: [],
    },
  });

  const { state: mcpAgentState } = useCoAgent({
    name: AvailableAgents.MCP_AGENT,
    initialState: {
      response: "",
      logs: [],
      mcp_config: configsRef.current,
    },
  });

  return (
    <AgentsContext.Provider
      value={[
        {
          ...restaurantIntelligenceState,
          __name__: AvailableAgents.RESTAURANT_INTELLIGENCE,
        },
        {
          ...aiResearchAgentState,
          __name__: AvailableAgents.RESEARCH_AGENT,
        },
        {
          ...mcpAgentState,
          __name__: AvailableAgents.MCP_AGENT,
        },
      ]}
    >
      {children}
    </AgentsContext.Provider>
  );
};

export const useCoAgents = () => {
  const context = useContext(AgentsContext);
  if (!context) {
    throw new Error("useAgents must be used within an AgentsProvider");
  }
  return context;
};