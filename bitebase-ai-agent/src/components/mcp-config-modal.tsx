import { FC } from "react";

interface MCPConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MCPConfigModal: FC<MCPConfigModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">MCP Server Configuration</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>
        <p className="text-gray-600">
          MCP server configuration will be available in a future update.
        </p>
      </div>
    </div>
  );
};