import { FC } from "react";

interface ReportViewProps {
  reportData: any;
}

export const ReportView: FC<ReportViewProps> = ({ reportData }) => {
  if (!reportData) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold mb-4">Restaurant Intelligence Report</h3>
      <pre className="bg-gray-50 p-4 rounded text-sm overflow-auto">
        {JSON.stringify(reportData, null, 2)}
      </pre>
    </div>
  );
};