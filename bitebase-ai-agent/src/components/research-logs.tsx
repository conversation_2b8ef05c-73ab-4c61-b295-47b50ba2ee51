import { FC } from "react";

interface ResearchLogsProps {
  logs: Array<{
    message: string;
    done: boolean;
  }>;
}

export const ResearchLogs: FC<ResearchLogsProps> = ({ logs }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold mb-4">Research Progress</h3>
      <div className="space-y-2">
        {logs.map((log, index) => (
          <div
            key={index}
            className={`text-sm ${
              log.done ? "text-green-600" : "text-gray-600"
            }`}
          >
            {log.done && "✓ "}
            {log.message}
          </div>
        ))}
      </div>
    </div>
  );
};