# BiteBase AI - Restaurant Intelligence Setup Guide

## 🍽️ Restaurant Intelligence Multi-Agent System

A comprehensive restaurant analytics platform powered by LangGraph, CopilotKit, and advanced AI agents specializing in:

- **PLACE Intelligence**: Location analysis, competitor mapping, market penetration
- **PRODUCT Intelligence**: Menu engineering, performance optimization, BCG matrix analysis  
- **PRICE Intelligence**: Dynamic pricing, revenue forecasting, financial analysis
- **PROMOTION Intelligence**: Customer segmentation, campaign ROI, marketing analytics

## 📋 Prerequisites

- Node.js 18+ and npm/pnpm
- OpenAI API key or GitHub Copilot API access
- Mapbox API key (for map visualizations)

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Frontend dependencies
cd bitebase_agent
npm install

# Agent backend dependencies  
cd agent
npm install
```

### 2. Environment Configuration

Create `agent/.env`:
```env
OPENAI_API_KEY=your_openai_api_key_here
LANGSMITH_API_KEY=your_langsmith_key_here  # Optional
```

Create `bitebase_agent/.env.local`:
```env
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here
NEXT_PUBLIC_CPK_PUBLIC_API_KEY=your_copilotkit_api_key_here  # Optional
```

### 3. Development Mode

```bash
# Run both frontend and backend concurrently
npm run dev

# Or run separately:
# Terminal 1 - Frontend (http://localhost:3000)
npm run dev:ui

# Terminal 2 - Agent Backend (http://localhost:8123) 
npm run dev:agent
```

### 4. Production Build

```bash
npm run build
npm start
```

## 🏗️ Architecture Overview

### Frontend Stack
- **Framework**: Next.js 15 with React 19
- **State Management**: Zustand + CopilotKit useCoAgent
- **UI Components**: TailwindCSS with custom restaurant-focused designs
- **Charts**: Nivo.js for analytics visualizations
- **Maps**: React-Map-GL with Deck.GL for geospatial analysis

### Backend Stack  
- **Agent Framework**: LangGraph with OpenAI GPT-4
- **Integration**: CopilotKit SDK for frontend-agent communication
- **Tools**: 8 specialized restaurant intelligence tools
- **Architecture**: Multi-agent routing with domain specialization

## 🧠 AI Agent Capabilities

### Place Intelligence Agent
```typescript
// Location Performance Analysis
analyzeLocationPerformance({
  location: "downtown_chicago"
})

// Competitor Benchmarking
generateCompetitorBenchmark({
  radius: 2.0 // miles
})
```

### Product Intelligence Agent  
```typescript
// Menu Engineering Analysis
analyzeMenuPerformance({
  timeframe: "month"
})

// Dynamic Pricing Optimization
optimizeMenuPricing({
  itemName: "Truffle Pasta",
  currentPrice: 24.99
})
```

### Price Intelligence Agent
```typescript
// Revenue Forecasting
forecastRevenue({
  timeframe: "quarter",
  scenario: "optimistic"
})

// Break-Even Analysis
analyzeBreakEven({
  fixedCosts: 15000,
  variableCostPerUnit: 8.50,
  avgSellingPrice: 18.75
})
```

### Promotion Intelligence Agent
```typescript
// Customer Segmentation (RFM Analysis)
analyzeCustomerSegments({
  segmentationType: "rfm"
})

// Campaign ROI Analysis
evaluateCampaignROI({
  campaignName: "Summer Special",
  spent: 5000,
  revenue: 12500,
  newCustomers: 85
})
```

## 🎯 Key Features

### 📊 Advanced Analytics Dashboard
- **Real-time Metrics**: Live revenue, customer, and performance tracking
- **Interactive Maps**: Customer density, competitor analysis, delivery zones
- **BCG Menu Matrix**: Star/Dog/Puzzle/Plowhorse classification
- **RFM Segmentation**: Customer lifetime value and retention analysis

### 🤖 AI-Powered Insights
- **Natural Language Queries**: Ask questions in plain English
- **Automated Analysis**: AI suggests optimizations and identifies trends
- **Contextual Recommendations**: Domain-specific business intelligence
- **Cross-Domain Integration**: Holistic restaurant intelligence

### 🗺️ Geospatial Intelligence
- **Heat Maps**: Customer density and ordering patterns
- **Competitor Mapping**: Real-time competitive landscape analysis
- **Delivery Optimization**: Route efficiency and coverage analysis
- **Site Selection**: Data-driven location expansion planning

## 🔧 Configuration Options

### Agent Routing Configuration
The system automatically routes queries to appropriate intelligence domains:

```typescript
// Place Intelligence triggers
"location", "competitor", "market", "delivery", "coverage"

// Product Intelligence triggers  
"menu", "item", "recipe", "popularity", "profitability"

// Price Intelligence triggers
"price", "revenue", "forecast", "margin", "cost"

// Promotion Intelligence triggers
"customer", "campaign", "segment", "marketing", "retention"
```

### Frontend Modules
Each intelligence domain has a dedicated frontend module:

- `src/features/place/` - Location and competitor analysis
- `src/features/product/` - Menu engineering and optimization
- `src/features/price/` - Financial analysis and forecasting
- `src/features/promotion/` - Customer analytics and campaigns

## 🧪 Testing

### Manual Testing Scenarios

1. **Place Intelligence**
   ```
   "Analyze the competition within 2 miles of our downtown location"
   "What's the customer density in our delivery area?"
   ```

2. **Product Intelligence**
   ```
   "Show me our menu performance using the BCG matrix"
   "What's the optimal price for our signature pasta dish?"
   ```

3. **Price Intelligence**
   ```
   "Forecast our revenue for next quarter assuming 10% growth"
   "Calculate our break-even point for the new location"
   ```

4. **Promotion Intelligence**
   ```
   "Segment our customers using RFM analysis"
   "Evaluate the ROI of our last marketing campaign"
   ```

### CopilotKit Integration Testing

The system includes frontend actions that integrate with the AI agent:

- `switchAnalyticsModule()` - Navigate between intelligence domains
- `toggleMapLayer()` - Control map visualizations
- `selectRestaurantLocation()` - Focus on specific locations  
- `analyzeMarketArea()` - Trigger comprehensive market analysis

## 📈 Performance Optimization

### Frontend Performance
- **Lazy Loading**: Analytics modules loaded on demand
- **Memoization**: Chart components optimized for re-renders
- **State Management**: Zustand for efficient state updates
- **Map Optimization**: Deck.GL for high-performance geospatial rendering

### Agent Performance
- **Tool Caching**: Restaurant data cached for repeated queries
- **Streaming Responses**: Real-time insights as analysis completes
- **Context Optimization**: Domain-specific system prompts
- **Memory Management**: LangGraph checkpointing for session persistence

## 🚨 Troubleshooting

### Common Issues

1. **Agent Connection Failed**
   - Verify OpenAI API key in `agent/.env`
   - Check that agent server is running on port 8123
   - Ensure firewall allows localhost connections

2. **Map Not Loading**
   - Verify Mapbox token in `.env.local`
   - Check browser console for API errors
   - Confirm token has required scopes

3. **CopilotKit Integration Issues**
   - Verify CopilotKit API key configuration
   - Check network requests in browser dev tools
   - Ensure agent exports graph correctly

### Development Tips

- Use `npm run dev:studio` for LangGraph Studio debugging
- Monitor agent logs for tool execution details
- Use browser dev tools to inspect CopilotKit state
- Check Zustand store for frontend state issues

## 📚 Additional Resources

- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [CopilotKit Integration Guide](https://docs.copilotkit.ai/)
- [Next.js 15 App Router](https://nextjs.org/docs)
- [Restaurant Analytics Best Practices](./docs/analytics-guide.md)

## 🎊 Success Validation

When properly configured, you should see:

✅ Frontend loads at http://localhost:3000  
✅ Agent API responds at http://localhost:8123  
✅ Map renders with sample restaurant data  
✅ AI chat interface accepts natural language queries  
✅ Analytics modules switch seamlessly  
✅ Charts display realistic restaurant metrics  
✅ AI provides domain-specific restaurant insights

---

*BiteBase AI - Transforming restaurant operations through intelligent analytics*