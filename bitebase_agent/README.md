# 🍽️ BiteBase - AI-Powered Restaurant Market Research Dashboard

BiteBase is a sophisticated, AI-powered market research dashboard specifically designed for the restaurant industry. Built with the advanced architecture outlined in the strategic blueprint, it provides comprehensive analytics across the 4P framework: **Place**, **Product**, **Price**, and **Promotion**.

## 🚀 Key Features

### 🗺️ **Advanced Geospatial Analytics**
- **High-Performance Mapping**: Mapbox GL JS + Deck.gl for GPU-accelerated visualizations
- **Interactive Market Intelligence**: Real-time competitor analysis and customer density mapping
- **Statistical Hotspot Analysis**: Getis-Ord Gi* algorithm for statistically significant spatial clusters
- **Dynamic Heatmaps**: Customer distribution and order value visualization

### 🤖 **AI-Powered Insights**
- **CopilotKit Integration**: Natural language interface for complex analytics
- **Shared State Architecture**: Real-time synchronization between AI and UI
- **Intelligent Recommendations**: Context-aware suggestions for business optimization
- **Conversational Analytics**: Ask questions like "Show me delivery hotspots" or "Find underperforming menu items"

### 📊 **Comprehensive Analytics Modules**

#### 📍 **Place Analytics**
- Competitive landscape visualization
- Customer density analysis with dynamic heatmaps
- Statistical hotspot identification for delivery optimization
- Geographic market gap analysis

#### 🍽️ **Product Analytics**
- Menu engineering matrix (BCG-style analysis)
- Top-selling items and profitability analysis
- Sales forecasting and trend analysis
- Customer segmentation by product preferences

#### 💰 **Price Analytics**
- Competitor price benchmarking
- Price elasticity simulation
- Break-even analysis visualization
- Revenue optimization recommendations

#### 📢 **Promotion Analytics**
- Marketing campaign ROI tracking
- RFM customer segmentation
- Customer sentiment analysis
- Campaign performance optimization

## 🏗️ Architecture

### **Technology Stack**
- **Frontend**: Next.js 15, React 19, TypeScript
- **Mapping**: Mapbox GL JS + Deck.gl for high-performance geospatial visualization
- **Charts**: Nivo.js for beautiful, interactive data visualizations
- **State Management**: Zustand for efficient shared state
- **AI Integration**: CopilotKit for conversational AI interface
- **Styling**: Tailwind CSS for responsive design

### **Modular Architecture**
```
src/
├── components/           # Shared UI components
├── features/            # Feature-based modules
│   ├── place/          # Place analytics module
│   ├── product/        # Product analytics module
│   ├── price/          # Price analytics module
│   └── promotion/      # Promotion analytics module
├── store/              # Zustand state management
├── types/              # TypeScript type definitions
└── lib/                # Utilities and services
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Mapbox account (for mapping features)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bitebase_agent
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   
   Add your Mapbox access token:
   ```
   NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_token_here
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🤖 AI Assistant Commands

The BiteBase AI assistant understands natural language commands across all analytics modules:

### **Place Analytics**
- "Show me competitor locations"
- "Toggle the customer heatmap"
- "Find delivery hotspots in the Financial District"
- "Analyze market gaps in SOMA"
- "Center the map on Union Square"

### **Product Analytics**
- "Show me my top-selling items"
- "Analyze menu performance"
- "Display the menu engineering matrix"
- "Find underperforming products"

### **Price Analytics**
- "Compare competitor prices"
- "Show price elasticity for Margherita Pizza"
- "Analyze break-even points"
- "Optimize pricing strategy"

### **Promotion Analytics**
- "Show marketing campaign ROI"
- "Analyze customer segments"
- "Display sentiment analysis"
- "Find high-value customers"

## 🗺️ Shared State Architecture

BiteBase implements a sophisticated shared state pattern where:

1. **UI Actions** automatically sync with the AI agent
2. **AI Commands** instantly update the dashboard
3. **Real-time Synchronization** ensures perfect harmony between human and AI interactions
4. **Context Awareness** allows the AI to understand the current state and provide relevant insights

## 📊 Data Sources

BiteBase is designed to integrate with various restaurant industry data sources:

- **POS Systems**: Toast, Square, Clover
- **Location APIs**: Google Places, Foursquare
- **Review Platforms**: Yelp, Google Reviews
- **Marketing Platforms**: Google Ads, Facebook Ads
- **Customer Data**: CRM systems, loyalty programs

## 🔧 Configuration

### **MCP Server Setup**
The project includes MCP (Model Context Protocol) server configuration for enhanced AI coding assistance:

```json
{
  "mcpServers": {
    "CopilotKit MCP": {
      "command": "node",
      "args": ["-e", "..."],
      "autoApprove": [
        "analyze_restaurant_data",
        "update_map_state", 
        "fetch_competitor_data",
        "calculate_hotspots"
      ]
    }
  }
}
```

## 🎯 Key Differentiators

1. **Statistical Rigor**: True hotspot analysis using Getis-Ord Gi* algorithm, not just density visualization
2. **AI-Native Design**: Built from the ground up for conversational analytics
3. **High Performance**: GPU-accelerated mapping with Deck.gl for smooth interaction with large datasets
4. **Industry-Specific**: Tailored specifically for restaurant market research needs
5. **Modular Architecture**: Easily extensible for new analytics modules

## 🚀 Future Enhancements

- **Real-time Data Integration**: Live POS and review data feeds
- **Advanced ML Models**: Predictive analytics for demand forecasting
- **Mobile App**: Native mobile experience for on-the-go insights
- **Multi-location Support**: Enterprise features for restaurant chains
- **API Marketplace**: Third-party integrations and custom data sources

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

We welcome contributions! Please see our Contributing Guidelines for details on how to submit pull requests, report issues, and suggest improvements.

---

**BiteBase** - Transforming restaurant market research through AI-powered geospatial analytics. 🍽️🗺️🤖