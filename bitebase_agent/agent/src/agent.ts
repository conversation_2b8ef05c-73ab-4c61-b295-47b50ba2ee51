/**
 * Restaurant Intelligence Multi-Agent System
 *
 * This system orchestrates four specialized agents for comprehensive restaurant analytics:
 * - Place Intelligence: Location analysis and competitor mapping
 * - Product Intelligence: Menu performance and optimization  
 * - Price Intelligence: Pricing strategy and revenue forecasting
 * - Promotion Intelligence: Marketing campaigns and customer segmentation
 */

import { z } from "zod";
import { RunnableConfig } from "@langchain/core/runnables";
import { tool } from "@langchain/core/tools";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { AIMessage, SystemMessage, BaseMessage } from "@langchain/core/messages";
import { MemorySaver, START, StateGraph, Annotation } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";
import { convertActionsToDynamicStructuredTools } from "@copilotkit/sdk-js/langgraph";

// ===== MULTI-AGENT STATE MANAGEMENT =====

// Enhanced state for multi-agent restaurant intelligence
const AgentStateAnnotation = Annotation.Root({
  // Core messaging for conversation flow
  messages: Annotation<BaseMessage[]>({
    reducer: (currentState: BaseMessage[], updateValue: BaseMessage[]) => currentState.concat(updateValue),
    default: () => [],
  }),

  // Active Agent Tracking
  activeAgent: Annotation<'orchestrator' | 'place' | 'product' | 'price' | 'promotion'>({
    reducer: (x, y) => y ?? x,
    default: () => 'orchestrator',
  }),

  // Agent Status Tracking with Progress
  agentStatus: Annotation<{
    currentStep: string;
    stepProgress: number;
    totalSteps: number;
    isProcessing: boolean;
    lastAction: string;
    startTime: string;
    estimatedCompletion?: string;
    confidenceScore: number;
  }>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({
      currentStep: "Initializing Restaurant Intelligence System",
      stepProgress: 0,
      totalSteps: 1,
      isProcessing: false,
      lastAction: "Multi-agent system started",
      startTime: new Date().toISOString(),
      confidenceScore: 100,
    }),
  }),

  // Analysis Pipeline for Cross-Agent Workflow
  analysisPipeline: Annotation<{
    stages: Array<{
      id: string;
      name: string;
      agent: string;
      status: 'pending' | 'in_progress' | 'completed' | 'failed';
      startTime?: string;
      endTime?: string;
      result?: any;
      error?: string;
      confidenceScore?: number;
    }>;
    currentStageIndex: number;
  }>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({
      stages: [],
      currentStageIndex: 0,
    }),
  }),

  // Restaurant Data Context (shared across agents)
  restaurantContext: Annotation<{
    locations: any[];
    menuItems: any[];
    customers: any[];
    campaigns: any[];
    financials: any[];
  }>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({
      locations: [],
      menuItems: [],
      customers: [],
      campaigns: [],
      financials: [],
    }),
  }),

  // Analysis Results by Domain
  analysisResults: Annotation<{
    place?: any;
    product?: any;
    price?: any;
    promotion?: any;
    synthesis?: any;
  }>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({}),
  }),

  // CopilotKit frontend tools
  tools: Annotation<any[]>({
    reducer: (x, y) => y ?? x,
    default: () => [],
  }),
});

export type RestaurantIntelligenceState = typeof AgentStateAnnotation.State;

// ===== AGENT ORCHESTRATION LOGIC =====

// Intent classification for agent routing
function classifyIntent(message: string): 'place' | 'product' | 'price' | 'promotion' | 'synthesis' {
  const lowerMessage = message.toLowerCase();
  
  // Place Intelligence keywords
  if (lowerMessage.includes('location') || lowerMessage.includes('competitor') || 
      lowerMessage.includes('market') || lowerMessage.includes('delivery') ||
      lowerMessage.includes('expansion') || lowerMessage.includes('demographic')) {
    return 'place';
  }
  
  // Product Intelligence keywords  
  if (lowerMessage.includes('menu') || lowerMessage.includes('item') ||
      lowerMessage.includes('dish') || lowerMessage.includes('popular') ||
      lowerMessage.includes('profitable') || lowerMessage.includes('bcg')) {
    return 'product';
  }
  
  // Price Intelligence keywords
  if (lowerMessage.includes('price') || lowerMessage.includes('revenue') ||
      lowerMessage.includes('forecast') || lowerMessage.includes('profit') ||
      lowerMessage.includes('cost') || lowerMessage.includes('financial')) {
    return 'price';
  }
  
  // Promotion Intelligence keywords
  if (lowerMessage.includes('marketing') || lowerMessage.includes('campaign') ||
      lowerMessage.includes('customer') || lowerMessage.includes('segment') ||
      lowerMessage.includes('roi') || lowerMessage.includes('loyalty')) {
    return 'promotion';
  }
  
  // Default to synthesis for complex multi-domain queries
  return 'synthesis';
}

// ===== PLACE INTELLIGENCE TOOLS =====

const analyzeLocationPerformance = tool(
  (args: { location: string }) => {
    const analysis = {
      location: args.location,
      customerDensity: Math.floor(Math.random() * 100) + 50,
      competitorCount: Math.floor(Math.random() * 8) + 2,
      marketSaturation: Math.random() > 0.5 ? "moderate" : "high",
      deliveryOptimization: {
        optimalRadius: "2.5 miles",
        coveragePercentage: 78,
        avgDeliveryTime: "22 minutes"
      },
      recommendations: [
        "Consider micro-fulfillment center for faster delivery",
        "Target marketing in high-density customer areas", 
        "Monitor competitor pricing in this location"
      ],
      confidenceScore: 85 + Math.floor(Math.random() * 15)
    };

    return `📍 **PLACE INTELLIGENCE ANALYSIS** for ${args.location}:

**Market Metrics:**
• Customer Density: ${analysis.customerDensity}/100 (${analysis.customerDensity > 70 ? 'High' : 'Moderate'})
• Competitor Count: ${analysis.competitorCount} restaurants within 1 mile
• Market Saturation: ${analysis.marketSaturation}
• Delivery Coverage: ${analysis.deliveryOptimization.coveragePercentage}% within optimal radius

**🎯 Strategic Recommendations:**
${analysis.recommendations.map(r => `• ${r}`).join('\n')}

**💡 Key Insight:** ${analysis.customerDensity > 70 ? 'Prime location with high customer density - focus on capacity optimization' : 'Moderate density area - consider targeted marketing campaigns'}

**🎯 Confidence Score: ${analysis.confidenceScore}%**`;
  },
  {
    name: "analyzeLocationPerformance",
    description: "Analyze restaurant location performance including customer density, competitor landscape, and delivery optimization.",
    schema: z.object({
      location: z.string().describe("The location or address to analyze"),
    }),
  }
);

const generateCompetitorBenchmark = tool(
  (args: { radius?: number }) => {
    const radius = args.radius || 2;
    const competitors = [
      { name: "Nonna's Kitchen", distance: 0.3, rating: 4.2, priceRange: "$$", cuisine: "Italian" },
      { name: "Pasta Palace", distance: 0.7, rating: 4.7, priceRange: "$$$$", cuisine: "Italian" },
      { name: "Bella Roma", distance: 1.2, rating: 4.0, priceRange: "$$$", cuisine: "Italian" }
    ];

    const analysis = {
      totalCompetitors: competitors.length,
      avgRating: (competitors.reduce((sum, c) => sum + c.rating, 0) / competitors.length).toFixed(1),
      priceDistribution: {
        "$$": competitors.filter(c => c.priceRange === "$$").length,
        "$$$": competitors.filter(c => c.priceRange === "$$$").length,  
        "$$$$": competitors.filter(c => c.priceRange === "$$$$").length
      },
      marketGaps: ["Fast-casual segment underserved", "Limited healthy options", "No late-night dining"],
      confidenceScore: 92
    };

    return `🏢 **COMPETITOR BENCHMARK ANALYSIS** (${radius} mile radius):

**📊 Market Overview:**
• Total Competitors: ${analysis.totalCompetitors}
• Average Rating: ${analysis.avgRating}⭐
• Price Distribution: $$ (${analysis.priceDistribution["$$"]}), $$$ (${analysis.priceDistribution["$$$"]}), $$$$ (${analysis.priceDistribution["$$$$"]})

**🎯 Identified Market Gaps:**
${analysis.marketGaps.map(gap => `• ${gap}`).join('\n')}

**📍 Closest Competitors:**
${competitors.map(c => `• ${c.name} - ${c.distance}mi, ${c.rating}⭐, ${c.priceRange}`).join('\n')}

**💡 Strategic Recommendation:** ${analysis.marketGaps.length > 0 ? `Focus on ${analysis.marketGaps[0]} to differentiate from competition` : 'Market is saturated - compete on quality and service'}

**🎯 Confidence Score: ${analysis.confidenceScore}%**`;
  },
  {
    name: "generateCompetitorBenchmark",
    description: "Generate comprehensive competitor analysis and market positioning insights.",
    schema: z.object({
      radius: z.number().describe("Analysis radius in miles (default: 2)").default(2),
    }),
  }
);

// ===== PRODUCT INTELLIGENCE TOOLS =====

const analyzeMenuPerformance = tool(
  (args: { timeframe?: string }) => {
    const menuItems = [
      { name: "Margherita Pizza", category: "Pizza", popularity: 85, profitability: 75, trend: "up", revenue: 2250 },
      { name: "Truffle Pasta", category: "Pasta", popularity: 45, profitability: 90, trend: "stable", revenue: 2400 },
      { name: "Caesar Salad", category: "Salads", popularity: 70, profitability: 80, trend: "down", revenue: 1440 },
      { name: "Tiramisu", category: "Desserts", popularity: 30, profitability: 85, trend: "up", revenue: 540 }
    ];

    const analysis = {
      totalItems: menuItems.length,
      avgPopularity: (menuItems.reduce((sum, item) => sum + item.popularity, 0) / menuItems.length).toFixed(1),
      avgProfitability: (menuItems.reduce((sum, item) => sum + item.profitability, 0) / menuItems.length).toFixed(1),
      totalRevenue: menuItems.reduce((sum, item) => sum + item.revenue, 0),
      stars: menuItems.filter(item => item.popularity >= 50 && item.profitability >= 50),
      dogs: menuItems.filter(item => item.popularity < 50 && item.profitability < 50),
      puzzles: menuItems.filter(item => item.popularity < 50 && item.profitability >= 50),
      plowhorses: menuItems.filter(item => item.popularity >= 50 && item.profitability < 50),
      confidenceScore: 88
    };

    return `🍽️ **PRODUCT INTELLIGENCE ANALYSIS** - Menu Engineering:

**📊 Portfolio Overview:**
• Total Menu Items: ${analysis.totalItems}
• Average Popularity: ${analysis.avgPopularity}%
• Average Profitability: ${analysis.avgProfitability}%
• Total Revenue: $${analysis.totalRevenue.toLocaleString()}

**🌟 BCG Matrix Classification:**
• **Stars** (High/High): ${analysis.stars.length} items - ${analysis.stars.map(s => s.name).join(', ')}
• **Plowhorses** (High/Low): ${analysis.plowhorses.length} items
• **Puzzles** (Low/High): ${analysis.puzzles.length} items - ${analysis.puzzles.map(p => p.name).join(', ')}
• **Dogs** (Low/Low): ${analysis.dogs.length} items

**🎯 Strategic Recommendations:**
• Promote Stars heavily and maintain quality
• Increase prices on Plowhorses or reduce costs
• Improve marketing for Puzzles (high profit potential)
• Consider removing Dogs from menu

**💡 Top Performer:** ${analysis.stars[0]?.name || 'Margherita Pizza'} - Perfect balance of popularity and profitability

**🎯 Confidence Score: ${analysis.confidenceScore}%**`;
  },
  {
    name: "analyzeMenuPerformance",
    description: "Analyze menu item performance using BCG matrix methodology (Stars, Dogs, Puzzles, Plowhorses).",
    schema: z.object({
      timeframe: z.string().describe("Analysis timeframe (week, month, quarter)").default("month"),
    }),
  }
);

const optimizeMenuPricing = tool(
  (args: { itemName: string; currentPrice: number }) => {
    const pricingAnalysis = {
      currentPrice: args.currentPrice,
      optimalPrice: args.currentPrice * (1 + (Math.random() * 0.2 - 0.1)),
      elasticity: -1.2,
      revenueImpact: {
        increase5: args.currentPrice * 1.05,
        increase10: args.currentPrice * 1.10,
        decrease5: args.currentPrice * 0.95
      },
      confidenceScore: 82
    };

    const scenarios = [
      { change: "+5%", price: pricingAnalysis.revenueImpact.increase5, demandChange: -6, revenueChange: -1.3 },
      { change: "+10%", price: pricingAnalysis.revenueImpact.increase10, demandChange: -12, revenueChange: -2.8 },
      { change: "-5%", price: pricingAnalysis.revenueImpact.decrease5, demandChange: +6, revenueChange: **** }
    ];

    return `💰 **PRODUCT PRICING ANALYSIS** for ${args.itemName}:

**📊 Current Pricing:**
• Current Price: $${args.currentPrice.toFixed(2)}
• Optimal Price Range: $${(pricingAnalysis.optimalPrice * 0.95).toFixed(2)} - $${(pricingAnalysis.optimalPrice * 1.05).toFixed(2)}
• Price Elasticity: ${pricingAnalysis.elasticity} (moderately elastic)

**🎯 Pricing Scenarios:**
${scenarios.map(s => `• ${s.change}: $${s.price.toFixed(2)} → Demand ${s.demandChange > 0 ? '+' : ''}${s.demandChange}%, Revenue ${s.revenueChange > 0 ? '+' : ''}${s.revenueChange}%`).join('\n')}

**💡 Recommendation:** ${scenarios.find(s => s.revenueChange > 0) ?
  `Consider ${scenarios.find(s => s.revenueChange > 0)?.change} price adjustment to maximize revenue` :
  'Current pricing is optimal - focus on cost reduction or value enhancement'}

**⚠️ Competitive Context:** Monitor competitor responses to any price changes above 5%

**🎯 Confidence Score: ${pricingAnalysis.confidenceScore}%**`;
  },
  {
    name: "optimizeMenuPricing",
    description: "Analyze optimal pricing for menu items using elasticity modeling and competitive benchmarking.",
    schema: z.object({
      itemName: z.string().describe("Name of the menu item to analyze"),
      currentPrice: z.number().describe("Current price of the item"),
    }),
  }
);

// ===== PRICE INTELLIGENCE TOOLS =====

const forecastRevenue = tool(
  (args: { timeframe: string; scenario: string }) => {
    const baseRevenue = 45000;
    const scenarios = {
      conservative: { growth: 0.02, confidence: 85 },
      optimistic: { growth: 0.08, confidence: 65 },
      aggressive: { growth: 0.15, confidence: 45 }
    };

    const scenario = scenarios[args.scenario as keyof typeof scenarios] || scenarios.conservative;
    const months = args.timeframe === "quarter" ? 3 : args.timeframe === "year" ? 12 : 1;
    const forecastRevenue = baseRevenue * Math.pow(1 + scenario.growth, months);

    return `📈 **PRICE INTELLIGENCE ANALYSIS** - Revenue Forecast (${args.timeframe}, ${args.scenario} scenario):

**💰 Financial Projections:**
• Projected Revenue: $${forecastRevenue.toLocaleString()}
• Growth Rate: ${(scenario.growth * 100).toFixed(1)}% monthly
• Confidence Level: ${scenario.confidence}%

**📋 Key Assumptions:**
• Current monthly baseline: $${baseRevenue.toLocaleString()}
• Market conditions remain stable
• No major competitive disruptions
• Seasonal patterns accounted for

**💡 Strategic Insight:** ${scenario.confidence > 70 ? 'High confidence forecast - suitable for planning' : 'Lower confidence - monitor closely and adjust'}

**🎯 Confidence Score: ${scenario.confidence}%**`;
  },
  {
    name: "forecastRevenue",
    description: "Generate revenue forecasts using different growth scenarios and confidence intervals.",
    schema: z.object({
      timeframe: z.enum(["month", "quarter", "year"]).describe("Forecast timeframe"),
      scenario: z.enum(["conservative", "optimistic", "aggressive"]).describe("Growth scenario"),
    }),
  }
);

const analyzeBreakEven = tool(
  (args: { fixedCosts: number; variableCostPerUnit: number; avgSellingPrice: number }) => {
    const contributionMargin = args.avgSellingPrice - args.variableCostPerUnit;
    const breakEvenUnits = Math.ceil(args.fixedCosts / contributionMargin);
    const breakEvenRevenue = breakEvenUnits * args.avgSellingPrice;
    const marginOfSafety = ((1450 - breakEvenUnits) / 1450 * 100);
    const confidenceScore = 91;

    return `⚖️ **PRICE INTELLIGENCE ANALYSIS** - Break-Even Analysis:

**📊 Cost Structure:**
• Fixed Costs: $${args.fixedCosts.toLocaleString()}/month
• Variable Cost per Unit: $${args.variableCostPerUnit.toFixed(2)}
• Average Selling Price: $${args.avgSellingPrice.toFixed(2)}
• Contribution Margin: $${contributionMargin.toFixed(2)}

**🎯 Break-Even Metrics:**
• Break-Even Units: ${breakEvenUnits.toLocaleString()} units/month
• Break-Even Revenue: $${breakEvenRevenue.toLocaleString()}/month
• Current Margin of Safety: ${marginOfSafety.toFixed(1)}%

**💡 Strategic Recommendations:**
${marginOfSafety > 20 ? '✅ Healthy margin of safety - consider growth investments' : '⚠️ Low margin of safety - focus on cost optimization'}
${contributionMargin < args.avgSellingPrice * 0.6 ? '• Consider price increases or cost reduction' : '• Strong unit economics'}

**🎯 Confidence Score: ${confidenceScore}%**`;
  },
  {
    name: "analyzeBreakEven",
    description: "Calculate break-even analysis with margin of safety and strategic recommendations.",
    schema: z.object({
      fixedCosts: z.number().describe("Monthly fixed costs"),
      variableCostPerUnit: z.number().describe("Variable cost per unit sold"),
      avgSellingPrice: z.number().describe("Average selling price per unit"),
    }),
  }
);

// ===== PROMOTION INTELLIGENCE TOOLS =====

const analyzeCustomerSegments = tool(
  (args: { segmentationType: string }) => {
    const rfmSegments = {
      champions: { count: 45, value: 12500, recency: 85, frequency: 90, monetary: 95 },
      loyalCustomers: { count: 78, value: 18200, recency: 70, frequency: 85, monetary: 80 },
      newCustomers: { count: 52, value: 8900, recency: 90, frequency: 45, monetary: 60 },
      atRisk: { count: 23, value: 5600, recency: 25, frequency: 75, monetary: 70 },
      lost: { count: 18, value: 2100, recency: 15, frequency: 25, monetary: 40 }
    };

    const segmentEntries = Object.entries(rfmSegments);
    const totalCustomers = segmentEntries.reduce((sum, [, seg]) => sum + seg.count, 0);
    const totalValue = segmentEntries.reduce((sum, [, seg]) => sum + seg.value, 0);
    const confidenceScore = 89;

    return `👥 **PROMOTION INTELLIGENCE ANALYSIS** - RFM Customer Segmentation:

**📊 Segment Distribution:**
${segmentEntries.map(([name, data]) =>
  `• ${name.charAt(0).toUpperCase() + name.slice(1)}: ${data.count} customers (${(data.count/totalCustomers*100).toFixed(1)}%) - $${data.value.toLocaleString()}`
).join('\n')}

**💰 Value Analysis:**
• Total Customer Value: $${totalValue.toLocaleString()}
• Average Customer Value: $${(totalValue/totalCustomers).toFixed(0)}
• Champions represent ${((rfmSegments.champions.value/totalValue)*100).toFixed(1)}% of total value

**🎯 Strategic Recommendations:**
• **Champions:** VIP treatment, referral programs, exclusive offers
• **Loyal Customers:** Retention campaigns, loyalty rewards
• **New Customers:** Onboarding sequences, first-purchase incentives
• **At Risk:** Re-engagement campaigns, win-back offers
• **Lost:** Targeted win-back or write-off decision

**💡 Priority Focus:** ${rfmSegments.atRisk.count > 20 ? 'At-Risk segment needs immediate attention' : 'Healthy customer distribution'}

**🎯 Confidence Score: ${confidenceScore}%**`;
  },
  {
    name: "analyzeCustomerSegments",
    description: "Perform RFM (Recency, Frequency, Monetary) customer segmentation analysis.",
    schema: z.object({
      segmentationType: z.enum(["rfm", "behavioral", "demographic"]).describe("Type of segmentation analysis"),
    }),
  }
);

const evaluateCampaignROI = tool(
  (args: { campaignName: string; spent: number; revenue: number; newCustomers: number }) => {
    const roi = ((args.revenue - args.spent) / args.spent * 100);
    const customerAcquisitionCost = args.spent / args.newCustomers;
    const revenuePerCustomer = args.revenue / args.newCustomers;
    const ltv = revenuePerCustomer * 6;
    const ltvCacRatio = ltv / customerAcquisitionCost;
    const confidenceScore = 87;

    return `📢 **PROMOTION INTELLIGENCE ANALYSIS** - Campaign Performance: ${args.campaignName}

**💰 Financial Metrics:**
• Total Spent: $${args.spent.toLocaleString()}
• Revenue Generated: $${args.revenue.toLocaleString()}
• ROI: ${roi.toFixed(1)}%
• New Customers Acquired: ${args.newCustomers}

**📊 Customer Metrics:**
• Customer Acquisition Cost (CAC): $${customerAcquisitionCost.toFixed(2)}
• Revenue per New Customer: $${revenuePerCustomer.toFixed(2)}
• Estimated 6-month LTV: $${ltv.toFixed(2)}
• LTV:CAC Ratio: ${ltvCacRatio.toFixed(1)}:1

**🎯 Performance Rating:**
${roi > 200 ? '🌟 Excellent - Scale up this campaign' :
  roi > 100 ? '✅ Good - Continue with optimization' :
  roi > 0 ? '⚠️ Marginal - Needs improvement' : '❌ Unprofitable - Reconsider strategy'}

**💡 Strategic Recommendation:** ${ltvCacRatio > 3 ? 'Strong unit economics - increase budget' : 'Monitor customer retention to improve LTV'}

**🎯 Confidence Score: ${confidenceScore}%**`;
  },
  {
    name: "evaluateCampaignROI",
    description: "Evaluate marketing campaign performance with ROI, CAC, and LTV analysis.",
    schema: z.object({
      campaignName: z.string().describe("Name of the marketing campaign"),
      spent: z.number().describe("Total amount spent on campaign"),
      revenue: z.number().describe("Revenue attributed to campaign"),
      newCustomers: z.number().describe("Number of new customers acquired"),
    }),
  }
);

// ===== AGENT WORKFLOW ORCHESTRATION =====

// Combined tools array for all intelligence domains
const restaurantIntelligenceTools = [
  analyzeLocationPerformance,
  generateCompetitorBenchmark,
  analyzeMenuPerformance,
  optimizeMenuPricing,
  forecastRevenue,
  analyzeBreakEven,
  analyzeCustomerSegments,
  evaluateCampaignROI
];

// Main chat node with multi-agent orchestration
async function chat_node(state: RestaurantIntelligenceState, config: RunnableConfig) {
  const model = new ChatOpenAI({ 
    temperature: 0.3, 
    model: "gpt-4o" 
  });

  const modelWithTools = model.bindTools([
    ...convertActionsToDynamicStructuredTools(state.tools || []),
    ...restaurantIntelligenceTools,
  ]);

  // Analyze the latest message to determine which agent should handle it
  const lastMessage = state.messages[state.messages.length - 1];
  const userMessage = lastMessage?.content?.toString() || "";
  const intendedAgent = classifyIntent(userMessage);

  // Enhanced system message with multi-agent coordination
  const systemMessage = new SystemMessage({
    content: `You are the Restaurant Intelligence Orchestrator, coordinating four specialized AI agents:

🍽️ **RESTAURANT INTELLIGENCE MULTI-AGENT SYSTEM**

**Current Analysis Focus: ${intendedAgent.toUpperCase()} INTELLIGENCE**

**🏢 PLACE Intelligence Agent** - Location & Market Analysis
• Customer density mapping and demographic analysis
• Competitor landscape assessment and market positioning  
• Delivery optimization and coverage analysis
• Site selection and expansion planning
• Market penetration and saturation analysis

**🍽️ PRODUCT Intelligence Agent** - Menu & Performance Analysis
• BCG menu engineering matrix (Stars, Dogs, Puzzles, Plowhorses)
• Item popularity and profitability analysis
• Trend analysis and seasonal performance
• Menu optimization and pricing strategy
• Recipe cost analysis and margin optimization

**💰 PRICE Intelligence Agent** - Revenue & Financial Analysis
• Dynamic pricing models and elasticity analysis
• Revenue forecasting and scenario planning
• Break-even analysis and cost structure optimization
• Profit margin analysis and cost management
• Financial performance benchmarking

**📢 PROMOTION Intelligence Agent** - Marketing & Customer Analytics
• RFM customer segmentation (Recency, Frequency, Monetary)
• Campaign ROI analysis and marketing attribution
• Customer lifetime value (LTV) and acquisition cost (CAC)
• Retention analysis and churn prediction
• Promotional effectiveness and A/B testing

**ORCHESTRATION INSTRUCTIONS:**
1. **Route the query** to the appropriate specialized agent based on intent analysis
2. **Use domain-specific tools** to provide comprehensive analysis
3. **Include confidence scores** (0-100%) for all insights and recommendations
4. **Provide actionable recommendations** with clear next steps
5. **Consider cross-domain impacts** and suggest synthesis when relevant
6. **Maintain professional tone** while being accessible to restaurant operators

**Current Status:** Agent ${intendedAgent.toUpperCase()} is now analyzing your request...

When responding:
- Start with the agent name and analysis type
- Include specific restaurant industry metrics and KPIs
- Provide confidence scores for all insights
- Suggest actionable next steps
- Consider impacts across other domains when relevant`
  });

  const response = await modelWithTools.invoke([
    systemMessage,
    ...state.messages
  ], config);

  return { 
    messages: [response],
    activeAgent: intendedAgent,
    agentStatus: {
      ...state.agentStatus,
      currentStep: `${intendedAgent.toUpperCase()} Intelligence Analysis`,
      lastAction: `Routing query to ${intendedAgent} agent`,
      isProcessing: true,
    }
  };
}

// Route determination logic
function shouldContinue(state: RestaurantIntelligenceState) {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;

  if (lastMessage.tool_calls?.length) {
    const toolCallName = lastMessage.tool_calls[0].name;
    if (!state.tools || state.tools.every((tool: any) => tool.name !== toolCallName)) {
      return "tool_node";
    }
  }

  return "__end__";
}

// ===== WORKFLOW GRAPH DEFINITION =====

const workflow = new StateGraph(AgentStateAnnotation)
  .addNode("chat_node", chat_node)
  .addNode("tool_node", new ToolNode(restaurantIntelligenceTools))
  .addEdge(START, "chat_node")
  .addEdge("tool_node", "chat_node")
  .addConditionalEdges("chat_node", shouldContinue);

const memory = new MemorySaver();

export const graph = workflow.compile({
  checkpointer: memory,
});
