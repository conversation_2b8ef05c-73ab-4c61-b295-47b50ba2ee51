{"name": "langgraph-js-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:ui\" \"npm run dev:agent\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:studio": "concurrently \"npm run dev:ui\" \"npm run dev:agent:studio\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:agent": "cd agent && npx @langchain/langgraph-cli dev --port 8123 --no-browser", "dev:agent:studio": "cd agent && npx @langchain/langgraph-cli dev --port 8123", "dev:ui": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ag-ui/langgraph": "0.0.7", "@copilotkit/react-core": "1.9.3", "@copilotkit/react-ui": "1.9.3", "@copilotkit/runtime": "1.9.3", "@deck.gl/core": "^9.0.0", "@deck.gl/layers": "^9.0.0", "@deck.gl/react": "^9.0.0", "@deck.gl/geo-layers": "^9.0.0", "@nivo/bar": "^0.87.0", "@nivo/line": "^0.87.0", "@nivo/pie": "^0.87.0", "@nivo/scatterplot": "^0.87.0", "@nivo/heatmap": "^0.87.0", "mapbox-gl": "^3.0.0", "react-map-gl": "^7.1.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "zod": "^3.24.4", "zustand": "^4.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@langchain/langgraph-cli": "0.0.40", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}