{"name": "langgraph-js-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"bun dev:ui\" \"bun dev:agent\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:studio": "concurrently \"bun dev:ui\" \"bun dev:agent:studio\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:agent": "cd agent && npx @langchain/langgraph-cli dev --port 8123 --no-browser", "dev:agent:studio": "cd agent && npx @langchain/langgraph-cli dev --port 8123", "dev:ui": "next dev", "dev:copilot": "npx copilot-api@latest start --github-token $GITHUB_TOKEN --port 4141", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ag-ui/langgraph": "0.0.7", "@copilotkit/react-core": "1.10.4", "@copilotkit/react-textarea": "1.10.4", "@copilotkit/react-ui": "1.10.4", "@copilotkit/runtime": "^1.10.4", "@deck.gl/core": "^9.1.14", "@deck.gl/geo-layers": "^9.1.14", "@deck.gl/layers": "^9.1.14", "@deck.gl/react": "^9.1.14", "@headlessui/react": "^2.2.8", "@nivo/bar": "^0.87.0", "@nivo/heatmap": "^0.87.0", "@nivo/line": "^0.87.0", "@nivo/pie": "^0.87.0", "@nivo/scatterplot": "^0.87.0", "@react-aria/focus": "^3.21.1", "bun": "^1.2.22", "clsx": "^2.1.1", "mapbox-gl": "^3.15.0", "next": "15.3.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-map-gl": "^7.1.9", "tailwind-merge": "^3.3.1", "update": "^0.7.4", "zod": "^3.25.76", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@langchain/langgraph-cli": "0.0.40", "@tailwindcss/postcss": "^4.1.13", "@types/node": "^20.19.16", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "concurrently": "^9.2.1", "eslint": "^9.35.0", "eslint-config-next": "15.3.2", "tailwindcss": "^4.1.13", "typescript": "^5.9.2"}}