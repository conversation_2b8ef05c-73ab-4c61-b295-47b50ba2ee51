"use client";

import { useCoAgent, useCopilotAction } from "@copilotkit/react-core";
import { CopilotKitCSSProperties, CopilotSidebar } from "@copilotkit/react-ui";
import { useState } from "react";
import BiteBaseDashboard from "../components/BiteBaseDashboard";
import { RestaurantAnalyticsState } from "../types/restaurant";

export default function CopilotKitPage() {
  const [themeColor, setThemeColor] = useState("#667eea");

  // 🪁 Frontend Actions: https://docs.copilotkit.ai/guides/frontend-actions
  useCopilotAction({
    name: "setThemeColor",
    description: "Set the theme color of the BiteBase dashboard.",
    parameters: [{
      name: "themeColor",
      description: "The theme color to set. Use restaurant-friendly colors like blue, green, or orange.",
      required: true, 
    }],
    handler({ themeColor }) {
      setThemeColor(themeColor);
    },
  });

  return (
    <main style={{ "--copilot-kit-primary-color": themeColor } as CopilotKitCSSProperties}>
      <BiteBaseDashboard themeColor={themeColor} />
      <CopilotSidebar
        clickOutsideToClose={false}
        defaultOpen={true}
        labels={{
          title: "🍽️ BiteBase AI Assistant",
          initial: "🗺️ Welcome to BiteBase! I'm your AI-powered restaurant market research assistant.\n\n**I can help you with:**\n- 📍 **Place Analytics**: \"Show me competitor locations\" or \"Find delivery hotspots\"\n- 🍽️ **Product Analytics**: \"Analyze my menu performance\" or \"Show top-selling items\"\n- 💰 **Price Analytics**: \"Compare competitor prices\" or \"Optimize pricing strategy\"\n- 📢 **Promotion Analytics**: \"Analyze customer segments\" or \"Track campaign ROI\"\n\n**Try these commands:**\n- \"Toggle the customer heatmap\"\n- \"Select my restaurant on the map\"\n- \"Show me my menu engineering matrix\"\n- \"Find underperforming menu items\"\n\nEvery interaction updates the dashboard in real-time through our shared state system!"
        }}
      />
    </main>
  );
}
