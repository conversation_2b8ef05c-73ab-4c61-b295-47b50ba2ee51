"use client";

import { useCopilotAction } from "@copilotkit/react-core";
import { CopilotKitCSSProperties, CopilotSidebar } from "@copilotkit/react-ui";
import { useState } from "react";
import SimpleBiteBaseDashboard from "../components/SimpleBiteBaseDashboard";

/**
 * Enhanced BiteBase Restaurant Intelligence Platform
 * 
 * Features:
 * - Multi-agent orchestration for comprehensive restaurant analytics
 * - Real-time agent status visualization and progress tracking
 * - Seamless map-chat integration with shared state synchronization
 * - Professional workflow visualization and confidence scoring
 * - Cross-agent synthesis and actionable business insights
 */
export default function BiteBasePage() {
  const [themeColor, setThemeColor] = useState("#667eea");

  // Enhanced theme action for multi-agent system
  useCopilotAction({
    name: "setThemeColor",
    description: "Set the theme color of the BiteBase multi-agent restaurant intelligence platform.",
    parameters: [{
      name: "themeColor",
      description: "The theme color to set. Use professional restaurant-friendly colors like blue, green, purple, or orange.",
      required: true, 
    }],
    handler({ themeColor }) {
      setThemeColor(themeColor);
    },
  });

  return (
    <main 
      style={{ "--copilot-kit-primary-color": themeColor } as CopilotKitCSSProperties}
    >
      {/* Simplified Multi-Agent Restaurant Intelligence Dashboard */}
      <SimpleBiteBaseDashboard />
      
      {/* CopilotKit Chat Sidebar */}
      <CopilotSidebar
        clickOutsideToClose={false}
        defaultOpen={true}
        labels={{
          title: "🍽️ BiteBase AI Assistant",
          initial: "🗺️ Welcome to BiteBase Intelligence!\n\n**I'm your AI-powered restaurant analytics assistant.**\n\n**I can help you with:**\n- 📍 **Place Intelligence**: \"Show competitor analysis\" or \"Find delivery hotspots\"\n- 🍽️ **Product Intelligence**: \"Analyze menu performance\" or \"Show top-selling items\"\n- 💰 **Price Intelligence**: \"Forecast revenue\" or \"Calculate break-even\"\n- 📢 **Promotion Intelligence**: \"Segment customers\" or \"Track campaign ROI\"\n\n**Try these commands:**\n- \"Toggle the customer heatmap\"\n- \"Analyze my restaurant location\"\n- \"Show menu performance insights\"\n- \"Calculate financial projections\"\n\nEvery interaction updates the dashboard in real-time!"
        }}
      />
    </main>
  );
}
