'use client';

import React, { useCallback, useState } from 'react';
import { useCoAgent, useCopilotAction } from "@copilotkit/react-core";
import { useRestaurantStore } from '../store/restaurantStore';
import { RestaurantAnalyticsState, RestaurantLocation, HotspotData } from '../types/restaurant';
import MapContainer from './Map/MapContainer';

interface BiteBaseDashboardProps {
  themeColor: string;
}

interface ChatMessage {
  type: 'user' | 'ai';
  content: string;
  timestamp: number;
}

interface MapInteraction {
  type: string;
  data: any;
  timestamp: number;
}

export default function BiteBaseDashboard({ themeColor }: BiteBaseDashboardProps) {
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      type: 'ai',
      content: '🗺️ Welcome! I\'m your AI Map Assistant with shared state capabilities. I can see your current map in real-time!<br><br><strong>Current map state:</strong><br>• Restaurant markers visible<br>• Zoom level: 12<br>• Mode: Explore<br>• Center: San Francisco',
      timestamp: Date.now() - 10000
    },
    {
      type: 'ai', 
      content: '✨ <strong>Shared State Demo:</strong> When you interact with the map, I instantly know about it! Try clicking to add a marker and watch me respond.',
      timestamp: Date.now() - 5000
    }
  ]);

  const {
    mapView,
    restaurants,
    customers,
    hotspots,
    selectedLocation,
    showHeatmap,
    showCompetitors,
    showHotspots,
    isLoading,
    aiInsights,
    lastAIAction,
    setMapView,
    toggleLayer,
    selectLocation,
    addAIInsight,
    setLastAIAction,
    updateHotspots,
    setLoading,
    addRestaurant,
    clearMap
  } = useRestaurantStore();

  // 🪁 Enhanced Shared State with AI Agent - Map Canvas & Chat Integration
  const { state, setState } = useCoAgent<RestaurantAnalyticsState & {
    mapInteractions: MapInteraction[];
    chatMessages: ChatMessage[];
    mapMode: 'explore' | 'analyze' | 'edit';
    selectedMarkers: string[];
  }>({
    name: "restaurantAnalyticsAgent",
    initialState: {
      mapView,
      restaurants,
      customers,
      hotspots,
      selectedLocation,
      activeModule: 'place',
      filters: {
        timeRange: {
          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString(),
          preset: 'week'
        }
      },
      menuItems: [],
      isLoading,
      showHeatmap,
      showCompetitors,
      showHotspots,
      aiInsights,
      mapInteractions: [],
      chatMessages,
      mapMode: 'explore',
      selectedMarkers: []
    }
  });

  // Helper function to add chat messages
  const addChatMessage = useCallback((type: 'user' | 'ai', content: string) => {
    const newMessage: ChatMessage = {
      type,
      content,
      timestamp: Date.now()
    };
    setChatMessages(prev => [...prev, newMessage]);
    setState({ 
      ...state, 
      chatMessages: [...state.chatMessages, newMessage]
    });
  }, [state, setState]);

  // Helper function to show status
  const showStatus = useCallback((message: string = "State Updated") => {
    setLastAIAction(message);
  }, [setLastAIAction]);

  // 🪁 Enhanced Frontend Actions for Dynamic Map-Chat Integration
  useCopilotAction({
    name: "addMapMarker",
    description: "Add a new marker to the map at specified coordinates or location name.",
    parameters: [
      {
        name: "name",
        description: "Name/label for the marker",
        type: "string",
        required: true,
      },
      {
        name: "type", 
        description: "Type of marker to add",
        type: "string",
        enum: ["restaurant", "poi", "business", "competitor"],
        required: true,
      },
      {
        name: "latitude",
        description: "Latitude coordinate (optional if using location name)",
        type: "number",
      },
      {
        name: "longitude", 
        description: "Longitude coordinate (optional if using location name)",
        type: "number",
      },
      {
        name: "location",
        description: "Location name to place marker (optional if using coordinates)",
        type: "string",
      }
    ],
    handler: ({ name, type, latitude, longitude, location }) => {
      // Use provided coordinates or default SF locations
      const landmarks: Record<string, { lat: number, lng: number }> = {
        'financial district': { lat: 37.7946, lng: -122.3999 },
        'union square': { lat: 37.7880, lng: -122.4074 },
        'north beach': { lat: 37.8067, lng: -122.4103 },
        'mission district': { lat: 37.7599, lng: -122.4148 },
        'soma': { lat: 37.7749, lng: -122.4194 },
        'golden gate bridge': { lat: 37.8199, lng: -122.4783 },
        'fisherman\'s wharf': { lat: 37.8084, lng: -122.4156 }
      };
      
      const coords = (latitude && longitude) 
        ? { lat: latitude, lng: longitude }
        : landmarks[location?.toLowerCase() || ''] || { lat: 37.7749 + (Math.random() - 0.5) * 0.02, lng: -122.4194 + (Math.random() - 0.5) * 0.02 };
      
      const newMarker: RestaurantLocation = {
        id: `marker-${Date.now()}`,
        name,
        lat: coords.lat,
        lng: coords.lng,
        address: `${coords.lat.toFixed(4)}, ${coords.lng.toFixed(4)}`,
        type: type === 'restaurant' ? 'own' : 'competitor',
        rating: 3.5 + Math.random() * 1.5,
        priceRange: ['$', '$$', '$$$'][Math.floor(Math.random() * 3)] as '$' | '$$' | '$$$' | '$$$$',
        cuisine: 'Various',
        isSelected: false
      };
      
      addRestaurant(newMarker);
      
      const newInteraction: MapInteraction = {
        type: 'marker_added',
        data: { name, type, coordinates: coords },
        timestamp: Date.now()
      };
      
      setState({ 
        ...state, 
        mapInteractions: [...state.mapInteractions, newInteraction],
        restaurants: [...restaurants, newMarker]
      });
      
      addAIInsight(`📍 Added new ${type} marker: "${name}" at ${coords.lat.toFixed(4)}, ${coords.lng.toFixed(4)}`);
      setLastAIAction(`Added ${name} marker`);
      showStatus("Marker Added");
      
      // Add AI chat response
      addChatMessage('ai', `🎯 Perfect! I've added "${name}" as a ${type} marker to your map. The shared state is working perfectly - I can see the new marker and all its details!`);
    },
  });

  useCopilotAction({
    name: "toggleMapLayer",
    description: "Toggle visibility of map layers (heatmap, competitors, hotspots).",
    parameters: [{
      name: "layer",
      description: "The map layer to toggle",
      type: "string",
      enum: ["heatmap", "competitors", "hotspots"],
      required: true,
    }],
    handler: ({ layer }) => {
      toggleLayer(layer as 'heatmap' | 'competitors' | 'hotspots');
      const newState = { ...state };
      if (layer === 'heatmap') newState.showHeatmap = !showHeatmap;
      if (layer === 'competitors') newState.showCompetitors = !showCompetitors;
      if (layer === 'hotspots') newState.showHotspots = !showHotspots;
      setState(newState);
      addAIInsight(`🗺️ Toggled ${layer} layer ${layer === 'heatmap' ? (showHeatmap ? 'off' : 'on') : layer === 'competitors' ? (showCompetitors ? 'off' : 'on') : (showHotspots ? 'off' : 'on')}`);
      setLastAIAction(`Toggled ${layer} layer`);
      addChatMessage('ai', `🗺️ ${layer.charAt(0).toUpperCase() + layer.slice(1)} layer toggled ${layer === 'heatmap' ? (showHeatmap ? 'off' : 'on') : layer === 'competitors' ? (showCompetitors ? 'off' : 'on') : (showHotspots ? 'off' : 'on')}!`);
    },
  });

  useCopilotAction({
    name: "clearMapMarkers",
    description: "Clear all added markers from the map (keeps original restaurants).",
    parameters: [],
    handler: () => {
      clearMap();
      setState({ ...state, restaurants: restaurants.filter(r => r.type === 'own'), selectedLocation: undefined });
      addAIInsight("🗑️ Map cleared! All added markers have been removed.");
      setLastAIAction("Map cleared");
      showStatus("Map Cleared");
      addChatMessage('ai', "🗑️ Map cleared! All markers have been removed. The shared state reflects this change instantly. Ready to start fresh!");
    },
  });

  const handleLocationSelect = useCallback((location: RestaurantLocation) => {
    setState({ ...state, selectedLocation: location.id });
    addChatMessage('user', `I selected the "${location.name}" marker`);
    addChatMessage('ai', `📍 You selected the "${location.name}" marker (${location.type}). I can now provide specific information about this location. What would you like to know?`);
  }, [state, setState, addChatMessage]);

  const handleMapClick = useCallback((coordinates: { lat: number, lng: number }) => {
    const newMarker: RestaurantLocation = {
      id: `click-marker-${Date.now()}`,
      name: `Location ${restaurants.length + 1}`,
      lat: coordinates.lat,
      lng: coordinates.lng,
      address: `${coordinates.lat.toFixed(4)}, ${coordinates.lng.toFixed(4)}`,
      type: 'competitor',
      rating: 3.5 + Math.random() * 1.5,
      priceRange: '$$',
      cuisine: 'Various',
      isSelected: false
    };
    
    addRestaurant(newMarker);
    showStatus("Marker Added");
    addChatMessage('user', `I added a new marker at coordinates (${coordinates.lat.toFixed(4)}, ${coordinates.lng.toFixed(4)})`);
    addChatMessage('ai', `🎯 I see you added a new marker! I've updated my understanding of your map. There are now ${restaurants.length + 1} markers total. Would you like me to suggest nearby places of interest?`);
  }, [restaurants.length, addRestaurant, showStatus, addChatMessage]);

  const zoomIn = useCallback(() => {
    if (mapView.zoom < 20) {
      const newZoom = mapView.zoom + 1;
      setMapView({ zoom: newZoom });
      setState({ ...state, mapView: { ...mapView, zoom: newZoom } });
      showStatus("Zoomed In");
      addChatMessage('user', `I zoomed in to level ${newZoom}`);
      addChatMessage('ai', `🔍 Zoom level increased to ${newZoom}. I can see more detail now!`);
    }
  }, [mapView, setMapView, setState, state, showStatus, addChatMessage]);

  const zoomOut = useCallback(() => {
    if (mapView.zoom > 1) {
      const newZoom = mapView.zoom - 1;
      setMapView({ zoom: newZoom });
      setState({ ...state, mapView: { ...mapView, zoom: newZoom } });
      showStatus("Zoomed Out");
      addChatMessage('user', `I zoomed out to level ${newZoom}`);
      addChatMessage('ai', `🔍 Zoom level decreased to ${newZoom}. Showing broader area view.`);
    }
  }, [mapView, setMapView, setState, state, showStatus, addChatMessage]);

  const addMarker = useCallback((type: 'poi' | 'business') => {
    const icons = { poi: '🏛️', business: '🏢' };
    const newMarker: RestaurantLocation = {
      id: `${type}-marker-${Date.now()}`,
      name: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
      lat: 37.7749 + (Math.random() - 0.5) * 0.02,
      lng: -122.4194 + (Math.random() - 0.5) * 0.02,
      address: `Random ${type} location`,
      type: 'competitor',
      rating: 3.5 + Math.random() * 1.5,
      priceRange: '$$',
      cuisine: 'Various',
      isSelected: false
    };
    
    addRestaurant(newMarker);
    showStatus(`${type} Added`);
    addChatMessage('user', `I added a new ${type} marker`);
    addChatMessage('ai', `✅ Perfect! I've added a new ${type} marker to your map. The shared state is working perfectly - I can see all ${restaurants.length + 1} markers now!`);
  }, [addRestaurant, restaurants.length, showStatus, addChatMessage]);

  const triggerAI = useCallback(() => {
    showStatus("AI Analyzing");
    addChatMessage('ai', `🤖 <strong>AI Analysis Complete!</strong><br><br>
      Current map state:<br>
      • ${restaurants.length} markers<br>
      • Zoom level: ${mapView.zoom}<br>
      • Selected: ${selectedLocation ? restaurants.find(r => r.id === selectedLocation)?.name || 'None' : 'None'}<br><br>
      ✨ <strong>Shared State Status:</strong> Perfect sync! I can see every change you make in real-time.`);
  }, [restaurants.length, mapView.zoom, selectedLocation, restaurants, showStatus, addChatMessage]);

  const sendMessage = useCallback((message: string) => {
    addChatMessage('user', message);
  }, [addChatMessage]);

  return (
    <div style={{
      margin: 0,
      padding: 0,
      boxSizing: 'border-box',
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      height: '100vh',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        background: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '15px 20px',
        textAlign: 'center',
        backdropFilter: 'blur(10px)'
      }}>
        <h1 style={{ fontSize: '24px', marginBottom: '5px' }}>🗺️ Map-Chat AI Assistant Demo</h1>
        <p style={{ fontSize: '14px', opacity: 0.9 }}>🚀 CopilotKit Shared State - Real-time synchronization between map and AI agent</p>
      </div>

      {/* Main Container */}
      <div style={{
        flex: 1,
        display: 'flex',
        padding: '20px',
        gap: '20px'
      }}>
        {/* Map Section */}
        <div style={{
          flex: 1,
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '12px',
          padding: '20px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
          display: 'flex',
          flexDirection: 'column',
          gap: '15px'
        }}>
          {/* Shared State Demo */}
          <div style={{
            background: 'rgba(76, 175, 80, 0.1)',
            border: '2px solid #4CAF50',
            borderRadius: '8px',
            padding: '15px',
            marginBottom: '15px'
          }}>
            <h3 style={{ color: '#2E7D32', marginBottom: '8px', fontSize: '16px' }}>✨ Shared State Magic</h3>
            <p style={{ color: '#388E3C', fontSize: '14px', lineHeight: 1.4 }}>
              Click on the map to add markers, and watch the AI instantly understand your actions! Every interaction syncs in real-time.
            </p>
          </div>

          {/* Controls */}
          <div style={{
            display: 'flex',
            gap: '15px',
            flexWrap: 'wrap',
            alignItems: 'center',
            background: '#f8f9fa',
            padding: '15px',
            borderRadius: '8px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <label>🗺️ Map:</label>
              <button 
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 500
                }}
                onClick={zoomIn}
              >
                🔍+
              </button>
              <button 
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 500
                }}
                onClick={zoomOut}
              >
                🔍-
              </button>
              <span>Zoom: {mapView.zoom.toFixed(1)}</span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <label>📍 Add:</label>
              <button 
                style={{
                  background: 'rgba(102, 126, 234, 0.1)',
                  color: '#667eea',
                  border: '2px dashed #667eea',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
                onClick={() => addMarker('poi')}
              >
                🏛️ POI
              </button>
              <button 
                style={{
                  background: 'rgba(102, 126, 234, 0.1)',
                  color: '#667eea',
                  border: '2px dashed #667eea',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
                onClick={() => addMarker('business')}
              >
                🏢 Business
              </button>
            </div>

            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <button 
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 500
                }}
                onClick={() => clearMap()}
              >
                🗑️ Clear
              </button>
              <button 
                style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: 500
                }}
                onClick={triggerAI}
              >
                🤖 AI Analyze
              </button>
            </div>
          </div>

          {/* Map Canvas */}
          <div style={{
            flex: 1,
            border: '2px solid #e9ecef',
            borderRadius: '8px',
            position: 'relative',
            minHeight: '400px',
            overflow: 'hidden'
          }}>
            {isLoading && (
              <div style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: '#4CAF50',
                color: 'white',
                padding: '6px 12px',
                borderRadius: '20px',
                fontSize: '12px',
                fontWeight: 600,
                zIndex: 1000
              }}>
                🔄 State Syncing
              </div>
            )}
            
            <div style={{
              position: 'absolute',
              bottom: '10px',
              left: '10px',
              background: 'rgba(0,0,0,0.8)',
              color: 'white',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '12px',
              fontFamily: 'monospace',
              zIndex: 1000
            }}>
              📍 {restaurants.length} markers | 🧭 Explore mode | 📊 Real-time sync active
            </div>

            <MapContainer onLocationSelect={handleLocationSelect} onMapClick={handleMapClick} />
          </div>
        </div>

        {/* Chat Section */}
        <div style={{
          width: '400px',
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* Chat Header */}
          <div style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            padding: '16px',
            borderRadius: '12px 12px 0 0',
            fontWeight: 600
          }}>
            🤖 AI Map Assistant
          </div>

          {/* Chat Content */}
          <div style={{
            flex: 1,
            padding: '20px',
            overflowY: 'auto',
            maxHeight: '400px'
          }}>
            {chatMessages.map((message, index) => (
              <div
                key={index}
                style={{
                  marginBottom: '15px',
                  padding: '12px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  lineHeight: 1.5,
                  background: message.type === 'user' 
                    ? '#e3f2fd' 
                    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%)',
                  borderLeft: message.type === 'user' 
                    ? '4px solid #2196F3' 
                    : '4px solid #764ba2'
                }}
                dangerouslySetInnerHTML={{ __html: message.content }}
              />
            ))}
          </div>

          {/* Suggestions */}
          <div style={{
            padding: '16px',
            borderTop: '1px solid #e9ecef',
            background: '#f8f9fa'
          }}>
            <h4 style={{ marginBottom: '10px', color: '#333', fontSize: '14px' }}>💡 Try These Commands:</h4>
            <button 
              style={{
                display: 'block',
                width: '100%',
                background: 'rgba(102, 126, 234, 0.1)',
                border: '1px solid rgba(102, 126, 234, 0.2)',
                color: '#667eea',
                padding: '10px',
                borderRadius: '6px',
                marginBottom: '8px',
                cursor: 'pointer',
                fontSize: '13px',
                textAlign: 'left'
              }}
              onClick={() => sendMessage('Find coffee shops nearby')}
            >
              ☕ Find coffee shops nearby
            </button>
            <button 
              style={{
                display: 'block',
                width: '100%',
                background: 'rgba(102, 126, 234, 0.1)',
                border: '1px solid rgba(102, 126, 234, 0.2)',
                color: '#667eea',
                padding: '10px',
                borderRadius: '6px',
                marginBottom: '8px',
                cursor: 'pointer',
                fontSize: '13px',
                textAlign: 'left'
              }}
              onClick={() => sendMessage('Plan a route between markers')}
            >
              🧭 Plan a route between markers
            </button>
            <button 
              style={{
                display: 'block',
                width: '100%',
                background: 'rgba(102, 126, 234, 0.1)',
                border: '1px solid rgba(102, 126, 234, 0.2)',
                color: '#667eea',
                padding: '10px',
                borderRadius: '6px',
                marginBottom: '8px',
                cursor: 'pointer',
                fontSize: '13px',
                textAlign: 'left'
              }}
              onClick={() => sendMessage('Add Golden Gate Bridge marker')}
            >
              📍 Add Golden Gate Bridge marker
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
