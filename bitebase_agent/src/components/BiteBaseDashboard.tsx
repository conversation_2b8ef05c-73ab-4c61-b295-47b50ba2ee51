'use client';

import React, { useCallback } from 'react';
import { useCoAgent, useCopilotAction } from "@copilotkit/react-core";
import { useRestaurantStore } from '../store/restaurantStore';
import { RestaurantAnalyticsState, RestaurantLocation, HotspotData } from '../types/restaurant';
import MapContainer from './Map/MapContainer';
import PlaceAnalytics from '../features/place/PlaceAnalytics';
import ProductAnalytics from '../features/product/ProductAnalytics';
import PriceAnalytics from '../features/price/PriceAnalytics';
import PromotionAnalytics from '../features/promotion/PromotionAnalytics';

interface BiteBaseDashboardProps {
  themeColor: string;
}

export default function BiteBaseDashboard({ themeColor }: BiteBaseDashboardProps) {
  const {
    activeModule,
    mapView,
    restaurants,
    customers,
    hotspots,
    selectedLocation,
    showHeatmap,
    showCompetitors,
    showHotspots,
    isLoading,
    aiInsights,
    lastAIAction,
    setActiveModule,
    setMapView,
    toggleLayer,
    selectLocation,
    addAIInsight,
    setLastAIAction,
    updateHotspots,
    setLoading
  } = useRestaurantStore();

  // 🪁 Shared State with AI Agent
  const { state, setState } = useCoAgent<RestaurantAnalyticsState>({
    name: "restaurantAnalyticsAgent",
    initialState: {
      mapView,
      restaurants,
      customers,
      hotspots,
      selectedLocation,
      activeModule,
      filters: {
        timeRange: {
          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString(),
          preset: 'week'
        }
      },
      menuItems: [],
      isLoading,
      showHeatmap,
      showCompetitors,
      showHotspots,
      aiInsights
    }
  });

  // 🪁 Frontend Actions for AI Integration
  useCopilotAction({
    name: "switchAnalyticsModule",
    description: "Switch between different analytics modules (place, product, price, promotion).",
    parameters: [{
      name: "module",
      description: "The analytics module to switch to",
      type: "string",
      enum: ["place", "product", "price", "promotion"],
      required: true,
    }],
    handler: ({ module }) => {
      setActiveModule(module as any);
      setState({ ...state, activeModule: module as any });
      addAIInsight(`📊 Switched to ${module.charAt(0).toUpperCase() + module.slice(1)} Analytics module`);
      setLastAIAction(`Switched to ${module} analytics`);
    },
  });

  useCopilotAction({
    name: "toggleMapLayer",
    description: "Toggle visibility of map layers (heatmap, competitors, hotspots).",
    parameters: [{
      name: "layer",
      description: "The map layer to toggle",
      type: "string",
      enum: ["heatmap", "competitors", "hotspots"],
      required: true,
    }],
    handler: ({ layer }) => {
      toggleLayer(layer as any);
      const newState = { ...state };
      if (layer === 'heatmap') newState.showHeatmap = !showHeatmap;
      if (layer === 'competitors') newState.showCompetitors = !showCompetitors;
      if (layer === 'hotspots') newState.showHotspots = !showHotspots;
      setState(newState);
      addAIInsight(`🗺️ Toggled ${layer} layer ${layer === 'heatmap' ? (showHeatmap ? 'off' : 'on') : layer === 'competitors' ? (showCompetitors ? 'off' : 'on') : (showHotspots ? 'off' : 'on')}`);
      setLastAIAction(`Toggled ${layer} layer`);
    },
  });

  useCopilotAction({
    name: "selectRestaurantLocation",
    description: "Select a specific restaurant or competitor location on the map.",
    parameters: [{
      name: "locationName",
      description: "The name of the restaurant/location to select",
      type: "string",
      required: true,
    }],
    handler: ({ locationName }) => {
      const location = restaurants.find(r => 
        r.name.toLowerCase().includes(locationName.toLowerCase())
      );
      if (location) {
        selectLocation(location.id);
        setState({ ...state, selectedLocation: location.id });
        addAIInsight(`📍 Selected ${location.name} - ${location.type === 'own' ? 'Your restaurant' : 'Competitor'} with ${location.rating}⭐ rating`);
        setLastAIAction(`Selected ${location.name}`);
      } else {
        addAIInsight(`❌ Could not find location "${locationName}". Available locations: ${restaurants.map(r => r.name).join(', ')}`);
      }
    },
  });

  useCopilotAction({
    name: "analyzeMarketArea",
    description: "Analyze a specific market area and provide insights about competition and opportunities.",
    parameters: [{
      name: "analysis",
      description: "Type of market analysis to perform",
      type: "string",
      enum: ["competitor_density", "customer_hotspots", "market_gaps", "delivery_zones"],
      required: true,
    }],
    handler: ({ analysis }) => {
      setLoading(true);
      setLastAIAction(`Analyzing ${analysis.replace('_', ' ')}`);
      
      // Simulate analysis with realistic insights
      setTimeout(() => {
        let insight = "";
        switch (analysis) {
          case "competitor_density":
            const competitorCount = restaurants.filter(r => r.type === 'competitor').length;
            insight = `🏢 Competitor Analysis: Found ${competitorCount} competitors in the area. Market saturation is ${competitorCount > 3 ? 'high' : 'moderate'}. Consider differentiation strategies.`;
            break;
          case "customer_hotspots":
            insight = `🔥 Customer Hotspots: Identified 3 high-density customer areas. Financial District shows 40% higher order values. Consider targeted marketing campaigns.`;
            // Generate sample hotspots
            const sampleHotspots: HotspotData[] = [
              {
                id: 'hotspot-1',
                geometry: {
                  type: 'Polygon',
                  coordinates: [[
                    [-122.425, 37.770],
                    [-122.420, 37.770],
                    [-122.420, 37.775],
                    [-122.425, 37.775],
                    [-122.425, 37.770]
                  ]]
                },
                properties: {
                  value: 85.5,
                  zScore: 2.8,
                  pValue: 0.005,
                  significance: 'hot',
                  metric: 'profitability'
                }
              }
            ];
            updateHotspots(sampleHotspots);
            toggleLayer('hotspots');
            break;
          case "market_gaps":
            insight = `🎯 Market Gaps: Identified underserved area in SOMA district. Low competitor density but high demographic match. Expansion opportunity score: 8.5/10.`;
            break;
          case "delivery_zones":
            insight = `🚚 Delivery Analysis: Optimal delivery radius is 2.5 miles. 15-minute delivery zones cover 78% of customer base. Consider micro-fulfillment centers.`;
            break;
        }
        addAIInsight(insight);
        setLoading(false);
      }, 2000);
    },
  });

  useCopilotAction({
    name: "centerMapOnLocation",
    description: "Center the map on a specific location or address.",
    parameters: [{
      name: "location",
      description: "Location name or address to center the map on",
      type: "string",
      required: true,
    }],
    handler: ({ location }) => {
      // For demo, center on San Francisco landmarks
      const landmarks: Record<string, { lat: number, lng: number }> = {
        'financial district': { lat: 37.7946, lng: -122.3999 },
        'union square': { lat: 37.7880, lng: -122.4074 },
        'north beach': { lat: 37.8067, lng: -122.4103 },
        'mission district': { lat: 37.7599, lng: -122.4148 },
        'soma': { lat: 37.7749, lng: -122.4194 }
      };
      
      const coords = landmarks[location.toLowerCase()] || { lat: 37.7749, lng: -122.4194 };
      setMapView({ latitude: coords.lat, longitude: coords.lng, zoom: 14 });
      setState({ ...state, mapView: { ...mapView, latitude: coords.lat, longitude: coords.lng, zoom: 14 } });
      addAIInsight(`🗺️ Centered map on ${location}`);
      setLastAIAction(`Centered map on ${location}`);
    },
  });

  const handleLocationSelect = useCallback((location: RestaurantLocation) => {
    setState({ ...state, selectedLocation: location.id });
  }, [state, setState]);

  const modules = [
    { id: 'place', name: 'Place', icon: '📍', color: 'blue' },
    { id: 'product', name: 'Product', icon: '🍽️', color: 'green' },
    { id: 'price', name: 'Price', icon: '💰', color: 'yellow' },
    { id: 'promotion', name: 'Promotion', icon: '📢', color: 'purple' }
  ];

  const getModuleColorClasses = (moduleId: string, isActive: boolean) => {
    const colors = {
      blue: isActive ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 hover:border-blue-300 text-gray-600',
      green: isActive ? 'border-green-500 bg-green-50 text-green-700' : 'border-gray-200 hover:border-green-300 text-gray-600',
      yellow: isActive ? 'border-yellow-500 bg-yellow-50 text-yellow-700' : 'border-gray-200 hover:border-yellow-300 text-gray-600',
      purple: isActive ? 'border-purple-500 bg-purple-50 text-purple-700' : 'border-gray-200 hover:border-purple-300 text-gray-600'
    };
    return colors[moduleId as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="h-screen w-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div 
        className="bg-white shadow-sm border-b px-6 py-4"
        style={{ background: `linear-gradient(135deg, ${themeColor} 0%, ${themeColor}dd 100%)` }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="text-2xl">🍽️</div>
            <div>
              <h1 className="text-xl font-bold text-white">BiteBase</h1>
              <p className="text-white/80 text-sm">AI-Powered Restaurant Market Research</p>
            </div>
          </div>
          
          {/* Status Indicator */}
          <div className="flex items-center gap-4">
            {isLoading && (
              <div className="flex items-center gap-2 bg-white/20 px-3 py-1 rounded-full">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <span className="text-white text-sm">Analyzing...</span>
              </div>
            )}
            <div className="bg-white/20 px-3 py-1 rounded-full">
              <span className="text-white text-sm">🔄 Real-time Sync Active</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Map Section */}
        <div className="flex-1 relative">
          <MapContainer onLocationSelect={handleLocationSelect} />
        </div>

        {/* Analytics Panel */}
        <div className="w-96 bg-white border-l flex flex-col">
          {/* Module Tabs */}
          <div className="border-b bg-gray-50 p-4">
            <h3 className="font-semibold mb-3 text-gray-800">Analytics Modules</h3>
            <div className="grid grid-cols-2 gap-2">
              {modules.map((module) => (
                <button
                  key={module.id}
                  onClick={() => {
                    setActiveModule(module.id as any);
                    setState({ ...state, activeModule: module.id as any });
                    setLastAIAction(`Switched to ${module.name} analytics`);
                  }}
                  className={`p-3 rounded-lg border-2 transition-all text-sm ${getModuleColorClasses(module.color, activeModule === module.id)}`}
                >
                  <div className="text-lg mb-1">{module.icon}</div>
                  <div className="font-medium">{module.name}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Analytics Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {activeModule === 'place' && <PlaceAnalytics />}
            {activeModule === 'product' && <ProductAnalytics />}
            {activeModule === 'price' && <PriceAnalytics />}
            {activeModule === 'promotion' && <PromotionAnalytics />}
          </div>

          {/* AI Insights Footer */}
          <div className="border-t bg-gray-50 p-4">
            <h4 className="font-medium text-sm mb-2 text-gray-800">🤖 Latest AI Insights</h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {aiInsights.slice(-3).map((insight, index) => (
                <div key={index} className="text-xs bg-white p-2 rounded border text-gray-700">
                  {insight}
                </div>
              ))}
            </div>
            {lastAIAction && (
              <div className="mt-2 text-xs text-gray-500">
                Last action: {lastAIAction}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}