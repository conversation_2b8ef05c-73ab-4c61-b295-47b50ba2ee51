"use client";

import React, { useState } from "react";
import { useRestaurantStore } from "@/store/restaurantStore";
import { AvailableAgents, getAgentConfig, getSpecializedAgents } from "@/lib/available-agents";
import AgentWorkflowVisualization from "@/components/agents/AgentWorkflowVisualization";
import { CoagentsProvider, useActiveAgent } from "@/components/coagents-provider";

// Import existing feature components
import PlaceAnalytics from "@/features/place/PlaceAnalytics";
import ProductAnalytics from "@/features/product/ProductAnalytics";
import PriceAnalytics from "@/features/price/PriceAnalytics";
import PromotionAnalytics from "@/features/promotion/PromotionAnalytics";

/**
 * Enhanced Restaurant Intelligence Dashboard
 * 
 * Features:
 * - Multi-agent orchestration with professional UI
 * - Real-time agent status visualization
 * - Seamless map-chat integration with shared state
 * - Professional workflow progress tracking
 * - Cross-agent synthesis and insights
 */
function EnhancedBiteBaseDashboardContent() {
  const {
    restaurants,
    customers,
    selectedLocation,
    showCompetitors,
    showHeatmap,
    showHotspots,
    addAIInsight
  } = useRestaurantStore();

  const [selectedAgent, setSelectedAgent] = useState<AvailableAgents>(AvailableAgents.RESTAURANT_ORCHESTRATOR);
  const [showWorkflow, setShowWorkflow] = useState(true);
  const activeAgent = useActiveAgent();

  const specializedAgents = getSpecializedAgents();

  const handleAgentSelect = (agentId: AvailableAgents) => {
    setSelectedAgent(agentId);
    addAIInsight(`🤖 Switched to ${getAgentConfig(agentId).name} agent`);
  };

  const renderAgentAnalytics = () => {
    switch (selectedAgent) {
      case AvailableAgents.PLACE_INTELLIGENCE:
        return <PlaceAnalytics />;
      case AvailableAgents.PRODUCT_INTELLIGENCE:
        return <ProductAnalytics />;
      case AvailableAgents.PRICE_INTELLIGENCE:
        return <PriceAnalytics />;
      case AvailableAgents.PROMOTION_INTELLIGENCE:
        return <PromotionAnalytics />;
      default:
        return <RestaurantOrchestratorDashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header with Agent Status */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xl">🍽️</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">BiteBase Intelligence</h1>
                  <p className="text-sm text-gray-600">Multi-Agent Restaurant Analytics Platform</p>
                </div>
              </div>
            </div>

            {/* Agent Status Indicator */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600">
                  Active: {getAgentConfig(activeAgent).name}
                </span>
              </div>
              
              {/* Workflow Toggle */}
              <button
                onClick={() => setShowWorkflow(!showWorkflow)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  showWorkflow 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {showWorkflow ? 'Hide' : 'Show'} Workflow
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Agent Selection Sidebar */}
          <div className="lg:col-span-1 space-y-4">
            {/* Agent Selector */}
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <h3 className="font-semibold mb-4">Intelligence Agents</h3>
              <div className="space-y-2">
                {/* Main Orchestrator */}
                <button
                  onClick={() => handleAgentSelect(AvailableAgents.RESTAURANT_ORCHESTRATOR)}
                  className={`w-full text-left p-3 rounded-lg border transition-all ${
                    selectedAgent === AvailableAgents.RESTAURANT_ORCHESTRATOR
                      ? 'border-purple-300 bg-purple-50'
                      : 'border-gray-200 hover:border-purple-200 hover:bg-purple-25'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center text-white text-sm">
                      🍽️
                    </div>
                    <div>
                      <div className="font-medium text-sm">Orchestrator</div>
                      <div className="text-xs text-gray-600">Main Controller</div>
                    </div>
                  </div>
                </button>

                {/* Specialized Agents */}
                {specializedAgents.map((agent) => (
                  <button
                    key={agent.id}
                    onClick={() => handleAgentSelect(agent.id)}
                    className={`w-full text-left p-3 rounded-lg border transition-all ${
                      selectedAgent === agent.id
                        ? 'border-blue-300 bg-blue-50'
                        : 'border-gray-200 hover:border-blue-200 hover:bg-blue-25'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 ${agent.color} rounded-lg flex items-center justify-center text-white text-sm`}>
                        {agent.icon}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{agent.name.replace(' Intelligence', '')}</div>
                        <div className="text-xs text-gray-600">{agent.description.slice(0, 30)}...</div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <h3 className="font-semibold mb-4">Quick Stats</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Restaurants</span>
                  <span className="font-semibold">{restaurants.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Customers</span>
                  <span className="font-semibold">{customers.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Active Layers</span>
                  <span className="font-semibold">
                    {[showCompetitors, showHeatmap, showHotspots].filter(Boolean).length}
                  </span>
                </div>
              </div>
            </div>

            {/* Agent Workflow Visualization */}
            {showWorkflow && (
              <AgentWorkflowVisualization 
                activeAgent={selectedAgent}
                isVisible={showWorkflow}
              />
            )}
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            {renderAgentAnalytics()}
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Restaurant Orchestrator Dashboard
 * Shows overview and cross-agent synthesis
 */
function RestaurantOrchestratorDashboard() {
  const specializedAgents = getSpecializedAgents();

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-2">Restaurant Intelligence Orchestrator</h2>
        <p className="text-purple-100">
          Coordinating four specialized AI agents to provide comprehensive restaurant business intelligence.
          Select an agent from the sidebar or use natural language queries in the chat.
        </p>
      </div>

      {/* Agent Overview Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {specializedAgents.map((agent) => (
          <div key={agent.id} className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-start gap-4">
              <div className={`w-12 h-12 ${agent.color} rounded-lg flex items-center justify-center text-white text-xl flex-shrink-0`}>
                {agent.icon}
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-2">{agent.name}</h3>
                <p className="text-gray-600 text-sm mb-4">{agent.description}</p>
                
                <div className="space-y-3">
                  <h4 className="font-medium text-sm">Key Capabilities:</h4>
                  <div className="grid grid-cols-1 gap-1">
                    {agent.capabilities.slice(0, 3).map((capability, index) => (
                      <div key={index} className="flex items-start gap-2 text-xs">
                        <div className="w-1 h-1 rounded-full bg-blue-500 mt-1.5 flex-shrink-0" />
                        <span className="text-gray-700">{capability}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Sample Queries */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-semibold text-lg mb-4">💡 Try These Intelligent Queries</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <span className="text-green-600">🍽️</span>
              Product Intelligence
            </h4>
            <div className="space-y-2">
              <div className="bg-gray-50 p-3 rounded text-sm">"Analyze my menu performance this month"</div>
              <div className="bg-gray-50 p-3 rounded text-sm">"Which dishes are underperforming?"</div>
              <div className="bg-gray-50 p-3 rounded text-sm">"Optimize pricing for pasta dishes"</div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <span className="text-blue-600">📍</span>
              Place Intelligence
            </h4>
            <div className="space-y-2">
              <div className="bg-gray-50 p-3 rounded text-sm">"Show competitor analysis in my area"</div>
              <div className="bg-gray-50 p-3 rounded text-sm">"Analyze customer density patterns"</div>
              <div className="bg-gray-50 p-3 rounded text-sm">"Optimize delivery routes"</div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <span className="text-yellow-600">💰</span>
              Price Intelligence
            </h4>
            <div className="space-y-2">
              <div className="bg-gray-50 p-3 rounded text-sm">"Forecast revenue for next quarter"</div>
              <div className="bg-gray-50 p-3 rounded text-sm">"Calculate break-even analysis"</div>
              <div className="bg-gray-50 p-3 rounded text-sm">"Analyze profit margins"</div>
            </div>
          </div>

          <div className="space-y-3">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <span className="text-pink-600">📢</span>
              Promotion Intelligence
            </h4>
            <div className="space-y-2">
              <div className="bg-gray-50 p-3 rounded text-sm">"Segment my customers by behavior"</div>
              <div className="bg-gray-50 p-3 rounded text-sm">"Evaluate campaign ROI"</div>
              <div className="bg-gray-50 p-3 rounded text-sm">"Predict customer churn"</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Main Enhanced Dashboard Component with CopilotKit Provider
 */
export default function EnhancedBiteBaseDashboard() {
  return (
    <CoagentsProvider>
      <EnhancedBiteBaseDashboardContent />
    </CoagentsProvider>
  );
}
