'use client';

import React, { useCallback, useMemo } from 'react';
import { Map } from 'react-map-gl';
import DeckGL from '@deck.gl/react';
import { ScatterplotLayer, HeatmapLayer, GeoJsonLayer } from '@deck.gl/layers';
import { useRestaurantStore } from '../../store/restaurantStore';
import { RestaurantLocation, CustomerDataPoint } from '../../types/restaurant';

// Mapbox access token - in production, use environment variable
const MAPBOX_ACCESS_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiYml0ZWJhc2UiLCJhIjoiY2x0ZXN0IiwiYSI6IkEifQ.test';

interface MapContainerProps {
  onLocationSelect?: (location: RestaurantLocation) => void;
}

export default function MapContainer({ onLocationSelect }: MapContainerProps) {
  const {
    mapView,
    restaurants,
    customers,
    hotspots,
    showHeatmap,
    showCompetitors,
    showHotspots,
    setMapView,
    selectLocation,
    addAIInsight,
    setLastAIAction
  } = useRestaurantStore();

  // Restaurant markers layer
  const restaurantLayer = useMemo(() => new ScatterplotLayer({
    id: 'restaurants',
    data: restaurants.filter(r => r.type === 'own' || showCompetitors),
    getPosition: (d: RestaurantLocation) => [d.lng, d.lat],
    getRadius: (d: RestaurantLocation) => d.isSelected ? 200 : 150,
    getFillColor: (d: RestaurantLocation) => {
      if (d.type === 'own') return [34, 139, 34, 200]; // Green for own restaurants
      return d.isSelected ? [255, 165, 0, 200] : [220, 20, 60, 180]; // Orange/Red for competitors
    },
    getLineColor: [255, 255, 255, 255],
    getLineWidth: 2,
    pickable: true,
    onClick: (info) => {
      if (info.object) {
        const location = info.object as RestaurantLocation;
        selectLocation(location.id);
        onLocationSelect?.(location);
        setLastAIAction(`Selected ${location.name} (${location.type})`);
        addAIInsight(`📍 Selected ${location.name}. ${location.type === 'own' ? 'This is your restaurant.' : `Competitor with ${location.rating}⭐ rating and ${location.priceRange} price range.`}`);
      }
    }
  }), [restaurants, showCompetitors, selectLocation, onLocationSelect, setLastAIAction, addAIInsight]);

  // Customer heatmap layer
  const heatmapLayer = useMemo(() => showHeatmap ? new HeatmapLayer({
    id: 'customer-heatmap',
    data: customers,
    getPosition: (d: CustomerDataPoint) => [d.lng, d.lat],
    getWeight: (d: CustomerDataPoint) => d.orderValue,
    radiusPixels: 60,
    intensity: 1,
    threshold: 0.03,
    colorRange: [
      [255, 255, 178, 25],
      [254, 204, 92, 85],
      [253, 141, 60, 127],
      [240, 59, 32, 170],
      [189, 0, 38, 255]
    ]
  }) : null, [customers, showHeatmap]);

  // Hotspots layer (statistical analysis results)
  const hotspotsLayer = useMemo(() => showHotspots && hotspots.length > 0 ? new GeoJsonLayer({
    id: 'hotspots',
    data: {
      type: 'FeatureCollection',
      features: hotspots.map(h => ({
        type: 'Feature',
        geometry: h.geometry,
        properties: h.properties
      }))
    },
    getFillColor: (d: any) => {
      const significance = d.properties.significance;
      const zScore = Math.abs(d.properties.zScore);
      const alpha = Math.min(255, zScore * 50);
      
      if (significance === 'hot') return [255, 0, 0, alpha];
      if (significance === 'cold') return [0, 0, 255, alpha];
      return [128, 128, 128, 50];
    },
    getLineColor: [255, 255, 255, 100],
    getLineWidth: 2,
    pickable: true,
    onClick: (info) => {
      if (info.object) {
        const props = info.object.properties;
        addAIInsight(`🔥 ${props.significance === 'hot' ? 'Hotspot' : props.significance === 'cold' ? 'Cold spot' : 'Neutral area'} detected! Z-score: ${props.zScore.toFixed(2)}, Metric: ${props.metric}`);
      }
    }
  }) : null, [hotspots, showHotspots, addAIInsight]);

  // Combine all layers
  const layers = useMemo(() => {
    const allLayers = [restaurantLayer];
    if (heatmapLayer) allLayers.push(heatmapLayer);
    if (hotspotsLayer) allLayers.push(hotspotsLayer);
    return allLayers;
  }, [restaurantLayer, heatmapLayer, hotspotsLayer]);

  const handleViewStateChange = useCallback(({ viewState }: any) => {
    setMapView(viewState);
    setLastAIAction(`Map view changed - Zoom: ${viewState.zoom.toFixed(1)}, Center: ${viewState.latitude.toFixed(4)}, ${viewState.longitude.toFixed(4)}`);
  }, [setMapView, setLastAIAction]);

  const handleMapClick = useCallback((info: any) => {
    if (!info.object && info.coordinate) {
      const [lng, lat] = info.coordinate;
      addAIInsight(`📍 Clicked on map at coordinates: ${lat.toFixed(4)}, ${lng.toFixed(4)}`);
      setLastAIAction(`Map clicked at ${lat.toFixed(4)}, ${lng.toFixed(4)}`);
    }
  }, [addAIInsight, setLastAIAction]);

  return (
    <div className="relative w-full h-full">
      <DeckGL
        viewState={mapView}
        onViewStateChange={handleViewStateChange}
        controller={true}
        layers={layers}
        onClick={handleMapClick}
        getCursor={() => 'crosshair'}
      >
        <Map
          mapboxAccessToken={MAPBOX_ACCESS_TOKEN}
          mapStyle="mapbox://styles/mapbox/light-v11"
          attributionControl={false}
        />
      </DeckGL>
      
      {/* Map Legend */}
      <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <h4 className="font-semibold text-sm mb-2">Map Legend</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-600"></div>
            <span>Your Restaurants</span>
          </div>
          {showCompetitors && (
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-red-600"></div>
              <span>Competitors</span>
            </div>
          )}
          {showHeatmap && (
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-gradient-to-r from-yellow-300 to-red-600"></div>
              <span>Customer Density</span>
            </div>
          )}
          {showHotspots && (
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-blue-600"></div>
              <span>Statistical Hotspots</span>
            </div>
          )}
        </div>
      </div>

      {/* Map Info */}
      <div className="absolute top-4 right-4 bg-black/80 text-white px-3 py-2 rounded-lg text-sm font-mono">
        📍 {restaurants.filter(r => r.type === 'own').length} restaurants | 
        🏢 {showCompetitors ? restaurants.filter(r => r.type === 'competitor').length : 0} competitors | 
        👥 {customers.length} customers | 
        🔍 Zoom: {mapView.zoom.toFixed(1)}
      </div>
    </div>
  );
}