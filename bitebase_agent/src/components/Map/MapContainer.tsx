'use client';

import React, { useCallback, useMemo } from 'react';
import { Map } from 'react-map-gl';
import DeckGL from '@deck.gl/react';
import { ScatterplotLayer } from '@deck.gl/layers';
import { useRestaurantStore } from '../../store/restaurantStore';
import { RestaurantLocation, CustomerDataPoint } from '../../types/restaurant';

// Mapbox access token - in production, use environment variable
const MAPBOX_ACCESS_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiYml0ZWJhc2UiLCJhIjoiY2x0ZXN0IiwiYSI6IkEifQ.test';

interface MapContainerProps {
  onLocationSelect?: (location: RestaurantLocation) => void;
  onMapClick?: (coordinates: { lat: number, lng: number }) => void;
}

export default function MapContainer({ onLocationSelect, onMapClick }: MapContainerProps) {
  const {
    mapView,
    restaurants,
    customers,
    hotspots,
    showHeatmap,
    showCompetitors,
    showHotspots,
    setMapView,
    selectLocation,
    addAIInsight,
    setLastAIAction
  } = useRestaurantStore();

  // Restaurant markers layer
  const restaurantLayer = useMemo(() => new ScatterplotLayer({
    id: 'restaurants',
    data: restaurants.filter(r => r.type === 'own' || showCompetitors),
    getPosition: (d: RestaurantLocation) => [d.lng, d.lat],
    getRadius: (d: RestaurantLocation) => d.isSelected ? 200 : 150,
    getFillColor: (d: RestaurantLocation) => {
      if (d.type === 'own') return [34, 139, 34, 200]; // Green for own restaurants
      return d.isSelected ? [255, 165, 0, 200] : [220, 20, 60, 180]; // Orange/Red for competitors
    },
    getLineColor: [255, 255, 255, 255],
    getLineWidth: 2,
    pickable: true,
    onClick: (info: any) => {
      if (info.object) {
        const location = info.object as RestaurantLocation;
        selectLocation(location.id);
        onLocationSelect?.(location);
        setLastAIAction(`Selected ${location.name} (${location.type})`);
        addAIInsight(`📍 Selected ${location.name}. ${location.type === 'own' ? 'This is your restaurant.' : `Competitor with ${location.rating}⭐ rating and ${location.priceRange} price range.`}`);
      }
    }
  }), [restaurants, showCompetitors, selectLocation, onLocationSelect, setLastAIAction, addAIInsight]);

  // Customer density layer (using ScatterplotLayer for simplicity)
  const heatmapLayer = useMemo(() => showHeatmap ? new ScatterplotLayer({
    id: 'customer-density',
    data: customers,
    getPosition: (d: CustomerDataPoint) => [d.lng, d.lat],
    getRadius: (d: CustomerDataPoint) => Math.max(50, d.orderValue * 2),
    getFillColor: (d: CustomerDataPoint) => [255, 165, 0, Math.min(200, d.orderValue * 5)],
    getLineColor: [255, 255, 255, 100],
    getLineWidth: 1,
    pickable: true
  }) : null, [customers, showHeatmap]);

  // Hotspots layer (statistical analysis results) - simplified for demo
  const hotspotsLayer = useMemo(() => showHotspots && hotspots.length > 0 ? new ScatterplotLayer({
    id: 'hotspots',
    data: hotspots.map(h => ({
      id: h.id,
      lat: h.geometry.coordinates[0][0][1], // Extract lat from polygon coordinates
      lng: h.geometry.coordinates[0][0][0], // Extract lng from polygon coordinates
      properties: h.properties
    })),
    getPosition: (d: any) => [d.lng, d.lat],
    getRadius: 300,
    getFillColor: (d: any) => {
      const significance = d.properties.significance;
      if (significance === 'hot') return [255, 0, 0, 150];
      if (significance === 'cold') return [0, 0, 255, 150];
      return [128, 128, 128, 100];
    },
    getLineColor: [255, 255, 255, 100],
    getLineWidth: 2,
    pickable: true,
    onClick: (info: any) => {
      if (info.object) {
        const props = info.object.properties;
        addAIInsight(`🔥 ${props.significance === 'hot' ? 'Hotspot' : props.significance === 'cold' ? 'Cold spot' : 'Neutral area'} detected! Z-score: ${props.zScore.toFixed(2)}, Metric: ${props.metric}`);
      }
    }
  }) : null, [hotspots, showHotspots, addAIInsight]);

  // Combine all layers
  const layers = useMemo(() => {
    const allLayers: any[] = [restaurantLayer];
    if (heatmapLayer) allLayers.push(heatmapLayer);
    if (hotspotsLayer) allLayers.push(hotspotsLayer);
    return allLayers;
  }, [restaurantLayer, heatmapLayer, hotspotsLayer]);

  const handleViewStateChange = useCallback(({ viewState }: { viewState: any }) => {
    setMapView(viewState);
    setLastAIAction(`Map view changed - Zoom: ${viewState.zoom.toFixed(1)}, Center: ${viewState.latitude.toFixed(4)}, ${viewState.longitude.toFixed(4)}`);
  }, [setMapView, setLastAIAction]);

  const handleMapClick = useCallback((info: any) => {
    if (!info.object && info.coordinate) {
      const [lng, lat] = info.coordinate;
      addAIInsight(`📍 Clicked on map at coordinates: ${lat.toFixed(4)}, ${lng.toFixed(4)}`);
      setLastAIAction(`Map clicked at ${lat.toFixed(4)}, ${lng.toFixed(4)}`);
      
      // Call the callback to add marker in dashboard
      onMapClick?.({ lat, lng });
    }
  }, [addAIInsight, setLastAIAction, onMapClick]);

  return (
    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
      <DeckGL
        viewState={mapView}
        onViewStateChange={handleViewStateChange}
        controller={true}
        layers={layers}
        onClick={handleMapClick}
        getCursor={() => 'crosshair'}
      >
        <Map
          mapboxAccessToken={MAPBOX_ACCESS_TOKEN}
          mapStyle="mapbox://styles/mapbox/light-v11"
          attributionControl={false}
        />
      </DeckGL>
    </div>
  );
}
