"use client";

import React from "react";
import { useRestaurantStore } from "@/store/restaurantStore";

/**
 * Simplified BiteBase Restaurant Intelligence Dashboard
 * Clean, consistent theme with easy readability
 */
export default function SimpleBiteBaseDashboard() {
  const {
    restaurants,
    customers,
    selectedLocation,
    showCompetitors,
    showHeatmap,
    showHotspots,
    toggleLayer,
    addAIInsight
  } = useRestaurantStore();

  const toggleCompetitors = () => toggleLayer('competitors');
  const toggleHeatmap = () => toggleLayer('heatmap');
  const toggleHotspots = () => toggleLayer('hotspots');

  const handleAgentAnalysis = (agentType: string) => {
    addAIInsight(`🤖 Starting ${agentType} analysis...`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Clean Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-2xl">🍽️</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">BiteBase Intelligence</h1>
                <p className="text-gray-600">Restaurant Analytics Platform</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">System Active</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Restaurants</p>
                <p className="text-2xl font-bold text-gray-900">{restaurants.length}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-xl">🏪</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Customers</p>
                <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-xl">👥</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Layers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {[showCompetitors, showHeatmap, showHotspots].filter(Boolean).length}
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 text-xl">📊</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Location</p>
                <p className="text-sm font-bold text-gray-900">
                  {selectedLocation || "Select Area"}
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <span className="text-orange-600 text-xl">📍</span>
              </div>
            </div>
          </div>
        </div>

        {/* Map Controls */}
        <div className="bg-white rounded-lg p-6 shadow-sm border mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Map Visualization</h3>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={toggleCompetitors}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                showCompetitors
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              🏢 Competitors {showCompetitors && '✓'}
            </button>
            
            <button
              onClick={toggleHeatmap}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                showHeatmap
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              🔥 Customer Heatmap {showHeatmap && '✓'}
            </button>
            
            <button
              onClick={toggleHotspots}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                showHotspots
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              📍 Delivery Hotspots {showHotspots && '✓'}
            </button>
          </div>
        </div>

        {/* Intelligence Agents */}
        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">AI Intelligence Agents</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Place Intelligence */}
            <div className="border rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-blue-600 text-xl">📍</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-2">Place Intelligence</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Analyze competitor locations, customer density, and optimal delivery zones.
                  </p>
                  <button
                    onClick={() => handleAgentAnalysis('Place Intelligence')}
                    className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Analyze Location Data
                  </button>
                </div>
              </div>
            </div>

            {/* Product Intelligence */}
            <div className="border rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-green-600 text-xl">🍽️</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-2">Product Intelligence</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Menu performance analysis, pricing optimization, and item recommendations.
                  </p>
                  <button
                    onClick={() => handleAgentAnalysis('Product Intelligence')}
                    className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Analyze Menu Performance
                  </button>
                </div>
              </div>
            </div>

            {/* Price Intelligence */}
            <div className="border rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-yellow-600 text-xl">💰</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-2">Price Intelligence</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Revenue forecasting, break-even analysis, and financial modeling.
                  </p>
                  <button
                    onClick={() => handleAgentAnalysis('Price Intelligence')}
                    className="w-full bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors"
                  >
                    Analyze Financial Data
                  </button>
                </div>
              </div>
            </div>

            {/* Promotion Intelligence */}
            <div className="border rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-pink-600 text-xl">📢</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-2">Promotion Intelligence</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Customer segmentation, campaign ROI analysis, and marketing insights.
                  </p>
                  <button
                    onClick={() => handleAgentAnalysis('Promotion Intelligence')}
                    className="w-full bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 transition-colors"
                  >
                    Analyze Customer Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
          <h3 className="text-xl font-semibold mb-4">🚀 Quick Start Guide</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">1. 📊 View Your Data</h4>
              <p className="opacity-90">Toggle map layers to see competitors, customer density, and delivery zones.</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">2. 🤖 Run Analysis</h4>
              <p className="opacity-90">Click any intelligence agent to start analyzing your restaurant data.</p>
            </div>
            <div>
              <h4 className="font-medium mb-2">3. 💬 Chat with AI</h4>
              <p className="opacity-90">Use natural language to ask questions about your business performance.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
