"use client";

import React from "react";
import { AvailableAgents, getAgentConfig } from "@/lib/available-agents";
import { AgentStatus, AgentWorkflowStage, useAgentStatus, useAnalysisPipeline } from "@/components/coagents-provider";

interface AgentWorkflowVisualizationProps {
  activeAgent: AvailableAgents;
  isVisible?: boolean;
}

/**
 * Professional Agent Workflow Visualization
 * 
 * Features:
 * - Real-time agent status with progress indicators
 * - Step-by-step workflow visualization
 * - Confidence scores and completion estimates
 * - Professional loading states and animations
 */
export function AgentWorkflowVisualization({ 
  activeAgent, 
  isVisible = true 
}: AgentWorkflowVisualizationProps) {
  const agentStatus = useAgentStatus();
  const analysisPipeline = useAnalysisPipeline();
  const agentConfig = getAgentConfig(activeAgent);

  if (!isVisible) return null;

  const formatDuration = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    
    if (diffSecs < 60) return `${diffSecs}s`;
    const diffMins = Math.floor(diffSecs / 60);
    return `${diffMins}m ${diffSecs % 60}s`;
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 90) return "text-green-600 bg-green-100";
    if (score >= 75) return "text-blue-600 bg-blue-100";
    if (score >= 60) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getStatusIcon = (status: AgentWorkflowStage['status']) => {
    switch (status) {
      case 'pending': return <div className="w-3 h-3 rounded-full bg-gray-300" />;
      case 'in_progress': return (
        <div className="w-3 h-3 rounded-full bg-blue-500 animate-pulse" />
      );
      case 'completed': return <div className="w-3 h-3 rounded-full bg-green-500" />;
      case 'failed': return <div className="w-3 h-3 rounded-full bg-red-500" />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-4 space-y-4">
      {/* Agent Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`w-10 h-10 rounded-lg ${agentConfig.color} flex items-center justify-center text-white text-lg`}>
            {agentConfig.icon}
          </div>
          <div>
            <h3 className="font-semibold text-lg">{agentConfig.name}</h3>
            <p className="text-sm text-gray-600">{agentConfig.description}</p>
          </div>
        </div>
        
        {/* Agent Status Badge */}
        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
          agentStatus.isProcessing 
            ? 'bg-blue-100 text-blue-800' 
            : 'bg-green-100 text-green-800'
        }`}>
          {agentStatus.isProcessing ? 'Processing' : 'Ready'}
        </div>
      </div>

      {/* Current Step Progress */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-gray-900">Current Step</h4>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">
              {formatDuration(agentStatus.startTime)}
            </span>
            <div className={`px-2 py-1 rounded text-xs font-medium ${getConfidenceColor(agentStatus.confidenceScore)}`}>
              {agentStatus.confidenceScore}% confidence
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">{agentStatus.currentStep}</span>
            <span className="text-xs text-gray-500">
              {agentStatus.stepProgress}/{agentStatus.totalSteps}
            </span>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                agentStatus.isProcessing 
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-500' 
                  : 'bg-gradient-to-r from-green-500 to-emerald-500'
              }`}
              style={{ 
                width: `${Math.max(5, (agentStatus.stepProgress / agentStatus.totalSteps) * 100)}%` 
              }}
            />
          </div>
        </div>

        {/* Last Action */}
        <div className="text-sm text-gray-600">
          <span className="font-medium">Last Action:</span> {agentStatus.lastAction}
        </div>

        {/* Estimated Completion */}
        {agentStatus.estimatedCompletion && (
          <div className="text-sm text-gray-600">
            <span className="font-medium">Estimated Completion:</span> {agentStatus.estimatedCompletion}
          </div>
        )}
      </div>

      {/* Analysis Pipeline Stages */}
      {analysisPipeline.stages.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Analysis Pipeline</h4>
          <div className="space-y-2">
            {analysisPipeline.stages.map((stage, index) => (
              <div 
                key={stage.id} 
                className={`flex items-center gap-3 p-3 rounded-lg border transition-all ${
                  index === analysisPipeline.currentStageIndex 
                    ? 'border-blue-300 bg-blue-50' 
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                {/* Status Icon */}
                {getStatusIcon(stage.status)}
                
                {/* Stage Info */}
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-sm">{stage.name}</span>
                    <div className="flex items-center gap-2">
                      {stage.confidenceScore && (
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getConfidenceColor(stage.confidenceScore)}`}>
                          {stage.confidenceScore}%
                        </span>
                      )}
                      {stage.startTime && stage.endTime && (
                        <span className="text-xs text-gray-500">
                          {formatDuration(stage.startTime)}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Agent Badge */}
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs text-gray-600">Agent:</span>
                    <span className="text-xs font-medium text-blue-600">{stage.agent}</span>
                  </div>
                  
                  {/* Error Message */}
                  {stage.error && (
                    <div className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded">
                      {stage.error}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Agent Capabilities */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Agent Capabilities</h4>
        <div className="grid grid-cols-1 gap-2">
          {agentConfig.capabilities.map((capability, index) => (
            <div key={index} className="flex items-start gap-2 text-sm">
              <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0" />
              <span className="text-gray-700">{capability}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Tools Available */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Available Tools</h4>
        <div className="flex flex-wrap gap-2">
          {agentConfig.tools.map((tool, index) => (
            <span 
              key={index}
              className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs font-medium"
            >
              {tool}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}

export default AgentWorkflowVisualization;
