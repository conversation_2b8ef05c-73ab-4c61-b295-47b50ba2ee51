"use client";

import { CopilotKit } from "@copilotkit/react-core";
import { CopilotSidebar } from "@copilotkit/react-ui";
import { ReactNode } from "react";
import { AvailableAgents } from "@/lib/available-agents";

interface CoagentsProviderProps {
  children: ReactNode;
}

/**
 * Enhanced CopilotKit Provider for Restaurant Intelligence Multi-Agent System
 * 
 * Provides:
 * - Multi-agent state management
 * - Real-time agent status tracking
 * - Cross-agent data sharing
 * - Professional workflow visualization
 */
export function CoagentsProvider({ children }: CoagentsProviderProps) {
  const runtimeUrl = process.env.NODE_ENV === "development" 
    ? "http://localhost:8000/copilotkit" 
    : "/api/copilotkit";

  return (
    <CopilotKit 
      runtimeUrl={runtimeUrl}
      // Enhanced CopilotKit configuration for multi-agent system
      showDevConsole={process.env.NODE_ENV === "development"}
    >
      <CopilotSidebar
        defaultOpen={false}
        clickOutsideToClose={false}
        className="restaurant-intelligence-sidebar"
        // Custom instructions for restaurant intelligence
        instructions={`
You are the Restaurant Intelligence Multi-Agent System coordinating four specialized AI agents for comprehensive restaurant analytics:

🍽️ **RESTAURANT INTELLIGENCE AGENTS:**

**🏢 PLACE Intelligence** - Location & Market Analysis
• Customer density mapping and demographic analysis
• Competitor landscape assessment and market positioning  
• Delivery optimization and coverage analysis
• Site selection and expansion planning

**🍽️ PRODUCT Intelligence** - Menu & Performance Analysis
• BCG menu engineering matrix (Stars, Dogs, Puzzles, Plowhorses)
• Item popularity and profitability analysis
• Menu optimization and pricing strategy
• Recipe cost analysis and margin optimization

**💰 PRICE Intelligence** - Revenue & Financial Analysis
• Revenue forecasting and scenario planning
• Break-even analysis and cost structure optimization
• Dynamic pricing models and elasticity analysis
• Financial performance benchmarking

**📢 PROMOTION Intelligence** - Marketing & Customer Analytics
• RFM customer segmentation (Recency, Frequency, Monetary)
• Campaign ROI analysis and marketing attribution
• Customer lifetime value (LTV) and acquisition cost (CAC)
• Retention analysis and churn prediction

**INTELLIGENT ROUTING:**
I automatically route your queries to the most appropriate specialized agent based on keywords and intent:

- **"menu performance"** → Product Intelligence
- **"competitor analysis"** → Place Intelligence  
- **"revenue forecast"** → Price Intelligence
- **"customer segments"** → Promotion Intelligence

**PROFESSIONAL FEATURES:**
• Real-time agent status with progress indicators
• Confidence scores (0-100%) for all insights
• Cross-domain impact analysis
• Actionable recommendations with next steps
• Export capabilities for reports and insights

Just ask natural questions like:
- "Analyze my menu performance this month"
- "What's the competitor landscape in my area?"
- "Forecast revenue for next quarter"
- "Show me customer segmentation analysis"

I'll route your request to the right specialist and provide comprehensive, actionable business intelligence!
        `}
        labels={{
          title: "🍽️ Restaurant Intelligence",
          initial: "Welcome to your Restaurant Intelligence System! I coordinate four specialized agents to provide comprehensive business insights. What would you like to analyze?",
        }}
      >
        {children}
      </CopilotSidebar>
    </CopilotKit>
  );
}

/**
 * Multi-Agent State Management Hooks
 * Provides typed access to agent-specific state
 */

export interface AgentStatus {
  currentStep: string;
  stepProgress: number;
  totalSteps: number;
  isProcessing: boolean;
  lastAction: string;
  startTime: string;
  estimatedCompletion?: string;
  confidenceScore: number;
}

export interface AgentWorkflowStage {
  id: string;
  name: string;
  agent: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  result?: any;
  error?: string;
  confidenceScore?: number;
}

export interface RestaurantContext {
  locations: any[];
  menuItems: any[];
  customers: any[];
  campaigns: any[];
  financials: any[];
}

export interface AnalysisResults {
  place?: any;
  product?: any;
  price?: any;
  promotion?: any;
  synthesis?: any;
}

// Multi-agent state interface for type safety
export interface MultiAgentState {
  activeAgent: AvailableAgents;
  agentStatus: AgentStatus;
  analysisPipeline: {
    stages: AgentWorkflowStage[];
    currentStageIndex: number;
  };
  restaurantContext: RestaurantContext;
  analysisResults: AnalysisResults;
}

/**
 * Custom hooks for accessing multi-agent state
 * These will be implemented to work with CopilotKit's state management
 */

// Hook to get current active agent
export const useActiveAgent = () => {
  // TODO: Implement with CopilotKit state management
  return AvailableAgents.RESTAURANT_ORCHESTRATOR;
};

// Hook to get agent status with real-time updates
export const useAgentStatus = () => {
  // TODO: Implement with CopilotKit state management
  return {
    currentStep: "Ready for analysis",
    stepProgress: 0,
    totalSteps: 1,
    isProcessing: false,
    lastAction: "System initialized",
    startTime: new Date().toISOString(),
    confidenceScore: 100,
  };
};

// Hook to get analysis pipeline status
export const useAnalysisPipeline = () => {
  // TODO: Implement with CopilotKit state management
  return {
    stages: [],
    currentStageIndex: 0,
  };
};

// Hook to get restaurant context data
export const useRestaurantContext = () => {
  // TODO: Implement with CopilotKit state management
  return {
    locations: [],
    menuItems: [],
    customers: [],
    campaigns: [],
    financials: [],
  };
};

// Hook to get analysis results by domain
export const useAnalysisResults = () => {
  // TODO: Implement with CopilotKit state management
  return {};
};
