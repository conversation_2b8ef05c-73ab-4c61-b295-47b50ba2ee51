'use client';

import React from 'react';
import { useRestaurantStore } from '../../store/restaurantStore';

export default function PlaceAnalytics() {
  const { 
    restaurants, 
    customers, 
    selectedLocation, 
    showCompetitors, 
    showHeatmap, 
    showHotspots,
    toggleLayer,
    addAIInsight 
  } = useRestaurantStore();

  const ownRestaurants = restaurants.filter(r => r.type === 'own');
  const competitors = restaurants.filter(r => r.type === 'competitor');
  const selectedRestaurant = restaurants.find(r => r.id === selectedLocation);

  const handleLayerToggle = (layer: 'heatmap' | 'competitors' | 'hotspots') => {
    toggleLayer(layer);
    addAIInsight(`🗺️ ${layer} layer ${layer === 'heatmap' ? (showHeatmap ? 'hidden' : 'shown') : 
      layer === 'competitors' ? (showCompetitors ? 'hidden' : 'shown') : 
      (showHotspots ? 'hidden' : 'shown')}`);
  };

  const customerSegmentCounts = customers.reduce((acc, customer) => {
    acc[customer.customerSegment] = (acc[customer.customerSegment] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="space-y-6">
      {/* Place Analytics Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
        <h2 className="text-xl font-bold text-blue-900 mb-2">📍 Place Analytics</h2>
        <p className="text-blue-700 text-sm">
          Geospatial market intelligence and competitive landscape analysis
        </p>
      </div>

      {/* Map Controls */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <h3 className="font-semibold mb-3">🗺️ Map Layers</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <button
            onClick={() => handleLayerToggle('heatmap')}
            className={`p-3 rounded-lg border-2 transition-all ${
              showHeatmap 
                ? 'border-orange-300 bg-orange-50 text-orange-800' 
                : 'border-gray-200 bg-gray-50 text-gray-600 hover:border-orange-200'
            }`}
          >
            <div className="text-lg mb-1">🔥</div>
            <div className="font-medium text-sm">Customer Heatmap</div>
            <div className="text-xs opacity-75">Order density visualization</div>
          </button>

          <button
            onClick={() => handleLayerToggle('competitors')}
            className={`p-3 rounded-lg border-2 transition-all ${
              showCompetitors 
                ? 'border-red-300 bg-red-50 text-red-800' 
                : 'border-gray-200 bg-gray-50 text-gray-600 hover:border-red-200'
            }`}
          >
            <div className="text-lg mb-1">🏢</div>
            <div className="font-medium text-sm">Competitors</div>
            <div className="text-xs opacity-75">{competitors.length} nearby restaurants</div>
          </button>

          <button
            onClick={() => handleLayerToggle('hotspots')}
            className={`p-3 rounded-lg border-2 transition-all ${
              showHotspots 
                ? 'border-purple-300 bg-purple-50 text-purple-800' 
                : 'border-gray-200 bg-gray-50 text-gray-600 hover:border-purple-200'
            }`}
          >
            <div className="text-lg mb-1">⚡</div>
            <div className="font-medium text-sm">Statistical Hotspots</div>
            <div className="text-xs opacity-75">Getis-Ord Gi* analysis</div>
          </button>
        </div>
      </div>

      {/* Location Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <span className="text-green-600">🏪</span>
            Your Restaurants
          </h3>
          <div className="space-y-2">
            {ownRestaurants.map(restaurant => (
              <div 
                key={restaurant.id}
                className={`p-3 rounded-lg border transition-all ${
                  restaurant.isSelected 
                    ? 'border-green-300 bg-green-50' 
                    : 'border-gray-200 hover:border-green-200'
                }`}
              >
                <div className="font-medium">{restaurant.name}</div>
                <div className="text-sm text-gray-600">{restaurant.address}</div>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                    {restaurant.cuisine}
                  </span>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {restaurant.priceRange}
                  </span>
                  <span className="text-xs text-yellow-600">
                    ⭐ {restaurant.rating}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <span className="text-blue-600">👥</span>
            Customer Distribution
          </h3>
          <div className="space-y-3">
            {Object.entries(customerSegmentCounts).map(([segment, count]) => {
              const percentage = ((count / customers.length) * 100).toFixed(1);
              const colors = {
                new: 'bg-green-500',
                regular: 'bg-blue-500',
                vip: 'bg-purple-500',
                'at-risk': 'bg-red-500'
              };
              
              return (
                <div key={segment} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${colors[segment as keyof typeof colors]}`}></div>
                    <span className="capitalize text-sm font-medium">{segment.replace('-', ' ')}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-semibold">{count}</div>
                    <div className="text-xs text-gray-500">{percentage}%</div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Selected Location Details */}
      {selectedRestaurant && (
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <span className="text-orange-600">📍</span>
            Selected Location: {selectedRestaurant.name}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {selectedRestaurant.type === 'own' ? customers.length : 'N/A'}
              </div>
              <div className="text-sm text-gray-600">Total Customers</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {competitors.length}
              </div>
              <div className="text-sm text-gray-600">Nearby Competitors</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {selectedRestaurant.rating}⭐
              </div>
              <div className="text-sm text-gray-600">Rating</div>
            </div>
          </div>
          
          {selectedRestaurant.type === 'competitor' && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="font-medium text-yellow-800 mb-2">🔍 Competitor Analysis</h4>
              <div className="text-sm text-yellow-700">
                <p><strong>Price Range:</strong> {selectedRestaurant.priceRange}</p>
                <p><strong>Cuisine:</strong> {selectedRestaurant.cuisine}</p>
                <p><strong>Rating:</strong> {selectedRestaurant.rating}⭐</p>
                <p className="mt-2 text-xs">
                  💡 This competitor has a {selectedRestaurant.rating && selectedRestaurant.rating > 4.5 ? 'high' : 'moderate'} rating 
                  and operates in the {selectedRestaurant.priceRange} price segment.
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}