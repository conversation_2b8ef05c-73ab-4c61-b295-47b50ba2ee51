'use client';

import React from 'react';
import { ResponsiveBar } from '@nivo/bar';
import { ResponsiveLine } from '@nivo/line';
import { useRestaurantStore } from '../../store/restaurantStore';

export default function PriceAnalytics() {
  const { addAIInsight } = useRestaurantStore();

  // Sample competitor pricing data
  const competitorPricing = [
    { item: 'Margherita Pizza', ourPrice: 18, competitor1: 16, competitor2: 22, competitor3: 19 },
    { item: 'Caesar Salad', ourPrice: 14, competitor1: 12, competitor2: 16, competitor3: 15 },
    { item: 'Truffle Pasta', ourPrice: 28, competitor1: 25, competitor2: 32, competitor3: 30 },
    { item: 'Tiramisu', ourPrice: 9, competitor1: 8, competitor2: 12, competitor3: 10 }
  ];

  // Sample price elasticity simulation data
  const elasticityData = [{
    id: 'revenue',
    data: [
      { x: 14, y: 2100 }, { x: 16, y: 2400 }, { x: 18, y: 2520 }, 
      { x: 20, y: 2400 }, { x: 22, y: 2200 }, { x: 24, y: 1920 }
    ]
  }];

  const handlePriceAnalysis = (item: string, ourPrice: number, avgCompetitor: number) => {
    const difference = ((ourPrice - avgCompetitor) / avgCompetitor * 100).toFixed(1);
    const position = ourPrice > avgCompetitor ? 'premium' : ourPrice < avgCompetitor ? 'value' : 'competitive';
    addAIInsight(`💰 ${item}: Our price $${ourPrice} vs avg competitor $${avgCompetitor.toFixed(2)} (${difference}% ${ourPrice > avgCompetitor ? 'higher' : 'lower'}). Position: ${position}`);
  };

  return (
    <div className="space-y-6">
      {/* Price Analytics Header */}
      <div className="bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg p-4">
        <h2 className="text-xl font-bold text-yellow-900 mb-2">💰 Price Analytics</h2>
        <p className="text-yellow-700 text-sm">
          Competitive pricing analysis and revenue optimization
        </p>
      </div>

      {/* Key Pricing Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-green-600">$21.50</div>
          <div className="text-sm text-gray-600">Avg Menu Price</div>
          <div className="text-xs text-green-600 mt-1">+5% vs competitors</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">68%</div>
          <div className="text-sm text-gray-600">Avg Profit Margin</div>
          <div className="text-xs text-blue-600 mt-1">Above industry avg</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">-1.2</div>
          <div className="text-sm text-gray-600">Price Elasticity</div>
          <div className="text-xs text-purple-600 mt-1">Moderately elastic</div>
        </div>
      </div>

      {/* Competitor Price Comparison */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-semibold mb-4 flex items-center gap-2">
          <span className="text-red-600">🏢</span>
          Competitor Price Benchmarking
        </h3>
        <div className="h-80">
          <ResponsiveBar
            data={competitorPricing}
            keys={['ourPrice', 'competitor1', 'competitor2', 'competitor3']}
            indexBy="item"
            margin={{ top: 20, right: 130, bottom: 80, left: 60 }}
            padding={0.3}
            valueScale={{ type: 'linear' }}
            indexScale={{ type: 'band', round: true }}
            colors={['#4CAF50', '#FF9800', '#F44336', '#9C27B0']}
            borderColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
            axisTop={null}
            axisRight={null}
            axisBottom={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: -45,
              legend: 'Menu Items',
              legendPosition: 'middle',
              legendOffset: 60
            }}
            axisLeft={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'Price ($)',
              legendPosition: 'middle',
              legendOffset: -40
            }}
            labelSkipWidth={12}
            labelSkipHeight={12}
            labelTextColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
            legends={[
              {
                dataFrom: 'keys',
                anchor: 'bottom-right',
                direction: 'column',
                justify: false,
                translateX: 120,
                translateY: 0,
                itemsSpacing: 2,
                itemWidth: 100,
                itemHeight: 20,
                itemDirection: 'left-to-right',
                itemOpacity: 0.85,
                symbolSize: 20,
                effects: [
                  {
                    on: 'hover',
                    style: {
                      itemOpacity: 1
                    }
                  }
                ]
              }
            ]}
            animate={true}
            motionStiffness={90}
            motionDamping={15}
            onClick={(data) => {
              const item = data.data as any;
              const avgCompetitor = (item.competitor1 + item.competitor2 + item.competitor3) / 3;
              handlePriceAnalysis(item.item, item.ourPrice, avgCompetitor);
            }}
          />
        </div>
      </div>

      {/* Price Elasticity Simulation */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-semibold mb-4 flex items-center gap-2">
          <span className="text-blue-600">📈</span>
          Price Elasticity Simulation - Margherita Pizza
        </h3>
        <div className="h-64">
          <ResponsiveLine
            data={elasticityData}
            margin={{ top: 20, right: 60, bottom: 60, left: 80 }}
            xScale={{ type: 'linear', min: 12, max: 26 }}
            yScale={{ type: 'linear', min: 1500, max: 2800 }}
            curve="cardinal"
            axisTop={null}
            axisRight={null}
            axisBottom={{
              orient: 'bottom',
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'Price ($)',
              legendOffset: 40,
              legendPosition: 'middle'
            }}
            axisLeft={{
              orient: 'left',
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'Weekly Revenue ($)',
              legendOffset: -60,
              legendPosition: 'middle'
            }}
            colors={['#2196F3']}
            pointSize={8}
            pointColor={{ theme: 'background' }}
            pointBorderWidth={2}
            pointBorderColor={{ from: 'serieColor' }}
            pointLabelYOffset={-12}
            useMesh={true}
            animate={true}
            motionStiffness={90}
            motionDamping={15}
            onClick={(point) => {
              addAIInsight(`📊 Price point $${point.data.x} would generate approximately $${point.data.y} in weekly revenue`);
            }}
          />
        </div>
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>💡 Insight:</strong> Optimal price point appears to be around $18-19, 
            balancing revenue maximization with demand elasticity. Current price of $18 is well-positioned.
          </p>
        </div>
      </div>

      {/* Break-Even Analysis */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-semibold mb-4 flex items-center gap-2">
          <span className="text-green-600">⚖️</span>
          Break-Even Analysis
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-3">Cost Structure</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Fixed Costs (Monthly):</span>
                  <span className="font-medium">$15,000</span>
                </div>
                <div className="flex justify-between">
                  <span>Variable Cost per Unit:</span>
                  <span className="font-medium">$7.50</span>
                </div>
                <div className="flex justify-between">
                  <span>Average Selling Price:</span>
                  <span className="font-medium">$21.50</span>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <span>Contribution Margin:</span>
                  <span className="font-medium text-green-600">$14.00</span>
                </div>
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium mb-3">Break-Even Metrics</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Break-Even Units (Monthly):</span>
                  <span className="font-medium">1,071 units</span>
                </div>
                <div className="flex justify-between">
                  <span>Break-Even Revenue:</span>
                  <span className="font-medium">$23,036</span>
                </div>
                <div className="flex justify-between">
                  <span>Current Monthly Units:</span>
                  <span className="font-medium text-green-600">1,450 units</span>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <span>Margin of Safety:</span>
                  <span className="font-medium text-green-600">26.1%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>⚠️ Recommendation:</strong> You're operating 379 units above break-even with a healthy 26% margin of safety. 
            Consider slight price increases on high-volume items to improve profitability.
          </p>
        </div>
      </div>

      {/* Pricing Strategy Recommendations */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-semibold mb-4 flex items-center gap-2">
          <span className="text-purple-600">🎯</span>
          Pricing Strategy Recommendations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border border-green-200 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-800 mb-2">✅ Opportunities</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Truffle Pasta: 12% below competitor average - room for increase</li>
              <li>• Caesar Salad: Premium positioning justified by quality</li>
              <li>• Desserts: Underpriced category with high margins</li>
            </ul>
          </div>
          <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">⚠️ Watch Items</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Margherita Pizza: Monitor competitor responses</li>
              <li>• Lunch specials: Price-sensitive customer segment</li>
              <li>• Beverages: Opportunity for bundling strategies</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}