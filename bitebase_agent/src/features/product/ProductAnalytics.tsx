'use client';

import React from 'react';
import { ResponsiveBar } from '@nivo/bar';
import { ResponsiveScatterPlot } from '@nivo/scatterplot';
import { useRestaurantStore } from '../../store/restaurantStore';

export default function ProductAnalytics() {
  const { menuItems, addAIInsight } = useRestaurantStore();

  // Prepare data for top-selling items chart
  const topSellingData = menuItems
    .sort((a, b) => b.salesVolume - a.salesVolume)
    .slice(0, 10)
    .map(item => ({
      item: item.name,
      volume: item.salesVolume,
      revenue: item.revenue,
      profit: item.revenue - item.cogs,
      margin: item.profitMargin
    }));

  // Prepare data for menu engineering matrix (BCG-style)
  const menuMatrixData = [{
    id: 'menu-items',
    data: menuItems.map(item => ({
      x: item.popularity, // Popularity (sales volume percentile)
      y: item.profitability, // Profitability (margin percentile)
      name: item.name,
      category: item.category,
      volume: item.salesVolume,
      revenue: item.revenue,
      margin: item.profitMargin
    }))
  }];

  const getQuadrantLabel = (popularity: number, profitability: number) => {
    if (popularity >= 50 && profitability >= 50) return 'Stars ⭐';
    if (popularity >= 50 && profitability < 50) return 'Plowhorses 🐎';
    if (popularity < 50 && profitability >= 50) return 'Puzzles 🧩';
    return 'Dogs 🐕';
  };

  const getQuadrantColor = (popularity: number, profitability: number) => {
    if (popularity >= 50 && profitability >= 50) return '#4CAF50'; // Green - Stars
    if (popularity >= 50 && profitability < 50) return '#FF9800'; // Orange - Plowhorses
    if (popularity < 50 && profitability >= 50) return '#2196F3'; // Blue - Puzzles
    return '#F44336'; // Red - Dogs
  };

  const handleItemClick = (item: any) => {
    const quadrant = getQuadrantLabel(item.x, item.y);
    addAIInsight(`📊 Selected ${item.name} - Category: ${quadrant}. This item has ${item.x}% popularity and ${item.y}% profitability. Revenue: $${item.revenue}`);
  };

  return (
    <div className="space-y-6">
      {/* Product Analytics Header */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
        <h2 className="text-xl font-bold text-green-900 mb-2">🍽️ Product Analytics</h2>
        <p className="text-green-700 text-sm">
          Menu performance analysis and profitability insights
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{menuItems.length}</div>
          <div className="text-sm text-gray-600">Menu Items</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-green-600">
            ${menuItems.reduce((sum, item) => sum + item.revenue, 0).toLocaleString()}
          </div>
          <div className="text-sm text-gray-600">Total Revenue</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">
            {(menuItems.reduce((sum, item) => sum + item.profitMargin, 0) / menuItems.length).toFixed(1)}%
          </div>
          <div className="text-sm text-gray-600">Avg Profit Margin</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">
            {menuItems.reduce((sum, item) => sum + item.salesVolume, 0)}
          </div>
          <div className="text-sm text-gray-600">Total Units Sold</div>
        </div>
      </div>

      {/* Top Selling Items Chart */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-semibold mb-4 flex items-center gap-2">
          <span className="text-blue-600">📊</span>
          Top Selling Items
        </h3>
        <div className="h-80">
          <ResponsiveBar
            data={topSellingData}
            keys={['volume']}
            indexBy="item"
            margin={{ top: 20, right: 60, bottom: 80, left: 60 }}
            padding={0.3}
            valueScale={{ type: 'linear' }}
            indexScale={{ type: 'band', round: true }}
            colors={{ scheme: 'blues' }}
            borderColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
            axisTop={null}
            axisRight={null}
            axisBottom={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: -45,
              legend: 'Menu Items',
              legendPosition: 'middle',
              legendOffset: 60
            }}
            axisLeft={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'Units Sold',
              legendPosition: 'middle',
              legendOffset: -40
            }}
            labelSkipWidth={12}
            labelSkipHeight={12}
            labelTextColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
            animate={true}
            motionStiffness={90}
            motionDamping={15}
          />
        </div>
      </div>

      {/* Menu Engineering Matrix */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="mb-4">
          <h3 className="font-semibold flex items-center gap-2">
            <span className="text-purple-600">🎯</span>
            Menu Engineering Matrix
          </h3>
          <p className="text-sm text-gray-600 mt-1">
            BCG-style analysis: Popularity vs Profitability
          </p>
        </div>
        
        {/* Legend */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4 text-xs">
          <div className="flex items-center gap-2 p-2 bg-green-50 rounded">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span>Stars ⭐ (High/High)</span>
          </div>
          <div className="flex items-center gap-2 p-2 bg-orange-50 rounded">
            <div className="w-3 h-3 rounded-full bg-orange-500"></div>
            <span>Plowhorses 🐎 (High/Low)</span>
          </div>
          <div className="flex items-center gap-2 p-2 bg-blue-50 rounded">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span>Puzzles 🧩 (Low/High)</span>
          </div>
          <div className="flex items-center gap-2 p-2 bg-red-50 rounded">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span>Dogs 🐕 (Low/Low)</span>
          </div>
        </div>

        <div className="h-96">
          <ResponsiveScatterPlot
            data={menuMatrixData}
            margin={{ top: 20, right: 60, bottom: 80, left: 80 }}
            xScale={{ type: 'linear', min: 0, max: 100 }}
            yScale={{ type: 'linear', min: 0, max: 100 }}
            colors={(d) => getQuadrantColor(d.data.x, d.data.y)}
            nodeSize={12}
            axisTop={null}
            axisRight={null}
            axisBottom={{
              orient: 'bottom',
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'Popularity (Sales Volume Percentile)',
              legendPosition: 'middle',
              legendOffset: 60
            }}
            axisLeft={{
              orient: 'left',
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'Profitability (Margin Percentile)',
              legendPosition: 'middle',
              legendOffset: -60
            }}
            onClick={(point) => handleItemClick(point.data)}
            tooltip={({ node }) => (
              <div className="bg-white p-3 border rounded-lg shadow-lg">
                <div className="font-semibold">{node.data.name}</div>
                <div className="text-sm text-gray-600">{node.data.category}</div>
                <div className="text-sm mt-1">
                  <div>Popularity: {node.data.x}%</div>
                  <div>Profitability: {node.data.y}%</div>
                  <div>Revenue: ${node.data.revenue}</div>
                  <div>Volume: {node.data.volume} units</div>
                </div>
                <div className="text-xs mt-2 font-medium text-purple-600">
                  {getQuadrantLabel(node.data.x, node.data.y)}
                </div>
              </div>
            )}
            animate={true}
            motionStiffness={90}
            motionDamping={15}
          />
        </div>

        {/* Quadrant Guidelines */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">📈 Strategic Recommendations</h4>
            <ul className="space-y-1 text-xs">
              <li><strong>Stars ⭐:</strong> Promote heavily, maintain quality</li>
              <li><strong>Plowhorses 🐎:</strong> Increase prices or reduce costs</li>
              <li><strong>Puzzles 🧩:</strong> Improve marketing and placement</li>
              <li><strong>Dogs 🐕:</strong> Consider removing from menu</li>
            </ul>
          </div>
          <div className="p-3 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">🎯 Current Distribution</h4>
            <div className="space-y-1 text-xs">
              {['Stars', 'Plowhorses', 'Puzzles', 'Dogs'].map(category => {
                const count = menuItems.filter(item => {
                  const label = getQuadrantLabel(item.popularity, item.profitability);
                  return label.includes(category);
                }).length;
                return (
                  <div key={category} className="flex justify-between">
                    <span>{category}:</span>
                    <span className="font-medium">{count} items</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}