'use client';

import React from 'react';
import { ResponsiveBar } from '@nivo/bar';
import { ResponsiveScatterPlot } from '@nivo/scatterplot';
import { ResponsivePie } from '@nivo/pie';
import { useRestaurantStore } from '../../store/restaurantStore';

export default function PromotionAnalytics() {
  const { customers, addAIInsight } = useRestaurantStore();

  // Sample marketing campaign data
  const campaignROI = [
    { campaign: 'Email Newsletter', spent: 500, revenue: 2400, newCustomers: 24, roi: 380 },
    { campaign: 'Google Ads', spent: 1200, revenue: 3600, newCustomers: 18, roi: 200 },
    { campaign: 'Social Media', spent: 800, revenue: 2800, newCustomers: 35, roi: 250 },
    { campaign: 'Direct Mail', spent: 1500, revenue: 2100, newCustomers: 12, roi: 40 }
  ];

  // RFM Analysis data based on customer segments
  const rfmData = [{
    id: 'customer-segments',
    data: [
      { x: 85, y: 90, segment: 'Champions', count: 45, value: 12500 },
      { x: 70, y: 85, segment: 'Loyal Customers', count: 78, value: 18200 },
      { x: 90, y: 45, segment: 'New Customers', count: 52, value: 8900 },
      { x: 25, y: 75, segment: 'At Risk', count: 23, value: 5600 },
      { x: 15, y: 25, segment: 'Lost', count: 18, value: 2100 }
    ]
  }];

  // Customer sentiment distribution
  const sentimentData = [
    { id: 'Positive', label: 'Positive', value: 68, color: '#4CAF50' },
    { id: 'Neutral', label: 'Neutral', value: 22, color: '#FF9800' },
    { id: 'Negative', label: 'Negative', value: 10, color: '#F44336' }
  ];

  const handleCampaignClick = (campaign: any) => {
    addAIInsight(`📢 ${campaign.campaign}: $${campaign.spent} spent, $${campaign.revenue} revenue, ${campaign.roi}% ROI. Acquired ${campaign.newCustomers} new customers.`);
  };

  const handleSegmentClick = (point: any) => {
    const data = point.data;
    addAIInsight(`👥 ${data.segment}: ${data.count} customers, $${data.value} total value. Recency: ${data.x}%, Frequency: ${data.y}%`);
  };

  const getSegmentColor = (segment: string) => {
    const colors: Record<string, string> = {
      'Champions': '#4CAF50',
      'Loyal Customers': '#2196F3',
      'New Customers': '#FF9800',
      'At Risk': '#FF5722',
      'Lost': '#9E9E9E'
    };
    return colors[segment] || '#9E9E9E';
  };

  return (
    <div className="space-y-6">
      {/* Promotion Analytics Header */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
        <h2 className="text-xl font-bold text-purple-900 mb-2">📢 Promotion Analytics</h2>
        <p className="text-purple-700 text-sm">
          Marketing campaign performance and customer engagement insights
        </p>
      </div>

      {/* Key Marketing Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-green-600">245%</div>
          <div className="text-sm text-gray-600">Avg Campaign ROI</div>
          <div className="text-xs text-green-600 mt-1">Above industry avg</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">89</div>
          <div className="text-sm text-gray-600">New Customers</div>
          <div className="text-xs text-blue-600 mt-1">This month</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">$42</div>
          <div className="text-sm text-gray-600">Customer Acq. Cost</div>
          <div className="text-xs text-purple-600 mt-1">-15% vs last month</div>
        </div>
        <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">$285</div>
          <div className="text-sm text-gray-600">Customer LTV</div>
          <div className="text-xs text-orange-600 mt-1">6.8x CAC ratio</div>
        </div>
      </div>

      {/* Campaign ROI Analysis */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-semibold mb-4 flex items-center gap-2">
          <span className="text-green-600">💰</span>
          Marketing Campaign ROI
        </h3>
        <div className="h-80">
          <ResponsiveBar
            data={campaignROI}
            keys={['roi']}
            indexBy="campaign"
            margin={{ top: 20, right: 60, bottom: 80, left: 60 }}
            padding={0.3}
            valueScale={{ type: 'linear' }}
            indexScale={{ type: 'band', round: true }}
            colors={['#4CAF50']}
            borderColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
            axisTop={null}
            axisRight={null}
            axisBottom={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: -45,
              legend: 'Marketing Campaigns',
              legendPosition: 'middle',
              legendOffset: 60
            }}
            axisLeft={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'ROI (%)',
              legendPosition: 'middle',
              legendOffset: -40
            }}
            labelSkipWidth={12}
            labelSkipHeight={12}
            labelTextColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
            animate={true}
            motionStiffness={90}
            motionDamping={15}
            onClick={(data) => handleCampaignClick(data.data)}
            tooltip={({ data }) => (
              <div className="bg-white p-3 border rounded-lg shadow-lg">
                <div className="font-semibold">{data.campaign}</div>
                <div className="text-sm mt-1">
                  <div>Spent: ${data.spent}</div>
                  <div>Revenue: ${data.revenue}</div>
                  <div>ROI: {data.roi}%</div>
                  <div>New Customers: {data.newCustomers}</div>
                </div>
              </div>
            )}
          />
        </div>
      </div>

      {/* RFM Customer Segmentation */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-semibold mb-4 flex items-center gap-2">
          <span className="text-blue-600">👥</span>
          RFM Customer Segmentation
        </h3>
        <div className="h-80">
          <ResponsiveScatterPlot
            data={rfmData}
            margin={{ top: 20, right: 60, bottom: 80, left: 80 }}
            xScale={{ type: 'linear', min: 0, max: 100 }}
            yScale={{ type: 'linear', min: 0, max: 100 }}
            colors={(d) => getSegmentColor(d.data.segment)}
            nodeSize={(d) => Math.sqrt(d.data.count) * 3}
            axisTop={null}
            axisRight={null}
            axisBottom={{
              orient: 'bottom',
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'Recency Score',
              legendPosition: 'middle',
              legendOffset: 60
            }}
            axisLeft={{
              orient: 'left',
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'Frequency Score',
              legendPosition: 'middle',
              legendOffset: -60
            }}
            onClick={(point) => handleSegmentClick(point)}
            tooltip={({ node }) => (
              <div className="bg-white p-3 border rounded-lg shadow-lg">
                <div className="font-semibold">{node.data.segment}</div>
                <div className="text-sm mt-1">
                  <div>Customers: {node.data.count}</div>
                  <div>Total Value: ${node.data.value}</div>
                  <div>Recency: {node.data.x}%</div>
                  <div>Frequency: {node.data.y}%</div>
                </div>
              </div>
            )}
            animate={true}
            motionStiffness={90}
            motionDamping={15}
          />
        </div>
        <div className="mt-4 grid grid-cols-1 md:grid-cols-5 gap-2 text-xs">
          {rfmData[0].data.map((segment) => (
            <div key={segment.segment} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: getSegmentColor(segment.segment) }}
              ></div>
              <span>{segment.segment}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Customer Sentiment Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="font-semibold mb-4 flex items-center gap-2">
            <span className="text-yellow-600">😊</span>
            Customer Sentiment
          </h3>
          <div className="h-64">
            <ResponsivePie
              data={sentimentData}
              margin={{ top: 20, right: 20, bottom: 20, left: 20 }}
              innerRadius={0.5}
              padAngle={0.7}
              cornerRadius={3}
              activeOuterRadiusOffset={8}
              colors={{ datum: 'data.color' }}
              borderWidth={1}
              borderColor={{ from: 'color', modifiers: [['darker', 0.2]] }}
              arcLinkLabelsSkipAngle={10}
              arcLinkLabelsTextColor="#333333"
              arcLinkLabelsThickness={2}
              arcLinkLabelsColor={{ from: 'color' }}
              arcLabelsSkipAngle={10}
              arcLabelsTextColor={{ from: 'color', modifiers: [['darker', 2]] }}
              animate={true}
              motionStiffness={90}
              motionDamping={15}
            />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="font-semibold mb-4 flex items-center gap-2">
            <span className="text-green-600">💬</span>
            Review Insights
          </h3>
          <div className="space-y-4">
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">🔥 Top Positive Themes</h4>
              <div className="flex flex-wrap gap-2">
                {['Excellent Service', 'Fresh Ingredients', 'Cozy Atmosphere', 'Great Value'].map((theme) => (
                  <span key={theme} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    {theme}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <h4 className="font-medium text-red-800 mb-2">⚠️ Areas for Improvement</h4>
              <div className="flex flex-wrap gap-2">
                {['Wait Times', 'Parking', 'Noise Level'].map((theme) => (
                  <span key={theme} className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                    {theme}
                  </span>
                ))}
              </div>
            </div>

            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">📊 Sentiment Trend</h4>
              <div className="text-sm text-blue-700">
                <div className="flex justify-between">
                  <span>This Month:</span>
                  <span className="font-medium text-green-600">↗️ +5% Positive</span>
                </div>
                <div className="flex justify-between">
                  <span>Avg Rating:</span>
                  <span className="font-medium">4.3/5 ⭐</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Marketing Recommendations */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="font-semibold mb-4 flex items-center gap-2">
          <span className="text-purple-600">🎯</span>
          Marketing Strategy Recommendations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border border-green-200 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-800 mb-2">✅ High Performers</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Email Newsletter: 380% ROI - scale up</li>
              <li>• Social Media: Strong customer acquisition</li>
              <li>• Champions segment: High LTV, promote referrals</li>
            </ul>
          </div>
          <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
            <h4 className="font-medium text-yellow-800 mb-2">⚠️ Optimize</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Google Ads: Good ROI but high CAC</li>
              <li>• At-Risk customers: Re-engagement campaign</li>
              <li>• Wait time complaints: Address operationally</li>
            </ul>
          </div>
          <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
            <h4 className="font-medium text-red-800 mb-2">🔄 Reconsider</h4>
            <ul className="text-sm text-red-700 space-y-1">
              <li>• Direct Mail: Low ROI, consider digital alternatives</li>
              <li>• Lost customers: Win-back campaign or write-off</li>
              <li>• Negative sentiment: Address root causes</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}