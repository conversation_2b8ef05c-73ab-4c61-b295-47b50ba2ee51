/**
 * Available Restaurant Intelligence Agents
 * Defines the specialized agents for comprehensive restaurant analytics
 */

export enum AvailableAgents {
  // Main orchestrator agent
  RESTAURANT_ORCHESTRATOR = "restaurant_orchestrator",
  
  // Specialized intelligence agents
  PRODUCT_INTELLIGENCE = "product_intelligence",
  PLACE_INTELLIGENCE = "place_intelligence", 
  PRICE_INTELLIGENCE = "price_intelligence",
  PROMOTION_INTELLIGENCE = "promotion_intelligence"
}

export interface AgentConfig {
  id: AvailableAgents;
  name: string;
  description: string;
  icon: string;
  color: string;
  capabilities: string[];
  tools: string[];
}

export const AGENT_CONFIGS: Record<AvailableAgents, AgentConfig> = {
  [AvailableAgents.RESTAURANT_ORCHESTRATOR]: {
    id: AvailableAgents.RESTAURANT_ORCHESTRATOR,
    name: "Restaurant Orchestrator",
    description: "Main intelligence coordinator routing queries to specialized agents",
    icon: "🍽️",
    color: "bg-gradient-to-r from-purple-500 to-indigo-500",
    capabilities: [
      "Intent classification and routing",
      "Cross-agent synthesis",
      "Workflow orchestration",
      "Multi-domain analysis coordination"
    ],
    tools: ["classifyIntent", "routeToAgent", "synthesizeResults"]
  },

  [AvailableAgents.PRODUCT_INTELLIGENCE]: {
    id: AvailableAgents.PRODUCT_INTELLIGENCE,
    name: "Product Intelligence",
    description: "Menu performance analytics and optimization using BCG matrix methodology",
    icon: "🍽️",
    color: "bg-gradient-to-r from-green-500 to-emerald-500",
    capabilities: [
      "BCG menu engineering analysis (Stars, Dogs, Puzzles, Plowhorses)",
      "Item popularity and profitability tracking",
      "Seasonal trend analysis and demand forecasting", 
      "Dynamic pricing recommendations",
      "Recipe cost vs. margin optimization"
    ],
    tools: ["analyzeMenuPerformance", "optimizeMenuPricing", "forecastDemand", "analyzeProfitability"]
  },

  [AvailableAgents.PLACE_INTELLIGENCE]: {
    id: AvailableAgents.PLACE_INTELLIGENCE,
    name: "Place Intelligence", 
    description: "Location analysis, competitor mapping, and market intelligence",
    icon: "📍",
    color: "bg-gradient-to-r from-blue-500 to-cyan-500",
    capabilities: [
      "Customer density heatmap generation",
      "Competitor landscape analysis within configurable radius",
      "Delivery optimization and route planning",
      "Site selection for expansion decisions",
      "Market penetration and demographic analysis"
    ],
    tools: ["analyzeLocationPerformance", "generateCompetitorBenchmark", "optimizeDelivery", "analyzeMarketPenetration"]
  },

  [AvailableAgents.PRICE_INTELLIGENCE]: {
    id: AvailableAgents.PRICE_INTELLIGENCE,
    name: "Price Intelligence",
    description: "Revenue forecasting, pricing strategy, and financial analysis", 
    icon: "💰",
    color: "bg-gradient-to-r from-yellow-500 to-orange-500",
    capabilities: [
      "Revenue forecasting with multiple scenarios",
      "Break-even analysis and margin of safety calculations",
      "Dynamic pricing models with elasticity analysis",
      "Peak hours traffic analysis and capacity planning",
      "Financial performance benchmarking"
    ],
    tools: ["forecastRevenue", "analyzeBreakEven", "optimizePricing", "analyzeCapacity"]
  },

  [AvailableAgents.PROMOTION_INTELLIGENCE]: {
    id: AvailableAgents.PROMOTION_INTELLIGENCE,
    name: "Promotion Intelligence",
    description: "Customer segmentation, campaign ROI, and marketing analytics",
    icon: "📢", 
    color: "bg-gradient-to-r from-pink-500 to-rose-500",
    capabilities: [
      "RFM customer segmentation (Recency, Frequency, Monetary)",
      "Campaign ROI analysis with LTV:CAC metrics",
      "AI-driven sentiment analysis from review platforms",
      "Customer retention and churn prediction",
      "Marketing effectiveness and attribution modeling"
    ],
    tools: ["analyzeCustomerSegments", "evaluateCampaignROI", "analyzeSentiment", "predictChurn"]
  }
};

export const getAgentConfig = (agentId: AvailableAgents): AgentConfig => {
  return AGENT_CONFIGS[agentId];
};

export const getAllAgentConfigs = (): AgentConfig[] => {
  return Object.values(AGENT_CONFIGS);
};

export const getSpecializedAgents = (): AgentConfig[] => {
  return getAllAgentConfigs().filter(agent => 
    agent.id !== AvailableAgents.RESTAURANT_ORCHESTRATOR
  );
};
