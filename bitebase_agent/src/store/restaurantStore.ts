// Zustand Store for Restaurant Analytics - Shared State Management
import { create } from 'zustand';
import { RestaurantAnalyticsState, RestaurantLocation, CustomerDataPoint, MenuItemAnalytics, HotspotData, MapViewState, AnalyticsFilters } from '../types/restaurant';

// Sample data for demo - in production this would come from APIs
const sampleRestaurants: RestaurantLocation[] = [
  {
    id: 'own-1',
    name: 'Bella Vista Italian',
    lat: 37.7749,
    lng: -122.4194,
    address: '123 Market St, San Francisco, CA',
    type: 'own',
    cuisine: 'Italian',
    priceRange: '$$$',
    rating: 4.5
  },
  {
    id: 'comp-1',
    name: '<PERSON><PERSON>\'s Kitchen',
    lat: 37.7849,
    lng: -122.4094,
    address: '456 Union St, San Francisco, CA',
    type: 'competitor',
    cuisine: 'Italian',
    priceRange: '$$',
    rating: 4.2
  },
  {
    id: 'comp-2',
    name: 'Pasta Palace',
    lat: 37.7649,
    lng: -122.4294,
    address: '789 Columbus Ave, San Francisco, CA',
    type: 'competitor',
    cuisine: 'Italian',
    priceRange: '$$$$',
    rating: 4.7
  }
];

const sampleCustomers: CustomerDataPoint[] = Array.from({ length: 200 }, (_, i) => ({
  id: `customer-${i}`,
  lat: 37.7749 + (Math.random() - 0.5) * 0.1,
  lng: -122.4194 + (Math.random() - 0.5) * 0.1,
  orderValue: Math.random() * 100 + 20,
  orderCount: Math.floor(Math.random() * 20) + 1,
  lastOrderDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
  customerSegment: ['new', 'regular', 'vip', 'at-risk'][Math.floor(Math.random() * 4)] as any
}));

const sampleMenuItems: MenuItemAnalytics[] = [
  {
    id: 'item-1',
    name: 'Margherita Pizza',
    category: 'Pizza',
    salesVolume: 150,
    revenue: 2250,
    cogs: 900,
    profitMargin: 60,
    popularity: 85,
    profitability: 75,
    trend: 'up'
  },
  {
    id: 'item-2',
    name: 'Truffle Pasta',
    category: 'Pasta',
    salesVolume: 80,
    revenue: 2400,
    cogs: 800,
    profitMargin: 66.7,
    popularity: 45,
    profitability: 90,
    trend: 'stable'
  },
  {
    id: 'item-3',
    name: 'Caesar Salad',
    category: 'Salads',
    salesVolume: 120,
    revenue: 1440,
    cogs: 480,
    profitMargin: 66.7,
    popularity: 70,
    profitability: 80,
    trend: 'down'
  }
];

interface RestaurantStore extends RestaurantAnalyticsState {
  // Actions
  setMapView: (view: Partial<MapViewState>) => void;
  setActiveModule: (module: 'place' | 'product' | 'price' | 'promotion') => void;
  setFilters: (filters: Partial<AnalyticsFilters>) => void;
  toggleLayer: (layer: 'heatmap' | 'competitors' | 'hotspots') => void;
  selectLocation: (locationId: string) => void;
  addAIInsight: (insight: string) => void;
  setLastAIAction: (action: string) => void;
  updateCustomerData: (customers: CustomerDataPoint[]) => void;
  updateHotspots: (hotspots: HotspotData[]) => void;
  setLoading: (loading: boolean) => void;
}

export const useRestaurantStore = create<RestaurantStore>((set, get) => ({
  // Initial State
  mapView: {
    latitude: 37.7749,
    longitude: -122.4194,
    zoom: 12,
    bearing: 0,
    pitch: 0
  },
  restaurants: sampleRestaurants,
  customers: sampleCustomers,
  hotspots: [],
  selectedLocation: undefined,
  activeModule: 'place',
  filters: {
    timeRange: {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString(),
      preset: 'week'
    }
  },
  menuItems: sampleMenuItems,
  isLoading: false,
  showHeatmap: true,
  showCompetitors: true,
  showHotspots: false,
  aiInsights: [
    "Welcome to BiteBase! I can see your restaurant and 2 competitors in the area.",
    "Your customer density is highest in the Financial District - consider targeted promotions there."
  ],

  // Actions
  setMapView: (view) => set((state) => ({
    mapView: { ...state.mapView, ...view }
  })),

  setActiveModule: (module) => set({ activeModule: module }),

  setFilters: (filters) => set((state) => ({
    filters: { ...state.filters, ...filters }
  })),

  toggleLayer: (layer) => set((state) => {
    switch (layer) {
      case 'heatmap':
        return { showHeatmap: !state.showHeatmap };
      case 'competitors':
        return { showCompetitors: !state.showCompetitors };
      case 'hotspots':
        return { showHotspots: !state.showHotspots };
      default:
        return state;
    }
  }),

  selectLocation: (locationId) => set((state) => ({
    selectedLocation: locationId,
    restaurants: state.restaurants.map(r => ({
      ...r,
      isSelected: r.id === locationId
    }))
  })),

  addAIInsight: (insight) => set((state) => ({
    aiInsights: [...state.aiInsights, insight]
  })),

  setLastAIAction: (action) => set({ lastAIAction: action }),

  updateCustomerData: (customers) => set({ customers }),

  updateHotspots: (hotspots) => set({ hotspots }),

  setLoading: (loading) => set({ isLoading: loading })
}));