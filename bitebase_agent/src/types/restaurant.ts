// Restaurant Analytics State Models - Core Types for BiteBase
export interface RestaurantLocation {
  id: string;
  name: string;
  lat: number;
  lng: number;
  address: string;
  type: 'own' | 'competitor';
  cuisine?: string;
  priceRange?: '$' | '$$' | '$$$' | '$$$$';
  rating?: number;
  isSelected?: boolean;
}

export interface CustomerDataPoint {
  id: string;
  lat: number;
  lng: number;
  orderValue: number;
  orderCount: number;
  lastOrderDate: string;
  customerSegment: 'new' | 'regular' | 'vip' | 'at-risk';
}

export interface MenuItemAnalytics {
  id: string;
  name: string;
  category: string;
  salesVolume: number;
  revenue: number;
  cogs: number;
  profitMargin: number;
  popularity: number; // percentile
  profitability: number; // percentile
  trend: 'up' | 'down' | 'stable';
}

export interface HotspotData {
  id: string;
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
  properties: {
    value: number;
    zScore: number;
    pValue: number;
    significance: 'hot' | 'cold' | 'neutral';
    metric: string; // 'profitability' | 'volume' | 'frequency'
  };
}

export interface MapViewState {
  latitude: number;
  longitude: number;
  zoom: number;
  bearing: number;
  pitch: number;
}

export interface AnalyticsFilters {
  timeRange: {
    start: string;
    end: string;
    preset: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
  };
  selectedBranch?: string;
  dayOfWeek?: string[];
  timeOfDay?: {
    start: string;
    end: string;
  };
  customerSegments?: string[];
  menuCategories?: string[];
}

export interface RestaurantAnalyticsState {
  // Map State
  mapView: MapViewState;
  restaurants: RestaurantLocation[];
  customers: CustomerDataPoint[];
  hotspots: HotspotData[];
  selectedLocation?: string;
  
  // Analytics State
  activeModule: 'place' | 'product' | 'price' | 'promotion';
  filters: AnalyticsFilters;
  menuItems: MenuItemAnalytics[];
  
  // UI State
  isLoading: boolean;
  showHeatmap: boolean;
  showCompetitors: boolean;
  showHotspots: boolean;
  
  // AI State
  lastAIAction?: string;
  aiInsights: string[];
}

export interface CompetitorAnalysis {
  location: RestaurantLocation;
  distance: number;
  priceComparison: {
    item: string;
    ourPrice: number;
    theirPrice: number;
    difference: number;
  }[];
  marketShare: number;
}

export interface MarketingCampaign {
  id: string;
  name: string;
  channel: 'email' | 'social' | 'google_ads' | 'direct_mail';
  startDate: string;
  endDate: string;
  budget: number;
  spent: number;
  newCustomers: number;
  revenue: number;
  roi: number;
}