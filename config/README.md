# Configuration System

This directory contains the configuration management system for the Open Multi-Agent Canvas project.

## 📁 Configuration Files

### Core Configuration Files

- **`default.conf`** - Default configuration values for all environments
- **`development.conf`** - Development-specific configuration overrides
- **`production.conf`** - Production-specific configuration overrides
- **`project.json`** - Structured project metadata and service definitions

### Configuration Scripts

- **`load-config.sh`** - Loads and validates configuration for specified environment
- **`setup-env.sh`** - Creates and manages environment files for services

## 🚀 Quick Start

### 1. Setup Environment Files
```bash
# Setup development environment files
make setup-env

# Or directly:
./config/setup-env.sh setup development
```

### 2. View Current Configuration
```bash
# Show current configuration
make config

# Or directly:
./config/setup-env.sh show development
```

### 3. Validate Environment
```bash
# Validate environment files
make validate-env

# Or directly:
./config/setup-env.sh validate
```

## 🔧 Configuration Usage

### Using with Makefile
```bash
# Load development configuration
make config-dev

# Load production configuration  
make config-prod

# Setup environment files
make setup-env

# Validate configuration
make validate-env
```

### Using Scripts Directly
```bash
# Load specific environment
./config/load-config.sh --env development
./config/load-config.sh --env production

# Load custom config file
./config/load-config.sh --config custom.conf

# List available configurations
./config/load-config.sh --list

# Setup environment files
./config/setup-env.sh setup development
./config/setup-env.sh setup production

# Show configuration
./config/setup-env.sh show development

# Validate environment files
./config/setup-env.sh validate
```

## ⚙️ Configuration Variables

### Core Settings
| Variable | Description | Default |
|----------|-------------|---------|
| `PROJECT_NAME` | Project name | "Open Multi-Agent Canvas" |
| `FRONTEND_PORT` | Frontend service port | 3000 |
| `AGENT_PORT` | Agent backend port | 8123 |
| `COPILOT_API_PORT` | Copilot API port | 4141 |
| `NODE_ENV` | Node environment | "development" |
| `DEBUG` | Enable debug mode | true |
| `LOG_LEVEL` | Logging level | "info" |

### Service Endpoints
| Variable | Description | Default |
|----------|-------------|---------|
| `FRONTEND_ENDPOINT` | Frontend URL | "http://localhost:3000" |
| `AGENT_ENDPOINT` | Agent backend URL | "http://localhost:8123" |
| `COPILOT_API_ENDPOINT` | Copilot API URL | "http://localhost:4141" |

### Copilot API Configuration
| Variable | Description | Default |
|----------|-------------|---------|
| `COPILOT_OPENAI_API_KEY` | API key for Copilot mode | "dummy" |
| `COPILOT_OPENAI_MODEL` | Model name | "claude-sonnet-4" |
| `COPILOT_OPENAI_BASE_URL` | Base URL | "http://localhost:4141" |

### Feature Flags
| Variable | Description | Default |
|----------|-------------|---------|
| `ENABLE_MCP_AGENT` | Enable MCP Agent | true |
| `ENABLE_COPILOT_API` | Enable Copilot API | true |
| `ENABLE_LANGSMITH` | Enable LangSmith | false |
| `ENABLE_ANALYTICS` | Enable analytics | false |

## 🌍 Environment-Specific Configuration

### Development Environment
- **File**: `development.conf`
- **Features**: Hot reload, dev tools, debug logging
- **Security**: Relaxed CORS, open hosts
- **Performance**: Lower limits for development

### Production Environment
- **File**: `production.conf`
- **Features**: Optimized builds, error-only logging
- **Security**: Strict CORS, specific hosts only
- **Performance**: Higher limits for production load

## 📝 Creating Custom Configurations

### 1. Create Custom Config File
```bash
# Copy from existing config
cp config/development.conf config/custom.conf

# Edit custom values
nano config/custom.conf
```

### 2. Use Custom Configuration
```bash
# Load custom config
./config/load-config.sh --config config/custom.conf

# Or export environment variables manually
source config/custom.conf
```

### 3. Custom Environment Setup
```bash
# Setup with custom environment
./config/setup-env.sh setup custom
```

## 🔍 Configuration Validation

The system includes automatic validation for:

- **Required Variables**: Ensures critical settings are defined
- **Port Conflicts**: Checks for duplicate port assignments
- **File Existence**: Verifies configuration files exist
- **Environment Files**: Validates .env files are properly created

### Validation Commands
```bash
# Validate current configuration
./config/load-config.sh --env development

# Validate environment files
./config/setup-env.sh validate

# Full validation with Makefile
make validate-env
```

## 🛠️ Troubleshooting

### Configuration Not Loading
```bash
# Check if config file exists
ls -la config/

# Verify file permissions
chmod +x config/*.sh

# Test configuration loading
./config/load-config.sh --list
```

### Environment Variables Not Set
```bash
# Source configuration manually
source config/development.conf

# Check loaded variables
env | grep -E "(FRONTEND|AGENT|COPILOT)"

# Reload configuration
./config/load-config.sh --env development
```

### Port Conflicts
```bash
# Check what's using ports
lsof -i :3000
lsof -i :8123
lsof -i :4141

# Update port configuration
nano config/development.conf
```

## 📊 Configuration Best Practices

1. **Environment Separation**: Keep development and production configs separate
2. **Sensitive Data**: Never commit API keys or secrets to version control
3. **Validation**: Always validate configuration before deployment
4. **Documentation**: Document custom configuration changes
5. **Backup**: Keep backup copies of working configurations

## 🔗 Integration

### With Docker
```bash
# Use environment-specific config
docker-compose --env-file config/.env.development up

# Or load config first
source config/development.conf
docker-compose up
```

### With Scripts
```bash
# Load config in scripts
source config/load-config.sh --env production

# Use variables in scripts
echo "Starting frontend on port $FRONTEND_PORT"
```

### With Make
```bash
# Configuration is automatically loaded by Makefile
make config-dev && make run-dev
make config-prod && make prod-start
```
