# Open Multi-Agent Canvas Configuration
# This file contains default configuration values for the project

# Project Information
PROJECT_NAME="Open Multi-Agent Canvas"
PROJECT_VERSION="1.0.0"
PROJECT_DESCRIPTION="Multi-agent chat interface with LangGraph and CopilotKit"

# Default Ports
FRONTEND_PORT=3000
AGENT_PORT=8123
COPILOT_API_PORT=4141

# Default Hosts
FRONTEND_HOST="localhost"
AGENT_HOST="localhost"
COPILOT_API_HOST="localhost"

# Development Settings
NODE_ENV="development"
DEBUG=true
LOG_LEVEL="info"

# API Endpoints
AGENT_ENDPOINT="http://localhost:8123"
FRONTEND_ENDPOINT="http://localhost:3000"
COPILOT_API_ENDPOINT="http://localhost:4141"

# OpenAI Configuration - Using Copilot API as Default Backend
OPENAI_API_KEY="dummy"
OPENAI_MODEL="claude-sonnet-4"
OPENAI_BASE_URL="http://localhost:4141"

# Copilot API Configuration
COPILOT_OPENAI_API_KEY="dummy"
COPILOT_OPENAI_MODEL="claude-sonnet-4"
COPILOT_OPENAI_BASE_URL="http://localhost:4141"

# Build Configuration
BUILD_OUTPUT_DIR="dist"
BUILD_SOURCE_MAP=true
BUILD_MINIFY=true

# Development Tools
ENABLE_HOT_RELOAD=true
ENABLE_DEVTOOLS=true
ENABLE_LOGGING=true

# Security Settings
CORS_ORIGIN="http://localhost:3000"
ALLOWED_HOSTS="localhost,127.0.0.1"

# Performance Settings
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30000
MEMORY_LIMIT="512M"

# Feature Flags
ENABLE_MCP_AGENT=true
ENABLE_COPILOT_API=true
ENABLE_LANGSMITH=false
ENABLE_ANALYTICS=false
