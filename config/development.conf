# Development Configuration
# Overrides for development environment

# Development specific settings
NODE_ENV="development"
DEBUG=true
LOG_LEVEL="debug"

# Development ports (can be different from production)
FRONTEND_PORT=3000
AGENT_PORT=8123
COPILOT_API_PORT=4141

# Enable development features
ENABLE_HOT_RELOAD=true
ENABLE_DEVTOOLS=true
ENABLE_LOGGING=true
BUILD_SOURCE_MAP=true
BUILD_MINIFY=false

# Relaxed security for development
CORS_ORIGIN="*"
ALLOWED_HOSTS="*"

# Development API settings - Using Copilot API as default
COPILOT_API_ENDPOINT="http://localhost:4141"
AGENT_ENDPOINT="http://localhost:8123"
FRONTEND_ENDPOINT="http://localhost:3000"

# OpenAI Configuration - Copilot API Backend
OPENAI_API_KEY="dummy"
OPENAI_MODEL="claude-sonnet-4"
OPENAI_BASE_URL="http://localhost:4141"

# Performance settings for development
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=60000
MEMORY_LIMIT="1G"

# Feature flags for development
ENABLE_MCP_AGENT=true
ENABLE_COPILOT_API=true
ENABLE_LANGSMITH=true
ENABLE_ANALYTICS=false
