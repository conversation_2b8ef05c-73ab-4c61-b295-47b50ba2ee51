#!/bin/bash

# Configuration Loader Script
# This script loads configuration based on environment

set -e

# Default values
CONFIG_DIR="$(dirname "$0")"
ENVIRONMENT="${NODE_ENV:-development}"
CONFIG_FILE=""

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --env ENVIRONMENT    Set environment (development|production) [default: development]"
    echo "  -c, --config FILE        Use specific config file"
    echo "  -l, --list               List available configurations"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --env development     Load development configuration"
    echo "  $0 --env production      Load production configuration"
    echo "  $0 --list                Show available configurations"
}

# Function to list available configurations
list_configs() {
    echo "Available configurations:"
    echo ""
    echo "Environments:"
    for file in "$CONFIG_DIR"/*.conf; do
        if [ -f "$file" ]; then
            basename "$file" .conf | sed 's/^/  - /'
        fi
    done
    echo ""
    echo "Configuration files:"
    ls -la "$CONFIG_DIR"/ | grep -E '\.(conf|json|yaml|yml)$' | awk '{print "  - " $9}'
}

# Function to load configuration
load_config() {
    local env="$1"
    local config_file="$CONFIG_DIR/${env}.conf"
    
    if [ ! -f "$config_file" ]; then
        echo "❌ Configuration file not found: $config_file"
        echo "Available configurations:"
        list_configs
        exit 1
    fi
    
    echo "📄 Loading configuration: $config_file"
    
    # Load configuration file
    set -a  # automatically export all variables
    source "$config_file"
    
    # Also load default configuration if not default
    if [ "$env" != "default" ] && [ -f "$CONFIG_DIR/default.conf" ]; then
        echo "📄 Loading default configuration: $CONFIG_DIR/default.conf"
        source "$CONFIG_DIR/default.conf"
        # Re-source environment specific to override defaults
        source "$config_file"
    fi
    
    set +a  # stop automatically exporting
    
    echo "✅ Configuration loaded for environment: $env"
    
    # Show key configuration values
    echo ""
    echo "Key Configuration Values:"
    echo "  Environment: ${NODE_ENV:-not set}"
    echo "  Frontend Port: ${FRONTEND_PORT:-not set}"
    echo "  Agent Port: ${AGENT_PORT:-not set}"
    echo "  Copilot API Port: ${COPILOT_API_PORT:-not set}"
    echo "  Debug Mode: ${DEBUG:-not set}"
    echo "  Log Level: ${LOG_LEVEL:-not set}"
}

# Function to validate configuration
validate_config() {
    local errors=0
    
    echo "🔍 Validating configuration..."
    
    # Check required variables
    required_vars=("FRONTEND_PORT" "AGENT_PORT" "COPILOT_API_PORT")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo "❌ Required variable not set: $var"
            errors=$((errors + 1))
        fi
    done
    
    # Check port conflicts
    ports=("$FRONTEND_PORT" "$AGENT_PORT" "$COPILOT_API_PORT")
    unique_ports=($(printf '%s\n' "${ports[@]}" | sort -u))
    
    if [ ${#ports[@]} -ne ${#unique_ports[@]} ]; then
        echo "❌ Port conflict detected in configuration"
        errors=$((errors + 1))
    fi
    
    if [ $errors -eq 0 ]; then
        echo "✅ Configuration validation passed"
        return 0
    else
        echo "❌ Configuration validation failed with $errors errors"
        return 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -l|--list)
            list_configs
            exit 0
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Load configuration
if [ -n "$CONFIG_FILE" ]; then
    if [ -f "$CONFIG_FILE" ]; then
        echo "📄 Loading custom configuration: $CONFIG_FILE"
        set -a
        source "$CONFIG_FILE"
        set +a
    else
        echo "❌ Custom configuration file not found: $CONFIG_FILE"
        exit 1
    fi
else
    load_config "$ENVIRONMENT"
fi

# Validate configuration
validate_config

echo ""
echo "🚀 Configuration ready for environment: $ENVIRONMENT"
