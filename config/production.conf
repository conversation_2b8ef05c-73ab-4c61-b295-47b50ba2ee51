# Production Configuration
# Optimized settings for production deployment

# Production environment
NODE_ENV="production"
DEBUG=false
LOG_LEVEL="error"

# Production ports
FRONTEND_PORT=80
AGENT_PORT=8123
COPILOT_API_PORT=4141

# Production hosts (configure for your domain)
FRONTEND_HOST="0.0.0.0"
AGENT_HOST="0.0.0.0"
COPILOT_API_HOST="0.0.0.0"

# Disable development features
ENABLE_HOT_RELOAD=false
ENABLE_DEVTOOLS=false
ENABLE_LOGGING=false
BUILD_SOURCE_MAP=false
BUILD_MINIFY=true

# Security settings for production
CORS_ORIGIN="https://yourdomain.com"
ALLOWED_HOSTS="yourdomain.com,www.yourdomain.com"

# Production API endpoints (configure for your domain)
COPILOT_API_ENDPOINT="https://yourdomain.com:4141"
AGENT_ENDPOINT="https://yourdomain.com:8123"
FRONTEND_ENDPOINT="https://yourdomain.com"

# Performance settings for production
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=15000
MEMORY_LIMIT="2G"

# Feature flags for production
ENABLE_MCP_AGENT=true
ENABLE_COPILOT_API=true
ENABLE_LANGSMITH=true
ENABLE_ANALYTICS=true

# SSL/TLS Settings (if applicable)
SSL_CERT_PATH="/path/to/cert.pem"
SSL_KEY_PATH="/path/to/key.pem"
FORCE_HTTPS=true
