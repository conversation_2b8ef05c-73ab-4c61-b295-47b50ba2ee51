{"project": {"name": "Open Multi-Agent Canvas", "version": "1.0.0", "description": "Multi-agent chat interface with LangGraph and CopilotKit", "author": "CopilotKit Team", "license": "MIT"}, "services": {"frontend": {"name": "Frontend", "port": 3000, "host": "localhost", "buildCommand": "pnpm run build", "startCommand": "pnpm run start", "devCommand": "pnpm run dev", "healthCheck": "http://localhost:3000", "dependencies": ["pnpm"]}, "agent": {"name": "Agent <PERSON>", "port": 8123, "host": "localhost", "buildCommand": "poetry install", "startCommand": "poetry run langgraph dev --host localhost --port 8123 --no-browser", "devCommand": "poetry run langgraph dev --host localhost --port 8123 --no-browser", "healthCheck": "http://localhost:8123", "dependencies": ["poetry", "python3"]}, "copilot-api": {"name": "Copilot API", "port": 4141, "host": "localhost", "startCommand": "npx copilot-api@latest start --github-token $GITHUB_TOKEN --port 4141", "healthCheck": "http://localhost:4141", "dependencies": ["npx", "node"], "requiresToken": true, "tokenVariable": "GITHUB_TOKEN"}}, "environments": {"development": {"configFile": "development.conf", "enabledServices": ["frontend", "agent", "copilot-api"], "features": {"hotReload": true, "devtools": true, "logging": true, "sourceMap": true}}, "production": {"configFile": "production.conf", "enabledServices": ["frontend", "agent", "copilot-api"], "features": {"hotReload": false, "devtools": false, "logging": false, "sourceMap": false, "minify": true}}}, "api": {"openai": {"defaultModel": "gpt-4", "maxTokens": 4096, "temperature": 0.7}, "copilotApi": {"model": "claude-sonnet-4", "apiKey": "dummy", "baseUrl": "http://localhost:4141"}, "langsmith": {"enabled": false, "project": "open-multi-agent-canvas"}}, "security": {"cors": {"development": "*", "production": ["https://yourdomain.com"]}, "allowedHosts": {"development": "*", "production": ["yourdomain.com", "www.yourdomain.com"]}}, "monitoring": {"healthChecks": true, "metrics": false, "logging": {"level": "info", "format": "json"}}}