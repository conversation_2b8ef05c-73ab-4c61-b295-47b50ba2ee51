#!/bin/bash

# Environment Setup Script
# This script helps set up environment files based on configuration

set -e

CONFIG_DIR="$(dirname "$0")"
PROJECT_ROOT="$(dirname "$CONFIG_DIR")"

echo "🔧 Environment Setup for Open Multi-Agent Canvas"
echo "================================================"

# Function to create environment file from template
create_env_file() {
    local service="$1"
    local template_file="$2"
    local target_file="$3"
    local config_env="${4:-development}"
    
    echo "📄 Creating $target_file for $service..."
    
    # Load configuration
    source "$CONFIG_DIR/${config_env}.conf" 2>/dev/null || source "$CONFIG_DIR/default.conf"
    
    # Create environment file
    cat > "$target_file" << EOF
# Auto-generated environment file for $service
# Generated on: $(date)
# Environment: $config_env

EOF
    
    # Add service-specific variables
    case "$service" in
        "frontend")
            cat >> "$target_file" << EOF
# Frontend Configuration
NEXT_PUBLIC_CPK_PUBLIC_API_KEY=your_copilot_api_key_here
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:${FRONTEND_PORT}

# API Endpoints
NEXT_PUBLIC_AGENT_ENDPOINT=${AGENT_ENDPOINT}
NEXT_PUBLIC_COPILOT_API_ENDPOINT=${COPILOT_API_ENDPOINT}

# Environment
NODE_ENV=${NODE_ENV}
DEBUG=${DEBUG}
EOF
            ;;
        "agent")
            cat >> "$target_file" << EOF
# Agent Configuration - Using Copilot API as Default Backend
OPENAI_API_KEY=dummy
OPENAI_MODEL=claude-sonnet-4
OPENAI_BASE_URL=http://localhost:4141

# Alternative: Traditional OpenAI API (comment above and uncomment below)
#OPENAI_API_KEY=your_openai_api_key_here
#OPENAI_MODEL=gpt-4
#OPENAI_BASE_URL=https://api.openai.com/v1

LANGSMITH_API_KEY=your_langsmith_api_key_here

# GitHub Token for Copilot API (required for default backend)
GITHUB_TOKEN=your_github_token_here

# Environment
LOG_LEVEL=${LOG_LEVEL}
DEBUG=${DEBUG}

# Service Configuration
AGENT_HOST=${AGENT_HOST}
AGENT_PORT=${AGENT_PORT}
EOF
            ;;
    esac
    
    echo "✅ Created $target_file"
}

# Function to setup all environment files
setup_all_envs() {
    local environment="${1:-development}"
    
    echo "Setting up environment files for: $environment"
    echo ""
    
    # Frontend environment
    if [ -d "$PROJECT_ROOT/frontend" ]; then
        create_env_file "frontend" \
            "$PROJECT_ROOT/frontend/example.env" \
            "$PROJECT_ROOT/frontend/.env" \
            "$environment"
    fi
    
    # Agent environment
    if [ -d "$PROJECT_ROOT/agent" ]; then
        create_env_file "agent" \
            "$PROJECT_ROOT/agent/example.env" \
            "$PROJECT_ROOT/agent/.env" \
            "$environment"
    fi
}

# Function to show current configuration
show_config() {
    local environment="${1:-development}"
    local config_file="$CONFIG_DIR/${environment}.conf"
    
    if [ ! -f "$config_file" ]; then
        echo "❌ Configuration file not found: $config_file"
        return 1
    fi
    
    echo "Current configuration for $environment:"
    echo "======================================="
    echo ""
    
    # Load and display configuration
    source "$config_file"
    
    echo "🌐 Frontend:"
    echo "   Host: $FRONTEND_HOST"
    echo "   Port: $FRONTEND_PORT"
    echo "   Endpoint: $FRONTEND_ENDPOINT"
    echo ""
    
    echo "🤖 Agent:"
    echo "   Host: $AGENT_HOST"
    echo "   Port: $AGENT_PORT"
    echo "   Endpoint: $AGENT_ENDPOINT"
    echo ""
    
    echo "🔧 Copilot API:"
    echo "   Host: $COPILOT_API_HOST"
    echo "   Port: $COPILOT_API_PORT"
    echo "   Endpoint: $COPILOT_API_ENDPOINT"
    echo "   Model: $COPILOT_OPENAI_MODEL"
    echo ""
    
    echo "⚙️ Environment:"
    echo "   NODE_ENV: $NODE_ENV"
    echo "   DEBUG: $DEBUG"
    echo "   LOG_LEVEL: $LOG_LEVEL"
}

# Function to validate environment files
validate_env_files() {
    echo "🔍 Validating environment files..."
    
    local errors=0
    
    # Check frontend .env
    if [ -f "$PROJECT_ROOT/frontend/.env" ]; then
        echo "✅ Frontend .env exists"
        if grep -q "your_.*_here" "$PROJECT_ROOT/frontend/.env"; then
            echo "⚠️  Frontend .env contains placeholder values"
        fi
    else
        echo "❌ Frontend .env missing"
        errors=$((errors + 1))
    fi
    
    # Check agent .env
    if [ -f "$PROJECT_ROOT/agent/.env" ]; then
        echo "✅ Agent .env exists"
        if grep -q "your_.*_here" "$PROJECT_ROOT/agent/.env"; then
            echo "⚠️  Agent .env contains placeholder values"
        fi
    else
        echo "❌ Agent .env missing"
        errors=$((errors + 1))
    fi
    
    if [ $errors -eq 0 ]; then
        echo "✅ Environment validation passed"
    else
        echo "❌ Environment validation failed with $errors errors"
    fi
}

# Parse command line arguments
case "${1:-setup}" in
    "setup")
        setup_all_envs "${2:-development}"
        ;;
    "show")
        show_config "${2:-development}"
        ;;
    "validate")
        validate_env_files
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [COMMAND] [ENVIRONMENT]"
        echo ""
        echo "Commands:"
        echo "  setup      Setup environment files (default)"
        echo "  show       Show current configuration"
        echo "  validate   Validate environment files"
        echo "  help       Show this help message"
        echo ""
        echo "Environment:"
        echo "  development  Development configuration (default)"
        echo "  production   Production configuration"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
