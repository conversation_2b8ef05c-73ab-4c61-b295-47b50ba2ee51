#!/bin/bash

# Configuration Validation Script
# Comprehensive validation for Open Multi-Agent Canvas configuration

set -e

CONFIG_DIR="$(dirname "$0")"
PROJECT_ROOT="$(dirname "$CONFIG_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
ERRORS=0
WARNINGS=0
CHECKS=0

# Function to print colored output
print_error() {
    echo -e "${RED}❌ $1${NC}"
    ERRORS=$((ERRORS + 1))
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    WARNINGS=$((WARNINGS + 1))
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if command exists
check_command() {
    local cmd="$1"
    local name="$2"
    CHECKS=$((CHECKS + 1))
    
    if command -v "$cmd" &> /dev/null; then
        print_success "$name is installed: $(command -v "$cmd")"
        return 0
    else
        print_error "$name is not installed"
        return 1
    fi
}

# Function to check file exists
check_file() {
    local file="$1"
    local description="$2"
    CHECKS=$((CHECKS + 1))
    
    if [ -f "$file" ]; then
        print_success "$description exists: $file"
        return 0
    else
        print_error "$description missing: $file"
        return 1
    fi
}

# Function to check directory exists
check_directory() {
    local dir="$1"
    local description="$2"
    CHECKS=$((CHECKS + 1))
    
    if [ -d "$dir" ]; then
        print_success "$description exists: $dir"
        return 0
    else
        print_error "$description missing: $dir"
        return 1
    fi
}

# Function to check port availability
check_port() {
    local port="$1"
    local service="$2"
    CHECKS=$((CHECKS + 1))
    
    if command -v lsof &> /dev/null; then
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
            print_warning "Port $port is already in use (needed for $service)"
        else
            print_success "Port $port is available for $service"
        fi
    else
        print_info "lsof not available, skipping port check for $port ($service)"
    fi
}

# Function to validate configuration file
validate_config_file() {
    local config_file="$1"
    local env_name="$2"
    
    echo ""
    echo "🔍 Validating $env_name configuration..."
    
    if ! check_file "$config_file" "$env_name configuration"; then
        return 1
    fi
    
    # Load configuration
    source "$config_file"
    
    # Check required variables
    local required_vars=(
        "PROJECT_NAME"
        "FRONTEND_PORT"
        "AGENT_PORT"
        "COPILOT_API_PORT"
        "NODE_ENV"
    )
    
    for var in "${required_vars[@]}"; do
        CHECKS=$((CHECKS + 1))
        if [ -n "${!var}" ]; then
            print_success "Required variable $var is set: ${!var}"
        else
            print_error "Required variable $var is not set"
        fi
    done
    
    # Check port ranges
    local ports=("$FRONTEND_PORT" "$AGENT_PORT" "$COPILOT_API_PORT")
    for port in "${ports[@]}"; do
        CHECKS=$((CHECKS + 1))
        if [[ "$port" =~ ^[0-9]+$ ]] && [ "$port" -ge 1024 ] && [ "$port" -le 65535 ]; then
            print_success "Port $port is in valid range (1024-65535)"
        else
            print_error "Port $port is invalid (should be 1024-65535)"
        fi
    done
    
    # Check for port conflicts
    CHECKS=$((CHECKS + 1))
    unique_ports=($(printf '%s\n' "${ports[@]}" | sort -u))
    if [ ${#ports[@]} -eq ${#unique_ports[@]} ]; then
        print_success "No port conflicts detected"
    else
        print_error "Port conflicts detected in configuration"
    fi
}

# Function to validate environment files
validate_environment_files() {
    echo ""
    echo "🔍 Validating environment files..."
    
    # Frontend environment
    local frontend_env="$PROJECT_ROOT/frontend/.env"
    if check_file "$frontend_env" "Frontend environment file"; then
        CHECKS=$((CHECKS + 1))
        if grep -q "your_.*_here\|<.*>" "$frontend_env"; then
            print_warning "Frontend .env contains placeholder values"
        else
            print_success "Frontend .env appears to be configured"
        fi
    fi
    
    # Agent environment
    local agent_env="$PROJECT_ROOT/agent/.env"
    if check_file "$agent_env" "Agent environment file"; then
        CHECKS=$((CHECKS + 1))
        if grep -q "your_.*_here\|<.*>" "$agent_env"; then
            print_warning "Agent .env contains placeholder values"
        else
            print_success "Agent .env appears to be configured"
        fi
    fi
}

# Function to validate project structure
validate_project_structure() {
    echo ""
    echo "🔍 Validating project structure..."
    
    # Check main directories
    check_directory "$PROJECT_ROOT/frontend" "Frontend directory"
    check_directory "$PROJECT_ROOT/agent" "Agent directory"
    check_directory "$PROJECT_ROOT/config" "Config directory"
    
    # Check important files
    check_file "$PROJECT_ROOT/Makefile" "Project Makefile"
    check_file "$PROJECT_ROOT/README.md" "Project README"
    check_file "$PROJECT_ROOT/frontend/package.json" "Frontend package.json"
    check_file "$PROJECT_ROOT/agent/pyproject.toml" "Agent pyproject.toml"
    
    # Check scripts
    check_file "$PROJECT_ROOT/frontend/install.sh" "Frontend install script"
    check_file "$PROJECT_ROOT/frontend/run.sh" "Frontend run script"
    check_file "$PROJECT_ROOT/agent/install.sh" "Agent install script"
    check_file "$PROJECT_ROOT/agent/run.sh" "Agent run script"
    check_file "$PROJECT_ROOT/agent/run-with-copilot.sh" "Agent Copilot run script"
}

# Function to validate dependencies
validate_dependencies() {
    echo ""
    echo "🔍 Validating dependencies..."
    
    # Core dependencies
    check_command "node" "Node.js"
    check_command "npm" "npm"
    check_command "npx" "npx"
    check_command "pnpm" "pnpm"
    check_command "python3" "Python 3"
    check_command "poetry" "Poetry"
    
    # Optional dependencies
    if check_command "git" "Git"; then
        print_info "Git version: $(git --version)"
    fi
    
    if check_command "curl" "curl"; then
        print_info "curl is available for health checks"
    fi
    
    if check_command "lsof" "lsof"; then
        print_info "lsof is available for port checking"
    fi
}

# Function to check ports
validate_ports() {
    echo ""
    echo "🔍 Checking port availability..."
    
    # Load default configuration to get ports
    if [ -f "$CONFIG_DIR/default.conf" ]; then
        source "$CONFIG_DIR/default.conf"
        check_port "$FRONTEND_PORT" "Frontend"
        check_port "$AGENT_PORT" "Agent"
        check_port "$COPILOT_API_PORT" "Copilot API"
    else
        print_warning "Default configuration not found, skipping port checks"
    fi
}

# Main validation function
main() {
    echo "🔍 Open Multi-Agent Canvas Configuration Validation"
    echo "=================================================="
    
    # Validate project structure
    validate_project_structure
    
    # Validate dependencies
    validate_dependencies
    
    # Validate configuration files
    if [ -f "$CONFIG_DIR/default.conf" ]; then
        validate_config_file "$CONFIG_DIR/default.conf" "Default"
    fi
    
    if [ -f "$CONFIG_DIR/development.conf" ]; then
        validate_config_file "$CONFIG_DIR/development.conf" "Development"
    fi
    
    if [ -f "$CONFIG_DIR/production.conf" ]; then
        validate_config_file "$CONFIG_DIR/production.conf" "Production"
    fi
    
    # Validate environment files
    validate_environment_files
    
    # Check ports
    validate_ports
    
    # Summary
    echo ""
    echo "📊 Validation Summary"
    echo "===================="
    echo "Total checks: $CHECKS"
    echo -e "Errors: ${RED}$ERRORS${NC}"
    echo -e "Warnings: ${YELLOW}$WARNINGS${NC}"
    echo -e "Passed: ${GREEN}$((CHECKS - ERRORS - WARNINGS))${NC}"
    
    if [ $ERRORS -eq 0 ]; then
        echo ""
        print_success "Configuration validation completed successfully!"
        if [ $WARNINGS -gt 0 ]; then
            print_info "Please review the warnings above"
        fi
        exit 0
    else
        echo ""
        print_error "Configuration validation failed with $ERRORS errors"
        print_info "Please fix the errors above before proceeding"
        exit 1
    fi
}

# Parse command line arguments
case "${1:-validate}" in
    "validate"|"")
        main
        ;;
    "quick")
        echo "🔍 Quick validation check..."
        validate_project_structure
        validate_dependencies
        echo "✅ Quick validation complete"
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "Commands:"
        echo "  validate   Full validation (default)"
        echo "  quick      Quick validation check"
        echo "  help       Show this help message"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
