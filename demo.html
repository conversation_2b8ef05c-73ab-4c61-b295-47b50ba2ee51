<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗺️ Map-Chat AI Demo - CopilotKit Shared State</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .demo-container {
            flex: 1;
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        .map-section {
            flex: 1;
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-outline {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px dashed #667eea;
        }

        .map-canvas {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: linear-gradient(45deg, #e3f2fd 0%, #bbdefb 100%);
            cursor: crosshair;
            position: relative;
            min-height: 400px;
        }

        .map-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: monospace;
        }

        .marker {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            transform: translate(-50%, -50%);
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            transition: transform 0.2s;
        }

        .marker:hover {
            transform: translate(-50%, -50%) scale(1.2);
        }

        .marker.location { background: #2196F3; }
        .marker.poi { background: #9C27B0; }
        .marker.business { background: #4CAF50; }
        .marker.selected {
            border: 3px solid white;
            transform: translate(-50%, -50%) scale(1.3);
        }

        .chat-section {
            width: 400px;
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            border-radius: 12px 12px 0 0;
            font-weight: 600;
        }

        .chat-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 400px;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.5;
        }

        .message.user {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
        }

        .message.ai {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-left: 4px solid #764ba2;
        }

        .suggestions {
            padding: 16px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .suggestions h4 {
            margin-bottom: 10px;
            color: #333;
            font-size: 14px;
        }

        .suggestion {
            display: block;
            width: 100%;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            color: #667eea;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 8px;
            cursor: pointer;
            font-size: 13px;
            text-align: left;
            transition: all 0.2s;
        }

        .suggestion:hover {
            background: rgba(102, 126, 234, 0.2);
        }

        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4CAF50;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .shared-state-demo {
            background: rgba(76, 175, 80, 0.1);
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .shared-state-demo h3 {
            color: #2E7D32;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .shared-state-demo p {
            color: #388E3C;
            font-size: 14px;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .demo-container {
                flex-direction: column;
                padding: 10px;
            }
            
            .chat-section {
                width: 100%;
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗺️ Map-Chat AI Assistant Demo</h1>
        <p>🚀 CopilotKit Shared State - Real-time synchronization between map and AI agent</p>
    </div>

    <div class="demo-container">
        <!-- Map Section -->
        <div class="map-section">
            <div class="shared-state-demo">
                <h3>✨ Shared State Magic</h3>
                <p>Click on the map to add markers, and watch the AI instantly understand your actions! Every interaction syncs in real-time.</p>
            </div>

            <div class="controls">
                <div class="control-group">
                    <label>🗺️ Map:</label>
                    <button class="btn" onclick="zoomIn()">🔍+</button>
                    <button class="btn" onclick="zoomOut()">🔍-</button>
                    <span id="zoom-level">Zoom: 12</span>
                </div>
                
                <div class="control-group">
                    <label>📍 Add:</label>
                    <button class="btn-outline" onclick="addMarker('poi')">🏛️ POI</button>
                    <button class="btn-outline" onclick="addMarker('business')">🏢 Business</button>
                </div>

                <div class="control-group">
                    <button class="btn" onclick="clearMap()">🗑️ Clear</button>
                    <button class="btn" onclick="triggerAI()">🤖 AI Analyze</button>
                </div>
            </div>

            <div class="map-canvas" id="map-canvas" onclick="addMarkerAtClick(event)">
                <div class="status-indicator" id="status" style="display: none;">
                    🔄 State Syncing
                </div>
                
                <div class="map-info" id="map-info">
                    📍 2 markers | 🧭 Explore mode | 📊 Real-time sync active
                </div>

                <!-- Initial markers -->
                <div class="marker location" style="top: 40%; left: 45%;" onclick="selectMarker(this, 'San Francisco', 'location')" title="San Francisco">📍</div>
                <div class="marker poi" style="top: 35%; left: 50%;" onclick="selectMarker(this, 'North Beach', 'poi')" title="North Beach">🏛️</div>
            </div>
        </div>

        <!-- Chat Section -->
        <div class="chat-section">
            <div class="chat-header">
                🤖 AI Map Assistant
            </div>

            <div class="chat-content" id="chat-content">
                <div class="message ai">
                    🗺️ Welcome! I'm your AI Map Assistant with shared state capabilities. I can see your current map in real-time!
                    <br><br>
                    <strong>Current map state:</strong><br>
                    • 2 markers visible<br>
                    • Zoom level: 12<br>
                    • Mode: Explore<br>
                    • Center: San Francisco
                </div>

                <div class="message ai">
                    ✨ <strong>Shared State Demo:</strong> When you interact with the map, I instantly know about it! Try clicking to add a marker and watch me respond.
                </div>
            </div>

            <div class="suggestions">
                <h4>💡 Try These Commands:</h4>
                <button class="suggestion" onclick="sendMessage('Find coffee shops nearby')">
                    ☕ Find coffee shops nearby
                </button>
                <button class="suggestion" onclick="sendMessage('Plan a route between markers')">
                    🧭 Plan a route between markers
                </button>
                <button class="suggestion" onclick="sendMessage('Show restaurants in North Beach')">
                    🍽️ Show restaurants in North Beach
                </button>
                <button class="suggestion" onclick="sendMessage('Add Golden Gate Bridge marker')">
                    📍 Add Golden Gate Bridge marker
                </button>
            </div>
        </div>
    </div>

    <script>
        let mapState = {
            zoom: 12,
            markers: 2,
            selectedMarker: null,
            mode: 'explore'
        };

        function showStatus(message = "State Updated") {
            const status = document.getElementById('status');
            status.textContent = `🔄 ${message}`;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, 2000);
        }

        function updateMapInfo() {
            document.getElementById('map-info').textContent = 
                `📍 ${mapState.markers} markers | 🧭 ${mapState.mode} mode | 🔄 Zoom: ${mapState.zoom}`;
            document.getElementById('zoom-level').textContent = `Zoom: ${mapState.zoom}`;
        }

        function addMarkerAtClick(event) {
            if (event.target.id !== 'map-canvas') return;
            
            const rect = event.currentTarget.getBoundingClientRect();
            const x = ((event.clientX - rect.left) / rect.width) * 100;
            const y = ((event.clientY - rect.top) / rect.height) * 100;
            
            const marker = document.createElement('div');
            marker.className = 'marker location';
            marker.style.left = x + '%';
            marker.style.top = y + '%';
            marker.innerHTML = '📍';
            marker.title = `New Location ${mapState.markers + 1}`;
            marker.onclick = () => selectMarker(marker, `Location ${mapState.markers + 1}`, 'location');
            
            document.getElementById('map-canvas').appendChild(marker);
            mapState.markers++;
            updateMapInfo();
            showStatus("Marker Added");
            
            sendMessage(`I added a new marker at coordinates (${x.toFixed(1)}%, ${y.toFixed(1)}%)`);
            addAIResponse(`🎯 I see you added a new marker! I've updated my understanding of your map. There are now ${mapState.markers} markers total. Would you like me to suggest nearby places of interest?`);
        }

        function addMarker(type) {
            const icons = { poi: '🏛️', business: '🏢', location: '📍' };
            const colors = { poi: 'poi', business: 'business', location: 'location' };
            
            const marker = document.createElement('div');
            marker.className = `marker ${colors[type]}`;
            marker.style.left = (Math.random() * 80 + 10) + '%';
            marker.style.top = (Math.random() * 60 + 20) + '%';
            marker.innerHTML = icons[type];
            marker.title = `New ${type}`;
            marker.onclick = () => selectMarker(marker, `New ${type}`, type);
            
            document.getElementById('map-canvas').appendChild(marker);
            mapState.markers++;
            updateMapInfo();
            showStatus(`${type} Added`);
            
            sendMessage(`I added a new ${type} marker`);
            addAIResponse(`✅ Perfect! I've added a new ${type} marker to your map. The shared state is working perfectly - I can see all ${mapState.markers} markers now!`);
        }

        function selectMarker(element, name, type) {
            // Remove previous selection
            document.querySelectorAll('.marker').forEach(m => m.classList.remove('selected'));
            
            // Select new marker
            element.classList.add('selected');
            mapState.selectedMarker = name;
            showStatus("Marker Selected");
            
            sendMessage(`I selected the "${name}" marker`);
            addAIResponse(`📍 You selected the "${name}" marker (${type}). I can now provide specific information about this location. What would you like to know?`);
        }

        function zoomIn() {
            if (mapState.zoom < 20) {
                mapState.zoom++;
                updateMapInfo();
                showStatus("Zoomed In");
                sendMessage(`I zoomed in to level ${mapState.zoom}`);
                addAIResponse(`🔍 Zoom level increased to ${mapState.zoom}. I can see more detail now!`);
            }
        }

        function zoomOut() {
            if (mapState.zoom > 1) {
                mapState.zoom--;
                updateMapInfo();
                showStatus("Zoomed Out");
                sendMessage(`I zoomed out to level ${mapState.zoom}`);
                addAIResponse(`🔍 Zoom level decreased to ${mapState.zoom}. Showing broader area view.`);
            }
        }

        function clearMap() {
            const markers = document.querySelectorAll('.marker');
            markers.forEach(marker => marker.remove());
            mapState.markers = 0;
            mapState.selectedMarker = null;
            updateMapInfo();
            showStatus("Map Cleared");
            
            sendMessage("I cleared all markers from the map");
            addAIResponse("🗑️ Map cleared! All markers have been removed. The shared state reflects this change instantly. Ready to start fresh!");
        }

        function triggerAI() {
            showStatus("AI Analyzing");
            addAIResponse(`🤖 <strong>AI Analysis Complete!</strong><br><br>
                Current map state:<br>
                • ${mapState.markers} markers<br>
                • Zoom level: ${mapState.zoom}<br>
                • Selected: ${mapState.selectedMarker || 'None'}<br><br>
                ✨ <strong>Shared State Status:</strong> Perfect sync! I can see every change you make in real-time.`);
        }

        function sendMessage(message) {
            const chatContent = document.getElementById('chat-content');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user';
            messageDiv.textContent = message;
            chatContent.appendChild(messageDiv);
            chatContent.scrollTop = chatContent.scrollHeight;
        }

        function addAIResponse(message) {
            setTimeout(() => {
                const chatContent = document.getElementById('chat-content');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ai';
                messageDiv.innerHTML = message;
                chatContent.appendChild(messageDiv);
                chatContent.scrollTop = chatContent.scrollHeight;
            }, 1000);
        }

        // Initialize
        updateMapInfo();
    </script>
</body>
</html>
