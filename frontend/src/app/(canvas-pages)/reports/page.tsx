"use client";
import { ReportView } from "@/components/report-view";
import { useReports } from "@/components/report-provider";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, FileText, Calendar, Clock } from "lucide-react";
import Link from "next/link";

export default function ReportsPage() {
  const { reports, currentReport, setCurrentReport } = useReports();

  const handleReportSelect = (reportId: string) => {
    const report = reports.find(r => r.id === reportId);
    setCurrentReport(report || null);
  };

  if (currentReport) {
    return (
      <div className="h-full flex flex-col">
        <div className="border-b bg-white px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentReport(null)}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Reports</span>
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">
                {currentReport.title}
              </h1>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Calendar className="h-4 w-4" />
              <span>{currentReport.createdAt.toLocaleDateString()}</span>
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <ReportView report={currentReport} />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="border-b bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reports</h1>
            <p className="text-gray-600">View and manage your generated reports</p>
          </div>
          <Link href="/">
            <Button variant="outline">
              Back to Canvas
            </Button>
          </Link>
        </div>
      </div>

      <div className="flex-1 overflow-auto p-6">
        {reports.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <FileText className="h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No reports yet
            </h3>
            <p className="text-gray-600 mb-6 max-w-md">
              Generate your first report by chatting with the Restaurant Intelligence agent on the main canvas.
            </p>
            <Link href="/">
              <Button>Go to Canvas</Button>
            </Link>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {reports.map((report) => (
              <div
                key={report.id}
                className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => handleReportSelect(report.id)}
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="font-semibold text-gray-900 text-lg leading-tight">
                      {report.title}
                    </h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      report.status === 'completed' ? 'bg-green-100 text-green-800' :
                      report.status === 'generating' ? 'bg-blue-100 text-blue-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {report.status}
                    </span>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {report.summary}
                  </p>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{report.createdAt.toLocaleDateString()}</span>
                    </div>
                    {report.completedAt && (
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{Math.round(report.metadata.analysisTime / 60)} min</span>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <span className="text-xs font-medium text-gray-700 uppercase tracking-wide">
                      {report.type.replace('_', ' ')}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}