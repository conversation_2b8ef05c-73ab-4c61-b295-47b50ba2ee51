"use client";
import * as Skeletons from "@/components/skeletons";
import { But<PERSON> } from "@/components/ui/button";
import { AvailableAgents } from "@/lib/available-agents";
import { useCoAgent, useCopilotAction } from "@copilotkit/react-core";
import { Icon, LatLngTuple } from "leaflet";
import "leaflet-defaulticon-compatibility";
import "leaflet-defaulticon-compatibility/dist/leaflet-defaulticon-compatibility.css";
import "leaflet/dist/leaflet.css";
import { CheckCircle, Loader2, XCircle } from "lucide-react";
import dynamic from "next/dynamic";
import { useRef, useState } from "react";
import { Marker, Popup, TileLayer } from "react-leaflet";

const customIcon = new Icon({
  iconUrl: "/icon.png",
  iconSize: [40, 40],
  iconAnchor: [12, 25],
  popupAnchor: [0, -25],
});

interface Restaurant {
  id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  rating: number;
  description: string;
  cuisine_type?: string;
  price_range?: string;
  competitor_level?: 'direct' | 'indirect' | 'potential';
}

const Map = dynamic(
  () => import("react-leaflet").then((mod) => mod.MapContainer),
  {
    ssr: false,
  }
);

export default function MapComponent() {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [center, setCenter] = useState<LatLngTuple>([0, 0]);
  const hasProcessedAnalysis = useRef(false);
  const hasInProgress = useRef(false);
  
  const researchAgentActive = useRef(false);
  const { running: researchAgentRunning } = useCoAgent({
    name: AvailableAgents.RESEARCH_AGENT,
  });

  if (researchAgentRunning !== researchAgentActive.current) {
    researchAgentActive.current = researchAgentRunning;
  }

  const { stop: stopRestaurantAgent } = useCoAgent({
    name: AvailableAgents.RESTAURANT_INTELLIGENCE,
  });

  useCopilotAction({
    name: "add_location_analysis",
    description: "Add restaurant location analysis with competitor mapping",
    parameters: [
      {
        name: "analysis",
        type: "object[]",
        description: "The location analysis data to add",
        required: true,
        attributes: [
          {
            name: "id",
            type: "string",
            description: "Unique identifier for the analysis",
          },
          {
            name: "name",
            type: "string",
            description: "Name of the analysis area",
          },
          {
            name: "center_latitude",
            type: "number",
            description: "Center latitude coordinate for the analysis area",
          },
          {
            name: "center_longitude",
            type: "number",
            description: "Center longitude coordinate for the analysis area",
          },
          {
            name: "zoom",
            type: "number",
            description: "Zoom level for the analysis area view",
          },
          {
            name: "restaurants",
            type: "object[]",
            description: "List of restaurants in the analysis area",
            attributes: [
              {
                name: "id",
                type: "string",
                description: "Unique identifier for the restaurant",
              },
              {
                name: "name",
                type: "string",
                description: "Name of the restaurant",
              },
              {
                name: "address",
                type: "string",
                description: "Full address of the restaurant",
              },
              {
                name: "latitude",
                type: "number",
                description: "Latitude coordinate of the restaurant",
              },
              {
                name: "longitude",
                type: "number",
                description: "Longitude coordinate of the restaurant",
              },
              {
                name: "rating",
                type: "number",
                description: "Rating of the restaurant (0-5)",
              },
              {
                name: "description",
                type: "string",
                description: "Brief description of the restaurant",
              },
              {
                name: "cuisine_type",
                type: "string",
                description: "Type of cuisine served",
              },
              {
                name: "price_range",
                type: "string",
                description: "Price range indicator ($, $$, $$$, $$$$)",
              },
              {
                name: "competitor_level",
                type: "string",
                description: "Competitor classification (direct, indirect, potential)",
              },
            ],
          },
        ],
      },
    ],
    renderAndWaitForResponse({ args, status, respond }) {
      if (["inProgress", "executing"].includes(status)) {
        hasInProgress.current = true;
      }

      if (status == "executing") {
        const analysis = args.analysis;
        if (!hasProcessedAnalysis.current) {
          setTimeout(() => {
            analysis.forEach((area) => {
              setRestaurants((prev) => [...prev, ...area.restaurants]);
              setCenter([area.center_latitude, area.center_longitude]);
            });
          }, 0);
          hasProcessedAnalysis.current = true;
        }
      }

      if (status === "complete") {
        hasInProgress.current = false;
      }

      return (
        <div className="p-4 bg-gray-100 rounded-md">
          {(() => {
            const isLoading = status === "inProgress";
            const isExecuting = status === "executing";
            const pending = isLoading || isExecuting;
            const colorClass = pending ? "text-blue-600" : "text-emerald-600";
            const Icon = pending ? Loader2 : CheckCircle;
            const message = isLoading
              ? "Analyzing Location..."
              : isExecuting
              ? "Please Confirm"
              : "Analysis Added Successfully!";

            return (
              <div className="flex items-start py-2 px-3">
                <span className={colorClass}>
                  <Icon
                    className={`h-4 w-4 ${pending ? "animate-spin" : ""}`}
                  />
                </span>
                <span className="ml-3 font-semibold capitalize">{message}</span>
              </div>
            );
          })()}
          <div className="p-2">
            <div
              key={`analysis-list-${args.analysis?.length ?? 0}`}
              className="space-y-6"
            >
              {args.analysis?.map((area) => (
                <div key={area.id} className="rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium text-gray-900">
                      {area.name}
                    </h3>
                    <span className="text-sm text-gray-500">
                      {area.restaurants?.length || 0} restaurants
                    </span>
                  </div>
                  <div className="flex flex-col gap-4">
                    {area.restaurants?.map((restaurant) => (
                      <div
                        key={restaurant.id}
                        className="bg-white p-4 rounded-md shadow-sm"
                      >
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-800">
                            {restaurant.name}
                          </h4>
                          {restaurant.competitor_level && (
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              restaurant.competitor_level === 'direct' ? 'bg-red-100 text-red-800' :
                              restaurant.competitor_level === 'indirect' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {restaurant.competitor_level}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {restaurant.address}
                        </p>
                        <div className="flex items-center mt-2 space-x-4">
                          <div className="flex items-center">
                            <span className="text-yellow-500">
                              {"★".repeat(Math.round(restaurant.rating))}
                            </span>
                            <span className="text-gray-400">
                              {"★".repeat(5 - Math.round(restaurant.rating))}
                            </span>
                          </div>
                          {restaurant.cuisine_type && (
                            <span className="text-sm text-gray-500">
                              {restaurant.cuisine_type}
                            </span>
                          )}
                          {restaurant.price_range && (
                            <span className="text-sm font-medium text-green-600">
                              {restaurant.price_range}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-700 mt-2">
                          {restaurant.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
          {status === "executing" ? (
            <div className="w-full flex items-center justify-between px-8 py-4">
              <Button
                variant="default"
                className="flex-1 flex items-center justify-center space-x-2 bg-green-500 hover:bg-green-600 text-white"
                onClick={() => {
                  respond?.("SEND");
                  setTimeout(() => {
                    stopRestaurantAgent();
                  }, 3000);
                }}
              >
                <CheckCircle className="h-5 w-5" />
                <span>Confirm</span>
              </Button>
              <Button
                variant="destructive"
                className="flex-1 mx-4 flex items-center justify-center space-x-2 px-4 py-2 hover:bg-red-600 focus:outline-none text-white bg-red-500"
                onClick={() => {
                  respond?.("CANCEL");
                }}
              >
                <XCircle className="h-5 w-5" />
                <span>Cancel</span>
              </Button>
            </div>
          ) : null}
          {status === "complete" ? (
            <div className="flex items-center justify-center">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span className="ml-2">Location Analysis Added Successfully!</span>
            </div>
          ) : null}
        </div>
      );
    },
    followUp: false,
  });

  useCopilotAction({
    name: "search_for_restaurants",
    parameters: [
      {
        name: "queries",
        type: "string[]",
        description: "The restaurant search queries (e.g., 'italian restaurants', 'fast food competitors')",
        required: true,
      },
    ],
    render({ args, status }) {
      /**
       * Dirty hack to mark the agent as in progress
       */
      if (!hasInProgress.current) {
        hasInProgress.current = true;
      }

      if (!args.queries) {
        return (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            <span className="ml-3">Searching for Restaurants...</span>
          </div>
        );
      }

      return (
        <div className="p-4 bg-gray-100 rounded-md">
          <h3 className="font-semibold mb-3">
            {status === "executing" || status === "inProgress"
              ? "Searching for restaurants..."
              : "Searched for restaurants"}
          </h3>
          <ul className="space-y-2 text-gray-700">
            {args.queries.map((q: string, i: number) => (
              <li key={i} className="flex items-center py-2 px-3 rounded-lg">
                {status === "executing" || status === "inProgress" ? (
                  <span className="text-blue-600">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </span>
                ) : (
                  <span className="text-emerald-600">
                    <CheckCircle className="h-4 w-4" />
                  </span>
                )}
                <span className="ml-3 font-medium capitalize">{q}</span>
              </li>
            ))}
          </ul>
        </div>
      );
    },
  });

  // Show skeleton during initial load or research
  if (hasInProgress.current) {
    return <Skeletons.MapSkeleton />;
  }

  // Return null for initial state when no restaurants are set
  if (!restaurants.length) {
    return null;
  }

  // Show map with points
  return (
    <div style={{ height: "100vh", width: "100%", position: "relative" }}>
      <Map 
        center={center} 
        zoom={13} 
        style={{ height: "100%", width: "100%" }}
        className="leaflet-container"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          className="leaflet-tile-container"
          maxZoom={19}
          minZoom={3}
          updateWhenZooming={false}
          updateWhenIdle={true}
          keepBuffer={2}
        />
        {restaurants.map((restaurant) => (
          <Marker
            key={restaurant.id}
            position={[restaurant.latitude, restaurant.longitude] as LatLngTuple}
            icon={customIcon}
          >
            <Popup>
              <div className="min-w-[200px]">
                <h3 className="font-bold text-gray-900">{restaurant.name}</h3>
                <p className="text-sm text-gray-600 mt-1">{restaurant.address}</p>
                <div className="flex items-center mt-2 space-x-2">
                  <span className="text-yellow-500">
                    {"★".repeat(Math.round(restaurant.rating))}
                  </span>
                  <span className="text-gray-400">
                    {"★".repeat(5 - Math.round(restaurant.rating))}
                  </span>
                </div>
                {restaurant.cuisine_type && (
                  <p className="text-sm text-gray-700 mt-1">
                    <strong>Cuisine:</strong> {restaurant.cuisine_type}
                  </p>
                )}
                {restaurant.price_range && (
                  <p className="text-sm text-gray-700">
                    <strong>Price:</strong> {restaurant.price_range}
                  </p>
                )}
                {restaurant.competitor_level && (
                  <span className={`inline-block text-xs px-2 py-1 rounded-full mt-2 ${
                    restaurant.competitor_level === 'direct' ? 'bg-red-100 text-red-800' :
                    restaurant.competitor_level === 'indirect' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {restaurant.competitor_level} competitor
                  </span>
                )}
                <p className="text-sm text-gray-700 mt-2">{restaurant.description}</p>
              </div>
            </Popup>
          </Marker>
        ))}
      </Map>
      {researchAgentActive.current && (
        <div className="absolute inset-0 z-[100] pointer-events-none">
          <div className="w-full h-full bg-[url('/map-overlay.png')] bg-cover bg-center bg-no-repeat" />
        </div>
      )}
    </div>
  );
}
