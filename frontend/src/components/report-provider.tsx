"use client";
import { createContext, useContext, useState, ReactNode, useMemo } from "react";
import { ReportData, ReportProgress, ReportGenerationRequest, ReportHistory, ReportType } from "@/types/reports";

interface ReportFilters {
  type?: ReportType;
  status?: 'generating' | 'completed' | 'failed';
  dateRange?: {
    start: Date;
    end: Date;
  };
  searchQuery?: string;
}

interface ReportSortOptions {
  field: 'createdAt' | 'completedAt' | 'title' | 'type' | 'analysisTime';
  direction: 'asc' | 'desc';
}

interface ReportContextType {
  reports: ReportData[];
  filteredReports: ReportData[];
  currentReport: ReportData | null;
  reportProgress: ReportProgress | null;
  isGeneratingReport: boolean;
  filters: ReportFilters;
  sortOptions: ReportSortOptions;

  // Actions
  generateReport: (request: ReportGenerationRequest) => Promise<void>;
  updateReportProgress: (progress: ReportProgress) => void;
  setCurrentReport: (report: ReportData | null) => void;
  getReportHistory: (filters?: ReportFilters) => ReportHistory;
  deleteReport: (reportId: string) => void;
  exportReport: (reportId: string, format: 'pdf' | 'json') => Promise<void>;

  // History & Filtering
  setFilters: (filters: Partial<ReportFilters>) => void;
  clearFilters: () => void;
  setSortOptions: (sortOptions: ReportSortOptions) => void;
  searchReports: (query: string) => void;
  getReportsByType: (type: ReportType) => ReportData[];
  getReportsByDateRange: (start: Date, end: Date) => ReportData[];
  getRecentReports: (limit?: number) => ReportData[];
}

const ReportContext = createContext<ReportContextType | undefined>(undefined);

export function useReports() {
  const context = useContext(ReportContext);
  if (context === undefined) {
    throw new Error("useReports must be used within a ReportProvider");
  }
  return context;
}

interface ReportProviderProps {
  children: ReactNode;
}

export function ReportProvider({ children }: ReportProviderProps) {
  const [reports, setReports] = useState<ReportData[]>([]);
  const [currentReport, setCurrentReport] = useState<ReportData | null>(null);
  const [reportProgress, setReportProgress] = useState<ReportProgress | null>(null);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [filters, setFiltersState] = useState<ReportFilters>({});
  const [sortOptions, setSortOptionsState] = useState<ReportSortOptions>({
    field: 'createdAt',
    direction: 'desc'
  });

  const generateReport = async (request: ReportGenerationRequest): Promise<void> => {
    setIsGeneratingReport(true);
    setReportProgress({
      id: `progress-${Date.now()}`,
      stage: 'initializing',
      progress: 0,
      message: 'Initializing report generation...',
      timestamp: new Date(),
    });

    try {
      // Create new report
      const newReport: ReportData = {
        id: `report-${Date.now()}`,
        title: `${request.type.replace('_', ' ')} Report`,
        type: request.type,
        status: 'generating',
        createdAt: new Date(),
        summary: '',
        content: {
          executiveSummary: '',
          keyFindings: [],
          recommendations: [],
        },
        metadata: {
          requestId: `req-${Date.now()}`,
          query: request.query,
          parameters: request.parameters,
          dataSourcesUsed: [],
          analysisTime: 0,
          confidence: 0,
          limitations: [],
        },
      };

      setReports(prev => [newReport, ...prev]);
      setCurrentReport(newReport);

      // Simulate report generation progress
      await simulateReportGeneration(newReport);
    } catch (error) {
      console.error('Report generation failed:', error);
      setReportProgress({
        id: `progress-${Date.now()}`,
        stage: 'failed',
        progress: 0,
        message: 'Report generation failed',
        timestamp: new Date(),
      });
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const simulateReportGeneration = async (report: ReportData): Promise<void> => {
    const stages = [
      { stage: 'analyzing', progress: 25, message: 'Analyzing data sources...' },
      { stage: 'generating', progress: 75, message: 'Generating insights and recommendations...' },
      { stage: 'completed', progress: 100, message: 'Report completed successfully!' },
    ] as const;

    for (const stage of stages) {
      await new Promise(resolve => setTimeout(resolve, 2000));

      setReportProgress({
        id: `progress-${Date.now()}`,
        stage: stage.stage,
        progress: stage.progress,
        message: stage.message,
        timestamp: new Date(),
      });

      if (stage.stage === 'completed') {
        // Update report with completed data
        const completedReport: ReportData = {
          ...report,
          status: 'completed',
          completedAt: new Date(),
          summary: 'Comprehensive analysis completed with actionable insights.',
          content: {
            executiveSummary: 'This report provides detailed analysis based on current market data and business intelligence.',
            keyFindings: [
              'Market opportunity identified in target area',
              'Competitive landscape analysis reveals strategic advantages',
              'Customer segmentation shows clear target demographics',
            ],
            recommendations: [
              {
                id: 'rec-1',
                priority: 'high',
                category: 'Strategy',
                title: 'Expand in identified market segment',
                description: 'Focus on the high-potential market segment identified in the analysis.',
                impact: 'High revenue potential',
                effort: 'Medium',
                timeline: '3-6 months',
              },
            ],
          },
          metadata: {
            ...report.metadata,
            analysisTime: 180,
            confidence: 0.85,
            dataSourcesUsed: ['POS Data', 'Market Research', 'Competitor Analysis'],
            limitations: ['Limited historical data available'],
          },
        };

        setReports(prev => prev.map(r => r.id === report.id ? completedReport : r));
        setCurrentReport(completedReport);
      }
    }
  };

  // Filtered and sorted reports
  const filteredReports = useMemo(() => {
    let filtered = [...reports];

    // Apply filters
    if (filters.type) {
      filtered = filtered.filter(report => report.type === filters.type);
    }

    if (filters.status) {
      filtered = filtered.filter(report => report.status === filters.status);
    }

    if (filters.dateRange) {
      filtered = filtered.filter(report => {
        const reportDate = report.createdAt;
        return reportDate >= filters.dateRange!.start && reportDate <= filters.dateRange!.end;
      });
    }

    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(report =>
        report.title.toLowerCase().includes(query) ||
        report.summary.toLowerCase().includes(query) ||
        report.content.executiveSummary.toLowerCase().includes(query) ||
        report.content.keyFindings.some(finding => finding.toLowerCase().includes(query))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortOptions.field) {
        case 'createdAt':
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
          break;
        case 'completedAt':
          aValue = a.completedAt?.getTime() || 0;
          bValue = b.completedAt?.getTime() || 0;
          break;
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'analysisTime':
          aValue = a.metadata.analysisTime;
          bValue = b.metadata.analysisTime;
          break;
        default:
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
      }

      if (sortOptions.direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [reports, filters, sortOptions]);

  const updateReportProgress = (progress: ReportProgress) => {
    setReportProgress(progress);
  };

  const getReportHistory = (appliedFilters?: ReportFilters): ReportHistory => {
    const effectiveFilters = appliedFilters || filters;
    return {
      reports: filteredReports,
      totalCount: reports.length,
      filters: effectiveFilters,
    };
  };

  // Filter and search functions
  const setFilters = (newFilters: Partial<ReportFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
  };

  const clearFilters = () => {
    setFiltersState({});
  };

  const setSortOptions = (newSortOptions: ReportSortOptions) => {
    setSortOptionsState(newSortOptions);
  };

  const searchReports = (query: string) => {
    setFilters({ searchQuery: query });
  };

  const getReportsByType = (type: ReportType): ReportData[] => {
    return reports.filter(report => report.type === type);
  };

  const getReportsByDateRange = (start: Date, end: Date): ReportData[] => {
    return reports.filter(report => {
      const reportDate = report.createdAt;
      return reportDate >= start && reportDate <= end;
    });
  };

  const getRecentReports = (limit: number = 10): ReportData[] => {
    return [...reports]
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  };

  const deleteReport = (reportId: string) => {
    setReports(prev => prev.filter(r => r.id !== reportId));
    if (currentReport?.id === reportId) {
      setCurrentReport(null);
    }
  };

  const exportReport = async (reportId: string, format: 'pdf' | 'json'): Promise<void> => {
    const report = reports.find(r => r.id === reportId);
    if (!report) {
      throw new Error('Report not found');
    }

    if (format === 'json') {
      const dataStr = JSON.stringify(report, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      const exportFileDefaultName = `${report.title.replace(/\s+/g, '_')}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    } else if (format === 'pdf') {
      // PDF export would be implemented with a PDF generation service
      console.log('PDF export not yet implemented');
    }
  };

  const value: ReportContextType = {
    reports,
    filteredReports,
    currentReport,
    reportProgress,
    isGeneratingReport,
    filters,
    sortOptions,
    generateReport,
    updateReportProgress,
    setCurrentReport,
    getReportHistory,
    deleteReport,
    exportReport,
    setFilters,
    clearFilters,
    setSortOptions,
    searchReports,
    getReportsByType,
    getReportsByDateRange,
    getRecentReports,
  };

  return (
    <ReportContext.Provider value={value}>
      {children}
    </ReportContext.Provider>
  );
}