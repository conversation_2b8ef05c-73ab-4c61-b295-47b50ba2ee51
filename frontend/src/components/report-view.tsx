"use client";
import { ReportData, Recommendation, ChartData, TableData, Appendix } from "@/types/reports";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Download,
  Calendar,
  Clock,
  Target,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Table,
  FileText,
  Info
} from "lucide-react";
import { useReports } from "./report-provider";

interface ReportViewProps {
  report: ReportData;
}

export function ReportView({ report }: ReportViewProps) {
  const { exportReport } = useReports();

  const getPriorityColor = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
    }
  };

  const getPriorityIcon = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high': return <AlertTriangle className="h-3 w-3" />;
      case 'medium': return <Target className="h-3 w-3" />;
      case 'low': return <CheckCircle className="h-3 w-3" />;
    }
  };

  const handleExport = async (format: 'pdf' | 'json') => {
    try {
      await exportReport(report.id, format);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Report Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{report.title}</h1>
            <p className="text-gray-600 mt-1">{report.summary}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => handleExport('json')}>
              <Download className="h-4 w-4 mr-2" />
              JSON
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
              <Download className="h-4 w-4 mr-2" />
              PDF
            </Button>
          </div>
        </div>

        {/* Report Metadata */}
        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
          <div className="flex items-center space-x-1">
            <Calendar className="h-4 w-4" />
            <span>Created: {report.createdAt.toLocaleDateString()}</span>
          </div>
          {report.completedAt && (
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>Completed: {report.completedAt.toLocaleDateString()}</span>
            </div>
          )}
          <div className="flex items-center space-x-1">
            <TrendingUp className="h-4 w-4" />
            <span>Analysis Time: {Math.round(report.metadata.analysisTime / 60)} minutes</span>
          </div>
          <div className="flex items-center space-x-1">
            <Target className="h-4 w-4" />
            <span>Confidence: {Math.round(report.metadata.confidence * 100)}%</span>
          </div>
        </div>

        <Badge variant="secondary" className="text-xs">
          {report.type.replace('_', ' ').toUpperCase()}
        </Badge>
      </div>

      <Separator />

      {/* Executive Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5" />
            <span>Executive Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 leading-relaxed">{report.content.executiveSummary}</p>
        </CardContent>
      </Card>

      {/* Key Findings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5" />
            <span>Key Findings</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-3">
            {report.content.keyFindings.map((finding, index) => (
              <li key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                  {index + 1}
                </div>
                <p className="text-gray-700">{finding}</p>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Recommendations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {report.content.recommendations.map((rec) => (
              <div key={rec.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge className={`${getPriorityColor(rec.priority)} flex items-center space-x-1`}>
                        {getPriorityIcon(rec.priority)}
                        <span className="capitalize">{rec.priority}</span>
                      </Badge>
                      <Badge variant="outline">{rec.category}</Badge>
                    </div>
                    <h4 className="font-semibold text-gray-900 mb-1">{rec.title}</h4>
                    <p className="text-gray-700 text-sm">{rec.description}</p>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-500">Impact:</span>
                    <p className="text-gray-700">{rec.impact}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-500">Effort:</span>
                    <p className="text-gray-700">{rec.effort}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-500">Timeline:</span>
                    <p className="text-gray-700">{rec.timeline}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      {report.content.charts && report.content.charts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Data Visualizations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {report.content.charts.map((chart) => (
                <div key={chart.id} className="border rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-4">{chart.title}</h4>
                  <div className="bg-gray-50 rounded-lg p-8 flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                      <p className="text-sm">Chart visualization ({chart.type})</p>
                      <p className="text-xs mt-1">Chart rendering would be implemented here</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tables */}
      {report.content.tables && report.content.tables.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Table className="h-5 w-5" />
              <span>Data Tables</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {report.content.tables.map((table) => (
                <div key={table.id} className="space-y-2">
                  <h4 className="font-semibold text-gray-900">{table.title}</h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full border border-gray-200 rounded-lg">
                      <thead className="bg-gray-50">
                        <tr>
                          {table.headers.map((header, index) => (
                            <th key={index} className="px-4 py-2 text-left text-sm font-medium text-gray-700 border-b">
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {table.rows.map((row, rowIndex) => (
                          <tr key={rowIndex} className="border-b border-gray-100">
                            {row.map((cell, cellIndex) => (
                              <td key={cellIndex} className="px-4 py-2 text-sm text-gray-700">
                                {cell}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Appendices */}
      {report.content.appendices && report.content.appendices.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Appendices</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {report.content.appendices.map((appendix) => (
                <div key={appendix.id} className="border-l-4 border-blue-200 pl-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="font-semibold text-gray-900">{appendix.title}</h4>
                    <Badge variant="outline" className="text-xs">
                      {appendix.type.replace('_', ' ')}
                    </Badge>
                  </div>
                  <div className="text-sm text-gray-700 whitespace-pre-wrap">
                    {appendix.content}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Report Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5" />
            <span>Report Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-500">Request ID:</span>
              <p className="text-gray-700 font-mono">{report.metadata.requestId}</p>
            </div>
            <div>
              <span className="font-medium text-gray-500">Query:</span>
              <p className="text-gray-700">{report.metadata.query}</p>
            </div>
            <div>
              <span className="font-medium text-gray-500">Data Sources:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {report.metadata.dataSourcesUsed.map((source, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {source}
                  </Badge>
                ))}
              </div>
            </div>
            <div>
              <span className="font-medium text-gray-500">Limitations:</span>
              <ul className="text-gray-700 mt-1">
                {report.metadata.limitations.map((limitation, index) => (
                  <li key={index} className="text-xs">• {limitation}</li>
                ))}
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}