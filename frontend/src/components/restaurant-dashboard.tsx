import { Log } from "@/components/coagents-provider";
import { ReportView } from "@/components/report-view";
import { AvailableAgents } from "@/lib/available-agents";
import { useCoAgent, useCoAgentStateRender } from "@copilotkit/react-core";
import { CheckCircleIcon, ChefHat } from "lucide-react";
import { FC, useEffect, useRef, useState } from "react";

export type RestaurantIntelligenceState = {
  current_report_type: string;
  location: string;
  concept_type: string;
  report_data: any;
  logs: Log[];
};

export default function RestaurantDashboard() {
  const [logs, setLogs] = useState<
    Array<{
      message: string;
      done: boolean;
    }>
  >([]);

  const isAnalysisInProgress = useRef(false);

  const { state: restaurantState, stop: stopRestaurantAgent } =
    useCoAgent<RestaurantIntelligenceState>({
      name: AvailableAgents.RESTAURANT_INTELLIGENCE,
      initialState: {
        current_report_type: "",
        location: "",
        concept_type: "",
        report_data: null,
        logs: [],
      },
    });

  useEffect(() => {
    if (restaurantState.logs) {
      setLogs((prevLogs) => {
        const newLogs = [...prevLogs];
        restaurantState.logs.forEach((log) => {
          const existingLogIndex = newLogs.findIndex(
            (l) => l.message === log.message
          );
          if (existingLogIndex >= 0) {
            if (log.done && !newLogs[existingLogIndex].done) {
              newLogs[existingLogIndex].done = true;
            }
          } else {
            newLogs.push(log);
          }
        });
        return newLogs;
      });
    }
  }, [restaurantState.logs]);

  useCoAgentStateRender({
    name: AvailableAgents.RESTAURANT_INTELLIGENCE,
    handler: ({ nodeName }) => {
      if (nodeName === "__end__") {
        setTimeout(() => {
          stopRestaurantAgent();
        }, 1000);
      }
    },
    render: ({ status }) => {
      if (status === "inProgress") {
        isAnalysisInProgress.current = true;
        return (
          <div className="flex flex-col gap-4 p-6">
            <div className="flex items-center gap-2 text-blue-600">
              <ChefHat className="h-5 w-5 animate-pulse" />
              <span>Analyzing restaurant data...</span>
            </div>
            <div className="space-y-2">
              {logs.map((log, index) => (
                <div
                  key={index}
                  className={`text-sm ${
                    log.done ? "text-green-600" : "text-gray-600"
                  }`}
                >
                  {log.done && "✓ "}
                  {log.message}
                </div>
              ))}
            </div>
          </div>
        );
      }

      if (status === "complete") {
        isAnalysisInProgress.current = false;
        return (
          <div className="p-6">
            <div className="flex items-center gap-2 text-green-600 mb-4">
              <CheckCircleIcon className="h-5 w-5" />
              <span>Analysis complete</span>
            </div>
          </div>
        );
      }
    },
  });

  if (isAnalysisInProgress.current) {
    return (
      <div className="flex flex-col gap-4 h-full">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center gap-2 text-blue-600 mb-4">
            <ChefHat className="h-6 w-6 animate-pulse" />
            <h2 className="text-xl font-semibold">
              Restaurant Intelligence Analysis
            </h2>
          </div>
          <div className="space-y-2">
            {logs.map((log, index) => (
              <div
                key={index}
                className={`text-sm ${
                  log.done ? "text-green-600" : "text-gray-600"
                }`}
              >
                {log.done && "✓ "}
                {log.message}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!restaurantState.report_data) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8">
        <ChefHat className="h-16 w-16 text-gray-400 mb-4" />
        <h2 className="text-2xl font-semibold text-gray-600 mb-2">
          BiteBase Intelligence
        </h2>
        <p className="text-gray-500 text-center max-w-md">
          Get comprehensive restaurant analytics and intelligence reports. Ask me to analyze locations, competitors, menus, sales forecasts, and more.
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 h-full">
      <ReportView reportData={restaurantState.report_data} />
    </div>
  );
}