export interface ReportProgress {
  id: string;
  stage: 'initializing' | 'analyzing' | 'generating' | 'completed' | 'failed';
  progress: number; // 0-100
  message: string;
  timestamp: Date;
}

export interface ReportData {
  id: string;
  title: string;
  type: ReportType;
  status: 'generating' | 'completed' | 'failed';
  createdAt: Date;
  completedAt?: Date;
  summary: string;
  content: ReportContent;
  metadata: ReportMetadata;
}

export type ReportType =
  | 'location_analysis'
  | 'competitor_benchmarking'
  | 'menu_optimization'
  | 'sales_forecasting'
  | 'customer_segmentation'
  | 'franchise_expansion';

export interface ReportContent {
  executiveSummary: string;
  keyFindings: string[];
  recommendations: Recommendation[];
  charts?: ChartData[];
  tables?: TableData[];
  appendices?: Appendix[];
}

export interface Recommendation {
  id: string;
  priority: 'high' | 'medium' | 'low';
  category: string;
  title: string;
  description: string;
  impact: string;
  effort: string;
  timeline: string;
}

export interface ChartData {
  id: string;
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'heatmap';
  title: string;
  data: any;
  config?: any;
}

export interface TableData {
  id: string;
  title: string;
  headers: string[];
  rows: (string | number)[][];
}

export interface Appendix {
  id: string;
  title: string;
  content: string;
  type: 'methodology' | 'data_sources' | 'calculations' | 'references';
}

export interface ReportMetadata {
  requestId: string;
  query: string;
  parameters: Record<string, any>;
  dataSourcesUsed: string[];
  analysisTime: number; // seconds
  confidence: number; // 0-1
  limitations: string[];
}

export interface ReportGenerationRequest {
  type: ReportType;
  query: string;
  parameters: Record<string, any>;
  priority?: 'normal' | 'high';
}

export interface ReportHistory {
  reports: ReportData[];
  totalCount: number;
  filters: {
    type?: ReportType;
    status?: 'generating' | 'completed' | 'failed';
    dateRange?: {
      start: Date;
      end: Date;
    };
  };
}