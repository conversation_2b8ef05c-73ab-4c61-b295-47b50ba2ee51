{"name": "bitebase-intelligence", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run  dev:agent\" \"npm run dev:copilot-api\" --names ui,agent,copilot-api --prefix-colors blue,green,yellow --kill-others", "dev:studio": "concurrently \"npm run dev:ui\" \"npm run dev:agent:studio\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:agent": "cd agent && poetry run langgraph dev --host localhost --port 8123 --no-browser", "dev:frontend": "cd frontend && npm run dev", "dev:copilot-api": "npx copilot-api@latest start gh $GITHUB_TOKEN", "build": "cd frontend && npm run build", "start": "cd frontend && npm run start", "lint": "cd frontend && npm run lint", "install:frontend": "cd frontend && npm install", "install:agent": "cd agent && poetry install", "install": "npm run install:frontend && npm run install:agent"}, "dependencies": {"concurrently": "^9.1.2"}}