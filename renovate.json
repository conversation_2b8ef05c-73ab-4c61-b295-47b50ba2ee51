{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "dependencyDashboard": true, "extends": ["config:recommended"], "poetry": {"enabled": true}, "pep621": {"enabled": false}, "npm": {"enabled": true}, "ignorePaths": ["node_modules"], "packageRules": [{"enabled": false, "matchPackageNames": ["*"], "labels": ["dependencies"]}, {"enabled": true, "matchPackageNames": ["/^@copilotkit/", "/^copilotkit/"], "labels": ["copilotkit"], "groupName": "CopilotKit dependencies"}]}