#!/bin/bash

# Copilot API Auto-Start Script
# This script automatically starts and manages the Copilot API server

set -e

COPILOT_PORT=4141
COPILOT_PID_FILE="/tmp/copilot-api.pid"

# Load environment variables from agent/.env if it exists
load_env() {
    local env_file=""
    if [ -f "agent/.env" ]; then
        env_file="agent/.env"
    elif [ -f "../agent/.env" ]; then
        env_file="../agent/.env"
    fi
    
    if [ -n "$env_file" ]; then
        # Export variables from .env file
        set -a
        . "$env_file"
        set +a
    fi
}

# Load environment variables
load_env

# Function to check if Copilot API is running
is_copilot_running() {
    if [ -f "$COPILOT_PID_FILE" ]; then
        local pid=$(cat "$COPILOT_PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$COPILOT_PID_FILE"
            return 1
        fi
    fi
    return 1
}

# Function to start Copilot API
start_copilot() {
    if is_copilot_running; then
        echo "✅ Copilot API is already running"
        return 0
    fi
    
    if [ -z "$GITHUB_TOKEN" ]; then
        echo "❌ GITHUB_TOKEN environment variable is required"
        echo "   Please set your GitHub token: export GITHUB_TOKEN=your_token_here"
        return 1
    fi
    
    echo "🤖 Starting Copilot API server on port $COPILOT_PORT..."
    
    # Start Copilot API in background and save PID
    npx copilot-api@latest start --github-token "$GITHUB_TOKEN" --port "$COPILOT_PORT" > /tmp/copilot-api.log 2>&1 &
    local pid=$!
    echo "$pid" > "$COPILOT_PID_FILE"
    
    # Wait for startup
    echo "⏳ Waiting for Copilot API to start..."
    local max_attempts=15
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s "http://localhost:$COPILOT_PORT" > /dev/null 2>&1; then
            echo "✅ Copilot API started successfully at http://localhost:$COPILOT_PORT"
            return 0
        fi
        sleep 2
        attempt=$((attempt + 1))
        echo "   Attempt $attempt/$max_attempts..."
    done
    
    echo "❌ Copilot API failed to start within 30 seconds"
    stop_copilot
    return 1
}

# Function to stop Copilot API
stop_copilot() {
    if [ -f "$COPILOT_PID_FILE" ]; then
        local pid=$(cat "$COPILOT_PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            echo "🛑 Stopping Copilot API (PID: $pid)..."
            kill "$pid" 2>/dev/null || true
        fi
        rm -f "$COPILOT_PID_FILE"
    fi
    
    # Also try to kill by process name
    pkill -f "copilot-api" 2>/dev/null || true
    echo "✅ Copilot API stopped"
}

# Function to check status
status_copilot() {
    if is_copilot_running; then
        local pid=$(cat "$COPILOT_PID_FILE")
        echo "✅ Copilot API is running (PID: $pid) at http://localhost:$COPILOT_PORT"
        
        # Test if it's responding
        if curl -s "http://localhost:$COPILOT_PORT" > /dev/null 2>&1; then
            echo "✅ Copilot API is responding to requests"
        else
            echo "⚠️  Copilot API process exists but not responding"
        fi
    else
        echo "❌ Copilot API is not running"
    fi
}

# Function to restart Copilot API
restart_copilot() {
    echo "🔄 Restarting Copilot API..."
    stop_copilot
    sleep 2
    start_copilot
}

# Function to show logs
logs_copilot() {
    if [ -f "/tmp/copilot-api.log" ]; then
        echo "📄 Copilot API logs:"
        tail -n 20 /tmp/copilot-api.log
    else
        echo "❌ No log file found"
    fi
}

# Main command handling
case "${1:-start}" in
    "start")
        start_copilot
        ;;
    "stop")
        stop_copilot
        ;;
    "restart")
        restart_copilot
        ;;
    "status")
        status_copilot
        ;;
    "logs")
        logs_copilot
        ;;
    "ensure")
        # Ensure it's running, start if not
        if ! is_copilot_running; then
            start_copilot
        else
            echo "✅ Copilot API is already running"
        fi
        ;;
    "help"|"-h"|"--help")
        echo "Copilot API Management Script"
        echo ""
        echo "Usage: $0 [COMMAND]"
        echo ""
        echo "Commands:"
        echo "  start     Start Copilot API server (default)"
        echo "  stop      Stop Copilot API server"
        echo "  restart   Restart Copilot API server"
        echo "  status    Check if Copilot API is running"
        echo "  logs      Show recent logs"
        echo "  ensure    Ensure Copilot API is running (start if not)"
        echo "  help      Show this help message"
        echo ""
        echo "Environment Variables:"
        echo "  GITHUB_TOKEN    Required GitHub token for Copilot API"
        echo ""
        echo "Examples:"
        echo "  export GITHUB_TOKEN=your_token_here"
        echo "  $0 start"
        echo "  $0 status"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
