#!/bin/bash

# Open Multi-Agent Canvas Setup Script
# This script helps you configure the project with either Copilot API or OpenAI

set -e

echo "🤖 Open Multi-Agent Canvas Setup"
echo "================================="
echo ""
echo "This project supports two backend options:"
echo "1. GitHub Copilot API (claude-sonnet-4 via Copilot)"
echo "2. Traditional OpenAI API (gpt-4, gpt-3.5-turbo, etc.)"
echo ""

# Check if already configured
if [ -f "agent/.env" ]; then
    echo "✅ Found existing configuration in agent/.env"
    echo ""
    echo "Current configuration:"
    if grep -q "^OPENAI_BASE_URL=http://localhost:4141" agent/.env; then
        echo "  Backend: Copilot API (claude-sonnet-4)"
    elif grep -q "^OPENAI_BASE_URL=https://api.openai.com" agent/.env; then
        echo "  Backend: Traditional OpenAI"
    else
        echo "  Backend: Custom configuration"
    fi
    echo ""
    read -p "Do you want to reconfigure? (y/N): " reconfigure
    if [[ ! "$reconfigure" =~ ^[Yy]$ ]]; then
        echo "✅ Using existing configuration. Run 'make run' to start!"
        exit 0
    fi
fi

echo "Choose your backend:"
echo "1) GitHub Copilot API (Recommended - uses claude-sonnet-4)"
echo "2) Traditional OpenAI API"
echo ""
read -p "Select option (1 or 2): " backend_choice

case $backend_choice in
    1)
        echo ""
        echo "🤖 Setting up GitHub Copilot API..."
        echo ""
        echo "Requirements:"
        echo "- GitHub account with Copilot access"
        echo "- GitHub Personal Access Token"
        echo ""
        
        if [ -z "$GITHUB_TOKEN" ]; then
            echo "Please create a GitHub Personal Access Token:"
            echo "1. Go to: https://github.com/settings/tokens"
            echo "2. Click 'Generate new token (classic)'"
            echo "3. Select these scopes: repo, read:user, user:email"
            echo "4. Copy the token"
            echo ""
            read -p "Enter your GitHub token: " github_token
            
            if [ -z "$github_token" ]; then
                echo "❌ GitHub token is required for Copilot API"
                exit 1
            fi
            
            # Update the .env file with the token
            if [ -f "agent/.env" ]; then
                sed -i.bak "s/^GITHUB_TOKEN=.*/GITHUB_TOKEN=$github_token/" agent/.env
            else
                echo "GITHUB_TOKEN=$github_token" >> agent/.env
            fi
        else
            echo "✅ Using GITHUB_TOKEN from environment"
        fi
        
        echo ""
        echo "🔧 Configuring for Copilot API..."
        make setup-copilot-config
        
        echo ""
        echo "✅ Copilot API configuration complete!"
        echo ""
        echo "To start the system:"
        echo "  make run          # Start both frontend and agent"
        echo "  make start-all    # Alternative command"
        echo ""
        ;;
        
    2)
        echo ""
        echo "🔧 Setting up Traditional OpenAI API..."
        echo ""
        echo "Requirements:"
        echo "- OpenAI account (https://platform.openai.com/)"
        echo "- OpenAI API key"
        echo ""
        
        read -p "Enter your OpenAI API key: " openai_key
        
        if [ -z "$openai_key" ]; then
            echo "❌ OpenAI API key is required"
            exit 1
        fi
        
        echo ""
        echo "🔧 Configuring for OpenAI API..."
        make setup-openai-config
        
        # Update the .env file with the API key
        sed -i.bak "s/^OPENAI_API_KEY=your_openai_api_key_here/OPENAI_API_KEY=$openai_key/" agent/.env
        
        echo ""
        echo "✅ OpenAI configuration complete!"
        echo ""
        echo "To start the system:"
        echo "  make run          # Start both frontend and agent"
        echo "  make start-all    # Alternative command"
        echo ""
        ;;
        
    *)
        echo "❌ Invalid selection. Please run the script again."
        exit 1
        ;;
esac

echo "🚀 Setup complete! Next steps:"
echo ""
echo "1. Install dependencies:"
echo "   make install"
echo ""
echo "2. Start the system:"
echo "   make run"
echo ""
echo "3. Open your browser to:"
echo "   Frontend: http://localhost:3000"
echo "   Agent API: http://localhost:8000"
echo ""
echo "For more commands, run: make help"
