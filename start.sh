#!/bin/bash

# Open Multi-Agent Canvas Startup Script
# This script starts the entire project with Copilot API as default backend

set -e

echo "🚀 Open Multi-Agent Canvas - Copilot API Startup"
echo "================================================"

# Configuration
COPILOT_PORT=4141
FRONTEND_PORT=3000
AGENT_PORT=8123

# Function to check if required tools are installed
check_dependencies() {
    echo "🔍 Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v node &> /dev/null; then
        missing_deps+=("node")
    fi
    
    if ! command -v pnpm &> /dev/null; then
        missing_deps+=("pnpm")
    fi
    
    if ! command -v python3 &> /dev/null; then
        missing_deps+=("python3")
    fi
    
    if ! command -v poetry &> /dev/null; then
        missing_deps+=("poetry")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo "❌ Missing dependencies: ${missing_deps[*]}"
        echo "   Please install the missing dependencies and try again"
        exit 1
    fi
    
    echo "✅ All dependencies found"
}

# Function to check environment
check_environment() {
    echo "🔧 Checking environment configuration..."
    
    if [ -z "$GITHUB_TOKEN" ]; then
        echo "❌ GITHUB_TOKEN environment variable is required for Copilot API"
        echo "   Please set your GitHub token:"
        echo "   export GITHUB_TOKEN=your_github_token_here"
        echo ""
        echo "   Get a token from: https://github.com/settings/tokens"
        exit 1
    fi
    
    echo "✅ GITHUB_TOKEN is set"
    
    # Check if environment files exist
    if [ ! -f "frontend/.env" ] || [ ! -f "agent/.env" ]; then
        echo "📄 Environment files not found. Creating them..."
        make setup-env
    fi
    
    echo "✅ Environment files ready"
}

# Function to start Copilot API
start_copilot_api() {
    echo "🤖 Starting Copilot API..."
    ./scripts/copilot-api.sh ensure
}

# Function to start frontend in background
start_frontend() {
    echo "🌐 Starting frontend in development mode..."
    cd frontend
    
    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        echo "📦 Installing frontend dependencies..."
        pnpm install
    fi
    
    # Start in background
    pnpm run dev > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo "$FRONTEND_PID" > ../logs/frontend.pid
    
    cd ..
    echo "✅ Frontend started (PID: $FRONTEND_PID)"
    echo "🌐 Frontend available at: http://localhost:$FRONTEND_PORT"
}

# Function to start agent backend
start_agent() {
    echo "🤖 Starting agent backend with Copilot API..."
    cd agent
    
    # Check if dependencies are installed
    if [ ! -d ".venv" ]; then
        echo "📦 Installing agent dependencies..."
        poetry install
    fi
    
    # Set Copilot API environment variables
    export OPENAI_API_KEY="dummy"
    export OPENAI_MODEL="claude-sonnet-4"
    export OPENAI_BASE_URL="http://localhost:$COPILOT_PORT"
    
    # Start agent
    ./run.sh &
    AGENT_PID=$!
    echo "$AGENT_PID" > ../logs/agent.pid
    
    cd ..
    echo "✅ Agent started (PID: $AGENT_PID)"
    echo "🤖 Agent available at: http://localhost:$AGENT_PORT"
}

# Function to show status
show_status() {
    echo ""
    echo "📊 Service Status:"
    echo "=================="
    
    # Copilot API
    if curl -s "http://localhost:$COPILOT_PORT" > /dev/null 2>&1; then
        echo "✅ Copilot API: Running (http://localhost:$COPILOT_PORT)"
    else
        echo "❌ Copilot API: Not responding"
    fi
    
    # Frontend
    if curl -s "http://localhost:$FRONTEND_PORT" > /dev/null 2>&1; then
        echo "✅ Frontend: Running (http://localhost:$FRONTEND_PORT)"
    else
        echo "❌ Frontend: Not responding"
    fi
    
    # Agent
    if curl -s "http://localhost:$AGENT_PORT" > /dev/null 2>&1; then
        echo "✅ Agent: Running (http://localhost:$AGENT_PORT)"
    else
        echo "❌ Agent: Not responding"
    fi
}

# Function to stop all services
stop_services() {
    echo ""
    echo "🛑 Stopping all services..."
    
    # Stop frontend
    if [ -f "logs/frontend.pid" ]; then
        local pid=$(cat logs/frontend.pid)
        kill "$pid" 2>/dev/null || true
        rm -f logs/frontend.pid
        echo "✅ Frontend stopped"
    fi
    
    # Stop agent
    if [ -f "logs/agent.pid" ]; then
        local pid=$(cat logs/agent.pid)
        kill "$pid" 2>/dev/null || true
        rm -f logs/agent.pid
        echo "✅ Agent stopped"
    fi
    
    # Stop Copilot API
    ./scripts/copilot-api.sh stop
    
    echo "✅ All services stopped"
}

# Function to show help
show_help() {
    echo "Open Multi-Agent Canvas Startup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start all services with Copilot API (default)"
    echo "  stop      Stop all services"
    echo "  status    Show service status"
    echo "  restart   Restart all services"
    echo "  logs      Show service logs"
    echo "  help      Show this help message"
    echo ""
    echo "Environment:"
    echo "  GITHUB_TOKEN    Required for Copilot API"
    echo ""
    echo "Services:"
    echo "  Copilot API:    http://localhost:$COPILOT_PORT"
    echo "  Frontend:       http://localhost:$FRONTEND_PORT"
    echo "  Agent:          http://localhost:$AGENT_PORT"
}

# Create logs directory
mkdir -p logs

# Trap to cleanup on exit
trap stop_services EXIT INT TERM

# Main command handling
case "${1:-start}" in
    "start")
        check_dependencies
        check_environment
        start_copilot_api
        start_frontend
        start_agent
        
        echo ""
        echo "🎉 All services started successfully!"
        echo ""
        echo "🌐 Frontend: http://localhost:$FRONTEND_PORT"
        echo "🤖 Agent: http://localhost:$AGENT_PORT"
        echo "🔧 Copilot API: http://localhost:$COPILOT_PORT"
        echo ""
        echo "Press Ctrl+C to stop all services"
        
        # Wait for user to stop
        while true; do
            sleep 10
            # Check if services are still running
            if ! curl -s "http://localhost:$FRONTEND_PORT" > /dev/null 2>&1 || \
               ! curl -s "http://localhost:$AGENT_PORT" > /dev/null 2>&1 || \
               ! curl -s "http://localhost:$COPILOT_PORT" > /dev/null 2>&1; then
                echo "⚠️  One or more services stopped unexpectedly"
                show_status
                break
            fi
        done
        ;;
    "stop")
        stop_services
        ;;
    "status")
        show_status
        ;;
    "restart")
        stop_services
        sleep 2
        exec "$0" start
        ;;
    "logs")
        echo "📄 Service Logs:"
        echo "================"
        if [ -f "logs/frontend.log" ]; then
            echo ""
            echo "Frontend logs (last 10 lines):"
            tail -n 10 logs/frontend.log
        fi
        if [ -f "logs/agent.log" ]; then
            echo ""
            echo "Agent logs (last 10 lines):"
            tail -n 10 logs/agent.log
        fi
        echo ""
        echo "Copilot API logs:"
        ./scripts/copilot-api.sh logs
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
